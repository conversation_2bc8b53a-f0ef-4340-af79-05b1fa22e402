﻿msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: lclstrconsts.hhshelpbrowsernotexecutable
msgid "Browser %s%s%s not executable."
msgstr "浏览器 %s%s%s 没有执行"

#: lclstrconsts.hhshelpbrowsernotfound
msgid "Browser %s%s%s not found."
msgstr "浏览器 %s%s%s 没有发现"

#: lclstrconsts.hhshelperrorwhileexecuting
msgid "Error while executing %s%s%s:%s%s"
msgstr "当执行时发生错误 %s%s%s:%s%s"

#: lclstrconsts.hhshelpnohtmlbrowserfound
msgid "Unable to find a HTML browser."
msgstr "无法找到一个HTML浏览器."

#: lclstrconsts.hhshelpnohtmlbrowserfoundpleasedefineoneinhelpconfigurehe
msgid "No HTML Browser found.%sPlease define one in Environment -> Options -> Help -> Help Options"
msgstr "没有发现HTML浏览器.%s请定义一个 环境 ->选项->帮助->帮助选项"

#: lclstrconsts.hhshelpthehelpdatabasewasunabletofindfile
msgid "The help database %s%s%s was unable to find file %s%s%s."
msgstr "帮助数据库 %s%s%s 无法找到文件 %s%s%s."

#: lclstrconsts.hhshelpthemacrosinbrowserparamswillbereplacedbytheurl
msgid "The macro %s in BrowserParams will be replaced by the URL."
msgstr "宏 %s 在浏览器中的URL参数将被取代"

#: lclstrconsts.ifsalt
msgid "Alt"
msgstr ""

#: lclstrconsts.ifsctrl
msgid "Ctrl"
msgstr ""

#: lclstrconsts.ifsvk_accept
msgid "Accept"
msgstr "接受"

#: lclstrconsts.ifsvk_apps
msgid "application key"
msgstr "Windows键"

#: lclstrconsts.ifsvk_back
msgid "Backspace"
msgstr ""

#: lclstrconsts.ifsvk_cancel
msgctxt "lclstrconsts.ifsvk_cancel"
msgid "Cancel"
msgstr "取消"

#: lclstrconsts.ifsvk_capital
msgid "Capital"
msgstr ""

#: lclstrconsts.ifsvk_clear
msgid "Clear"
msgstr "清除"

#: lclstrconsts.ifsvk_control
msgid "Control"
msgstr ""

#: lclstrconsts.ifsvk_convert
msgid "Convert"
msgstr "转换"

#: lclstrconsts.ifsvk_delete
msgctxt "lclstrconsts.ifsvk_delete"
msgid "Delete"
msgstr ""

#: lclstrconsts.ifsvk_down
msgctxt "lclstrconsts.ifsvk_down"
msgid "Down"
msgstr ""

#: lclstrconsts.ifsvk_end
msgctxt "lclstrconsts.ifsvk_end"
msgid "End"
msgstr ""

#: lclstrconsts.ifsvk_escape
msgid "Escape"
msgstr ""

#: lclstrconsts.ifsvk_execute
msgid "Execute"
msgstr "执行"

#: lclstrconsts.ifsvk_final
msgid "Final"
msgstr "最终"

#: lclstrconsts.ifsvk_hanja
msgid "Hanja"
msgstr ""

#: lclstrconsts.ifsvk_help
msgid "Help"
msgstr "帮助"

#: lclstrconsts.ifsvk_home
msgctxt "lclstrconsts.ifsvk_home"
msgid "Home"
msgstr ""

#: lclstrconsts.ifsvk_insert
msgctxt "lclstrconsts.ifsvk_insert"
msgid "Insert"
msgstr ""

#: lclstrconsts.ifsvk_junja
msgid "Junja"
msgstr ""

#: lclstrconsts.ifsvk_kana
msgid "Kana"
msgstr ""

#: lclstrconsts.ifsvk_lbutton
msgid "Mouse Button Left"
msgstr ""

#: lclstrconsts.ifsvk_left
msgctxt "lclstrconsts.ifsvk_left"
msgid "Left"
msgstr ""

#: lclstrconsts.ifsvk_lwin
msgid "left windows key"
msgstr ""

#: lclstrconsts.ifsvk_mbutton
msgid "Mouse Button Middle"
msgstr ""

#: lclstrconsts.ifsvk_menu
msgctxt "lclstrconsts.ifsvk_menu"
msgid "Menu"
msgstr "菜单"

#: lclstrconsts.ifsvk_modechange
msgid "Mode Change"
msgstr "模式变化"

#: lclstrconsts.ifsvk_next
msgctxt "lclstrconsts.ifsvk_next"
msgid "Next"
msgstr "下一个"

#: lclstrconsts.ifsvk_nonconvert
msgid "Nonconvert"
msgstr "非转换"

#: lclstrconsts.ifsvk_numlock
msgid "Numlock"
msgstr ""

#: lclstrconsts.ifsvk_numpad
msgid "Numpad %d"
msgstr ""

#: lclstrconsts.ifsvk_pause
msgid "Pause key"
msgstr ""

#: lclstrconsts.ifsvk_print
msgid "Print"
msgstr ""

#: lclstrconsts.ifsvk_prior
msgctxt "lclstrconsts.ifsvk_prior"
msgid "Prior"
msgstr ""

#: lclstrconsts.ifsvk_rbutton
msgid "Mouse Button Right"
msgstr ""

#: lclstrconsts.ifsvk_return
msgid "Return"
msgstr "返回"

#: lclstrconsts.ifsvk_right
msgctxt "lclstrconsts.ifsvk_right"
msgid "Right"
msgstr ""

#: lclstrconsts.ifsvk_rwin
msgid "right windows key"
msgstr ""

#: lclstrconsts.ifsvk_scroll
msgid "Scroll"
msgstr ""

#: lclstrconsts.ifsvk_select
msgid "Select"
msgstr "选择"

#: lclstrconsts.ifsvk_shift
msgid "Shift"
msgstr ""

#: lclstrconsts.ifsvk_snapshot
msgid "Snapshot"
msgstr "截屏"

#: lclstrconsts.ifsvk_space
msgid "Space key"
msgstr ""

#: lclstrconsts.ifsvk_tab
msgctxt "lclstrconsts.ifsvk_tab"
msgid "Tab"
msgstr ""

#: lclstrconsts.ifsvk_unknown
msgid "Unknown"
msgstr ""

#: lclstrconsts.ifsvk_up
msgctxt "lclstrconsts.ifsvk_up"
msgid "Up"
msgstr ""

#: lclstrconsts.liscannotexecute
msgid "can not execute %s"
msgstr "无法执行 %s"

#: lclstrconsts.lislclresourcesnotfound
msgctxt "lclstrconsts.lislclresourcesnotfound"
msgid "Resource %s not found"
msgstr "资源 %s 没有找到"

#: lclstrconsts.lisprogramfilenotfound
msgid "program file not found %s"
msgstr "找不到程序文件 %s"

#: lclstrconsts.rs3ddkshadowcolorcaption
msgid "3D Dark Shadow"
msgstr "3D阴影"

#: lclstrconsts.rs3dlightcolorcaption
msgid "3D Light"
msgstr "3D光线"

#: lclstrconsts.rsacontrolcannothaveitselfasparent
msgid "A control can't have itself as a parent"
msgstr "一个不受父级的自控程序"

#: lclstrconsts.rsactivebordercolorcaption
msgid "Active Border"
msgstr "活动边框"

#: lclstrconsts.rsactivecaptioncolorcaption
msgid "Active Caption"
msgstr "活动标题"

#: lclstrconsts.rsallfiles
msgid "All files (%s)|%s|%s"
msgstr "所有文件 (%s)|%s|%s"

#: lclstrconsts.rsappworkspacecolorcaption
msgid "Application Workspace"
msgstr "应用程序工作区"

#: lclstrconsts.rsaquacolorcaption
msgid "Aqua"
msgstr "浅绿色"

#: lclstrconsts.rsbackgroundcolorcaption
msgid "Desktop"
msgstr "桌面"

#: lclstrconsts.rsbackward
msgid "Backward"
msgstr "撤回"

#: lclstrconsts.rsbitmaps
msgid "Bitmaps"
msgstr "位图"

#: lclstrconsts.rsblackcolorcaption
msgid "Black"
msgstr "黑色"

#: lclstrconsts.rsblank
msgid "Blank"
msgstr "空白"

#: lclstrconsts.rsbluecolorcaption
msgid "Blue"
msgstr "蓝色"

#: lclstrconsts.rsbtnfacecolorcaption
msgid "Button Face"
msgstr "按钮表面"

#: lclstrconsts.rsbtnhighlightcolorcaption
msgid "Button Highlight"
msgstr "按钮突出"

#: lclstrconsts.rsbtnshadowcolorcaption
msgid "Button Shadow"
msgstr "按钮阴影"

#: lclstrconsts.rsbtntextcolorcaption
msgid "Button Text"
msgstr "按钮文本"

#: lclstrconsts.rscalculator
msgid "Calculator"
msgstr "计算器"

#: lclstrconsts.rscancelrecordhint
msgctxt "lclstrconsts.rscancelrecordhint"
msgid "Cancel"
msgstr "取消"

#: lclstrconsts.rscannotfocus
msgid "Can not focus"
msgstr "无法集中"

#: lclstrconsts.rscanvasdoesnotallowdrawing
msgid "Canvas does not allow drawing"
msgstr "禁止画图"

#: lclstrconsts.rscaptiontextcolorcaption
msgid "Caption Text"
msgstr "标题文本"

#: lclstrconsts.rscasesensitive
msgid "Case sensitive"
msgstr "区分大小写"

#: lclstrconsts.rscontrolclasscantcontainchildclass
msgid "Control of class '%s' can't have control of class '%s' as a child"
msgstr "控制类 '%s' 非控制类 '%s' 子条目"

#: lclstrconsts.rscontrolhasnoparentwindow
msgid "Control '%s' has no parent window"
msgstr "控制 '%s' 没有父窗口"

#: lclstrconsts.rscreamcolorcaption
msgid "Cream"
msgstr ""

#: lclstrconsts.rscreatinggdbcatchableerror
msgid "Creating gdb catchable error:"
msgstr "创建gdb明显错误:"

#: lclstrconsts.rscursor
msgid "Cursor"
msgstr "光标"

#: lclstrconsts.rscustomcolorcaption
msgid "Custom ..."
msgstr "自定义"

#: lclstrconsts.rsdefaultcolorcaption
msgid "Default"
msgstr "默认"

#: lclstrconsts.rsdefaultfileinfovalue
msgid "permissions user group size date time"
msgstr "用户组权限的大小 日期 时间"

#: lclstrconsts.rsdeleterecord
msgid "Delete record?"
msgstr "删除记录?"

#: lclstrconsts.rsdeleterecordhint
msgctxt "lclstrconsts.rsdeleterecordhint"
msgid "Delete"
msgstr "删除"

#: lclstrconsts.rsdirection
msgid "Direction"
msgstr "使用说明"

#: lclstrconsts.rsdirectory
msgid "&Directory"
msgstr "&目录"

#: lclstrconsts.rsdocking
msgid "Docking"
msgstr "接口"

#: lclstrconsts.rsduplicateiconformat
msgid "Duplicate icon format."
msgstr "重复的图标格式"

#: lclstrconsts.rseditrecordhint
msgid "Edit"
msgstr "编辑"

#: lclstrconsts.rsentirescope
msgid "Search entire file"
msgstr "搜索整个文件"

#: lclstrconsts.rserror
msgctxt "lclstrconsts.rserror"
msgid "Error"
msgstr "错误"

#: lclstrconsts.rserrorcreatingdevicecontext
msgid "Error creating device context for %s.%s"
msgstr "创建设备上下文错误 %s.%s""

#: lclstrconsts.rserrorinlcl
msgid "ERROR in LCL: "
msgstr "错误的LCL:"

#: lclstrconsts.rserroroccurredinataddressframe
msgid "Error occurred in %s at %sAddress %s%s Frame %s"
msgstr "错误发生 %s 在 %s地址 %s%s 帧 %s"

#: lclstrconsts.rserrorreadingproperty
msgid "Error reading %s%s%s: %s"
msgstr "读取错误 %s%s%s: %s"

#: lclstrconsts.rserrorwhilesavingbitmap
msgid "Error while saving bitmap."
msgstr "保存位图错误."

#: lclstrconsts.rsexception
msgid "Exception"
msgstr "异常"

#: lclstrconsts.rsfddirectorymustexist
msgid "Directory must exist"
msgstr "目录不存在"

#: lclstrconsts.rsfddirectorynotexist
msgid "The directory \"%s\" does not exist."
msgstr "T目录 \"%s\" 不存在."

#: lclstrconsts.rsfdfilealreadyexists
msgid "The file \"%s\" already exists. Overwrite ?"
msgstr "文件 \"%s\" 已存在. 覆盖吗 ?"

#: lclstrconsts.rsfdfilemustexist
msgid "File must exist"
msgstr "文件不存在"

#: lclstrconsts.rsfdfilenotexist
msgid "文件 \"%s\" 不存在."
msgstr ""

#: lclstrconsts.rsfdfilereadonly
msgid "The file \"%s\" is not writable."
msgstr "文件 \"%s\" 不可写."

#: lclstrconsts.rsfdfilereadonlytitle
msgid "File is not writable"
msgstr "文件不能写入"

#: lclstrconsts.rsfdfilesaveas
msgid "Save file as"
msgstr "保存文件"

#: lclstrconsts.rsfdopenfile
msgid "Open existing file"
msgstr "打开文件"

#: lclstrconsts.rsfdoverwritefile
msgid "Overwrite file ?"
msgstr "是否覆盖文件?"

#: lclstrconsts.rsfdpathmustexist
msgid "Path must exist"
msgstr "路径不存在"

#: lclstrconsts.rsfdpathnoexist
msgid "The path \"%s\" does not exist."
msgstr "路径 \"%s\" 不存在."

#: lclstrconsts.rsfdselectdirectory
msgid "Select Directory"
msgstr "选择目录"

#: lclstrconsts.rsfileinfofilenotfound
msgid "(file not found: \"%s\")"
msgstr "(未找到文件: \"%s\")"

#: lclstrconsts.rsfileinformation
msgid "File information"
msgstr "文件信息"

#: lclstrconsts.rsfind
msgid "Find"
msgstr "查找"

#: lclstrconsts.rsfindmore
msgid "Find more"
msgstr "查找更多"

#: lclstrconsts.rsfirstrecordhint
msgid "First"
msgstr "首先"

#: lclstrconsts.rsfixedcolstoobig
msgid "FixedCols can't be >= ColCount"
msgstr ""

#: lclstrconsts.rsfixedrowstoobig
msgid "FixedRows can't be >= RowCount"
msgstr ""

#: lclstrconsts.rsformcolorcaption
msgid "Form"
msgstr "形式"

#: lclstrconsts.rsformstreamingerror
msgid "Form streaming \"%s\" error: %s"
msgstr "形式流 \"%s\" 错误: %s"

#: lclstrconsts.rsforward
msgid "Forward"
msgstr "向前"

#: lclstrconsts.rsfuchsiacolorcaption
msgid "Fuchsia"
msgstr ""

#: lclstrconsts.rsgdkoptiondebug
msgid "--gdk-debug flags     Turn on specific GDK trace/debug messages."
msgstr "--gdk调试标志     打开特定 GDK 追踪/调试信息."

#: lclstrconsts.rsgdkoptionnodebug
msgid "--gdk-no-debug flags  Turn off specific GDK trace/debug messages."
msgstr "--gdk-no-debug flags  关闭特定 GDK 跟踪/调试信息."

#: lclstrconsts.rsgif
msgid "Graphics Interchange Format"
msgstr "图形交换格式"

#: lclstrconsts.rsgoptionfatalwarnings
msgid "--g-fatal-warnings    Warnings and errors generated by Gtk+/GDK will halt the application."
msgstr "--g-fatal-warnings   由 Gtk+/GDK生成的警告和错误将停止应用程序."

#: lclstrconsts.rsgradientactivecaptioncolorcaption
msgid "Gradient Active Caption"
msgstr "逐步激活标题"

#: lclstrconsts.rsgradientinactivecaptioncolorcaption
msgid "Gradient Inactive Caption"
msgstr "逐步闲置标题"

#: lclstrconsts.rsgraphic
msgid "Graphic"
msgstr "图形"

#: lclstrconsts.rsgraycolorcaption
msgid "Gray"
msgstr "灰色"

#: lclstrconsts.rsgraytextcolorcaption
msgid "Gray Text"
msgstr "灰色文本"

#: lclstrconsts.rsgreencolorcaption
msgid "Green"
msgstr "绿色"

#: lclstrconsts.rsgridfiledoesnotexists
msgid "Grid file doesn't exists"
msgstr "光栅文件不从在"

#: lclstrconsts.rsgridindexoutofrange
msgid "Grid index out of range."
msgstr "光栅索引超出范围"

#: lclstrconsts.rsgroupindexcannotbelessthanprevious
msgid "GroupIndex cannot be less than a previous menu item's GroupIndex"
msgstr "组索引不能少于上一个菜单的组索引"

#: lclstrconsts.rsgtkfilter
msgid "Filter:"
msgstr "过滤器"

#: lclstrconsts.rsgtkhistory
msgid "History:"
msgstr "历史记录"

#: lclstrconsts.rsgtkoptionclass
msgid "--class classname     Following Xt conventions, the class of a program is the program name with the initial character capitalized. For example, the classname for gimp is \"Gimp\". If --class is specified, the class of the program will be set to \"classname\"."
msgstr "--class classname     Xt约定,程序的类名第一个字符大写。例如,gimp的名字是\"gimp\".如果指定—类,类的程序名将被设置为\"classname\"."

#: lclstrconsts.rsgtkoptiondebug
msgid "--gtk-debug flags     Turn on specific Gtk+ trace/debug messages."
msgstr "--gtk-debug flags    打开特定的Gtk+ 跟踪/调试消息."

#: lclstrconsts.rsgtkoptiondisplay
msgid "--display h:s:d       Connect to the specified X server, where \"h\" is the hostname, \"s\" is the server number (usually 0), and \"d\" is the display number (typically omitted). If --display is not specified, the DISPLAY environment variable is used."
msgstr "--display h:s:d       连接到指定的X服务器,  \"h\" 是主机名, \"s\" 是服务器数量 (通常是 0),  \"d\" 是显示号码 (通常省略).如果 --显示未指定显示环境变量."

#: lclstrconsts.rsgtkoptionmodule
msgid "--gtk-module module   Load the specified module at startup."
msgstr "--gtk-module module   在启动时加载指定的模块."

#: lclstrconsts.rsgtkoptionname
msgid "--name programe       Set program name to \"progname\". If not specified, program name will be set to ParamStrUTF8(0)."
msgstr "--name programe       设置程序名 \"程序名\". 如果未指定, 程序名称将被设置为 ParamStrUTF8(0)."

#: lclstrconsts.rsgtkoptionnodebug
msgid "--gtk-no-debug flags  Turn off specific Gtk+ trace/debug messages."
msgstr "--gtk-no-debug flags  关闭特定Gtk+ 追踪/调试信息."

#: lclstrconsts.rsgtkoptionnotransient
msgid "--lcl-no-transient    Do not set transient order for modal forms"
msgstr "--lcl-no-transient    不设置瞬态模态形式"

#: lclstrconsts.rsgtkoptionnoxshm
msgid "--no-xshm             Disable use of the X Shared Memory Extension."
msgstr "--no-xshm             禁止使用X共享内存扩展."

#: lclstrconsts.rsgtkoptionsync
msgid "--sync                Call XSynchronize (display, True) after the Xserver connection has been established. This makes debugging X protocol errors easier, because X request buffering will be disabled and X errors will be received immediately after the protocol request that generated the error has been processed by the X server."
msgstr "--sync                呼叫xsynchronize(显示,真)后，X服务器连接已建立.这使得调试X协议错误更容易,因为X请求缓冲将被禁用和X误差会收到后立即产生错误的协议要求已经由X服务器处理"

#: lclstrconsts.rshelpalreadyregistered
msgid "%s: Already registered"
msgstr "%s: 已经注册的"

#: lclstrconsts.rshelpcontextnotfound
msgid "Help Context not found"
msgstr "帮助上下文没有发现"

#: lclstrconsts.rshelpdatabasenotfound
msgid "Help Database not found"
msgstr "帮助数据库没有找到"

#: lclstrconsts.rshelperror
msgid "Help Error"
msgstr "帮助错误"

#: lclstrconsts.rshelphelpcontextnotfound
msgid "Help context %s not found."
msgstr "帮助上下文 %s 没有找到."

#: lclstrconsts.rshelphelpcontextnotfoundindatabase
msgid "Help context %s not found in Database %s%s%s."
msgstr "帮助上下文%s 在数据库内没有找到 %s%s%s."

#: lclstrconsts.rshelphelpdatabasedidnotfoundaviewerforahelppageoftype
msgid "Help Database %s%s%s did not found a viewer for a help page of type %s"
msgstr "帮助数据库 %s%s%s 没有发现读者的帮助页面类型 %"

#: lclstrconsts.rshelphelpdatabasenotfound
msgid "Help Database %s%s%s not found"
msgstr "帮助数据库 %s%s%s 没有找到"

#: lclstrconsts.rshelphelpkeywordnotfound
msgid "Help keyword %s%s%s not found."
msgstr "帮助关键字 %s%s%s 没有找到"

#: lclstrconsts.rshelphelpkeywordnotfoundindatabase
msgid "Help keyword %s%s%s not found in Database %s%s%s."
msgstr "帮助关键字 %s%s%s 在数据库没有找到 %s%s%s."

#: lclstrconsts.rshelphelpnodehasnohelpdatabase
msgid "Help node %s%s%s has no Help Database"
msgstr "帮助节点 %s%s%s 没有帮助数据库"

#: lclstrconsts.rshelpnohelpfoundforsource
msgid "No help found for line %d, column %d of %s."
msgstr "没有发现行帮助 %d, 列 %d 的 %s."

#: lclstrconsts.rshelpnohelpnodesavailable
msgid "No help nodes available"
msgstr "没有可用的帮助节点"

#: lclstrconsts.rshelpnotfound
msgid "Help not found"
msgstr "帮助没有找到"

#: lclstrconsts.rshelpnotregistered
msgid "%s: Not registered"
msgstr "%s: 未注册"

#: lclstrconsts.rshelpselectorerror
msgid "Help Selector Error"
msgstr "帮助选择错误"

#: lclstrconsts.rshelpthereisnoviewerforhelptype
msgid "There is no viewer for help type %s%s%s"
msgstr "没有浏览器的帮助类型 %s%s%s"

#: lclstrconsts.rshelpviewererror
msgid "Help Viewer Error"
msgstr "帮助查看器错误"

#: lclstrconsts.rshelpviewernotfound
msgid "Help Viewer not found"
msgstr "帮助查看器未找到"

#: lclstrconsts.rshighlightcolorcaption
msgid "Highlight"
msgstr "高亮"

#: lclstrconsts.rshighlighttextcolorcaption
msgid "Highlight Text"
msgstr "高亮显示文本"

#: lclstrconsts.rshotlightcolorcaption
msgid "Hot Light"
msgstr "热光"

#: lclstrconsts.rsicns
msgid "Mac OS X Icon"
msgstr "Mac OS X图标"

#: lclstrconsts.rsicon
msgid "Icon"
msgstr "图标"

#: lclstrconsts.rsiconimageempty
msgid "Icon image cannot be empty"
msgstr "图标不能为空"

#: lclstrconsts.rsiconimageformat
msgid "Icon image must have the same format"
msgstr "图标必须有相同的格式"

#: lclstrconsts.rsiconimageformatchange
msgid "Cannot change format of icon image"
msgstr "无法更改图标格式"

#: lclstrconsts.rsiconimagesize
msgid "Icon image must have the same size"
msgstr "图标必须有相同的尺寸"

#: lclstrconsts.rsiconimagesizechange
msgid "Cannot change size of icon image"
msgstr "无法更改图标大小"

#: lclstrconsts.rsiconnocurrent
msgid "Icon has no current image"
msgstr "图标当前不显示"

#: lclstrconsts.rsinactivebordercolorcaption
msgid "Inactive Border"
msgstr "闲置边框"

#: lclstrconsts.rsinactivecaptioncolorcaption
msgctxt "lclstrconsts.rsinactivecaptioncolorcaption"
msgid "Inactive Caption"
msgstr "无效标题"

#: lclstrconsts.rsinactivecaptiontext
msgctxt "lclstrconsts.rsinactivecaptiontext"
msgid "Inactive Caption"
msgstr "无效标题"

#: lclstrconsts.rsindexoutofbounds
msgid "%s Index %d out of bounds 0 .. %d"
msgstr "%s 索引 %d 超出界外 0 .. %d"

#: lclstrconsts.rsindexoutofrange
msgid "Index Out of range Cell[Col=%d Row=%d]"
msgstr "超出范围的单元[Col=%d Row=%d]"

#: lclstrconsts.rsinfobkcolorcaption
msgid "Info Background"
msgstr "信息背景"

#: lclstrconsts.rsinfotextcolorcaption
msgid "Info Text"
msgstr "信息文本"

#: lclstrconsts.rsinsertrecordhint
msgctxt "lclstrconsts.rsinsertrecordhint"
msgid "Insert"
msgstr "输入"

#: lclstrconsts.rsinvaliddate
msgid "Invalid Date : %s"
msgstr "无效的日期 : %s"

#: lclstrconsts.rsinvaliddaterangehint
msgid "Invalid Date: %s. Must be between %s and %s"
msgstr "无效的日期: %s. 必须在 %s 和 %s之间"

#: lclstrconsts.rsinvalidformobjectstream
msgid "invalid Form object stream"
msgstr "无效的表单对象流"

#: lclstrconsts.rsinvalidpropertyvalue
msgid "Invalid property value"
msgstr "无效的属性值"

#: lclstrconsts.rsinvalidstreamformat
msgid "Invalid stream format"
msgstr "流格式无效"

#: lclstrconsts.rsisalreadyassociatedwith
msgid "%s is already associated with %s"
msgstr "%s 已与 %s关联"

#: lclstrconsts.rsjpeg
msgid "Joint Picture Expert Group"
msgstr "联合图像专家组"

#: lclstrconsts.rslastrecordhint
msgid "Last"
msgstr "最后"

#: lclstrconsts.rslimecolorcaption
msgid "Lime"
msgstr "绿黄色"

#: lclstrconsts.rslistindexexceedsbounds
msgid "List index exceeds bounds (%d)"
msgstr "列表索引超出范围 (%d)"

#: lclstrconsts.rslistmustbeempty
msgid "List must be empty"
msgstr "列表必须为空"

#: lclstrconsts.rsmarooncolorcaption
msgid "Maroon"
msgstr "栗色"

#: lclstrconsts.rsmbabort
msgid "Abort"
msgstr "中止"

#: lclstrconsts.rsmball
msgid "&All"
msgstr "所有(&A)"

#: lclstrconsts.rsmbcancel
msgctxt "lclstrconsts.rsmbcancel"
msgid "Cancel"
msgstr "取消"

#: lclstrconsts.rsmbclose
msgid "&Close"
msgstr "关闭(&C)"

#: lclstrconsts.rsmbhelp
msgid "&Help"
msgstr "帮助(&H)"

#: lclstrconsts.rsmbignore
msgid "&Ignore"
msgstr "忽略(&I)"

#: lclstrconsts.rsmbno
msgid "&No"
msgstr "否(&N)"

#: lclstrconsts.rsmbnotoall
msgid "No to all"
msgstr "全否"

#: lclstrconsts.rsmbok
msgid "&OK"
msgstr "确定(&O)"

#: lclstrconsts.rsmbopen
msgid "&Open"
msgstr "打开(&O)"

#: lclstrconsts.rsmbretry
msgid "&Retry"
msgstr "重试(&R)"

#: lclstrconsts.rsmbsave
msgid "&Save"
msgstr "保存(&S)"

#: lclstrconsts.rsmbunlock
msgid "&Unlock"
msgstr "开启(&U)"

#: lclstrconsts.rsmbyes
msgid "&Yes"
msgstr "是(&Y)"

#: lclstrconsts.rsmbyestoall
msgid "Yes to &All"
msgstr "全是(&A)"

#: lclstrconsts.rsmedgraycolorcaption
msgid "Medium Gray"
msgstr "中灰色"

#: lclstrconsts.rsmenubarcolorcaption
msgid "Menu Bar"
msgstr "菜单栏"

#: lclstrconsts.rsmenucolorcaption
msgctxt "lclstrconsts.rsmenucolorcaption"
msgid "Menu"
msgstr "菜单"

#: lclstrconsts.rsmenuhighlightcolorcaption
msgid "Menu Highlight"
msgstr "菜单高亮显示"

#: lclstrconsts.rsmenutextcolorcaption
msgid "Menu Text"
msgstr "菜单文本"

#: lclstrconsts.rsmodified
msgid "  modified "
msgstr "  修正的"

#: lclstrconsts.rsmoneygreencolorcaption
msgid "Money Green"
msgstr "铜绿色"

#: lclstrconsts.rsmtauthentication
msgid "Authentication"
msgstr "验证"

#: lclstrconsts.rsmtconfirmation
msgid "Confirmation"
msgstr "确认"

#: lclstrconsts.rsmtcustom
msgid "Custom"
msgstr "自定义"

#: lclstrconsts.rsmterror
msgctxt "lclstrconsts.rsmterror"
msgid "Error"
msgstr "错误"

#: lclstrconsts.rsmtinformation
msgid "Information"
msgstr "信息"

#: lclstrconsts.rsmtwarning
msgid "Warning"
msgstr "警告"

#: lclstrconsts.rsnavycolorcaption
msgid "Navy"
msgstr "深蓝色"

#: lclstrconsts.rsnextrecordhint
msgctxt "lclstrconsts.rsnextrecordhint"
msgid "Next"
msgstr "下一个"

#: lclstrconsts.rsnonecolorcaption
msgid "None"
msgstr "没有一个"

#: lclstrconsts.rsnotavalidgridfile
msgid "Not a valid grid file"
msgstr "不是一个有效的栅格文件"

#: lclstrconsts.rsnowidgetset
msgid "No widgetset object. Please check if the unit \"interfaces\" was added to the programs uses clause."
msgstr "没有widgetset对象. 如果单元 \"接口\" 添加到程序的使用条款.请检查"

#: lclstrconsts.rsolivecolorcaption
msgid "Olive"
msgstr "橄榄色"

#: lclstrconsts.rspickdate
msgid "Select a date"
msgstr "选择一个日期"

#: lclstrconsts.rspixmap
msgid "Pixmap"
msgstr "Pixmap图"

#: lclstrconsts.rsportablebitmap
msgid "Portable BitMap"
msgstr "便携式位图"

#: lclstrconsts.rsportablegraymap
msgid "Portable GrayMap"
msgstr "便携式GrayMap图"

#: lclstrconsts.rsportablenetworkgraphic
msgid "Portable Network Graphic"
msgstr "便携式网络图形"

#: lclstrconsts.rsportablepixmap
msgid "Portable PixMap"
msgstr "便携式Pixmap图"

#: lclstrconsts.rspostrecordhint
msgid "Post"
msgstr "邮件"

#: lclstrconsts.rspressoktoignoreandriskdatacorruptionpresscanceltok
msgid "%s%sPress OK to ignore and risk data corruption.%sPress Cancel to kill the program."
msgstr "%s%s按OK忽略数据损坏.%s按取消关闭程序."

#: lclstrconsts.rspriorrecordhint
msgctxt "lclstrconsts.rspriorrecordhint"
msgid "Prior"
msgstr "优先级"

#: lclstrconsts.rspropertydoesnotexist
msgid "Property %s does not exist"
msgstr "属性 %s 不存在"

#: lclstrconsts.rspurplecolorcaption
msgid "Purple"
msgstr "紫色"

#: lclstrconsts.rsqtoptiondograb
msgid "-dograb (only under X11), running under a debugger can cause an implicit -nograb, use -dograb to override. Need QT_DEBUG."
msgstr "-dograb (只有在X11下), 在调试器下运行会导致隐式 -nograb, 使用-dograb 覆盖. 需要 QT_DEBUG."

#: lclstrconsts.rsqtoptiongraphicsstyle
msgid "-graphicssystem param, sets the backend to be used for on-screen widgets and QPixmaps. Available options are native, raster and opengl. OpenGL is still unstable."
msgstr "-graphicssystem param, 设置后端用于屏幕上的小部件和QPixmaps.可用的选项是本地人光栅格式和opengl格式.OpenGL可能不稳定."

#: lclstrconsts.rsqtoptionnograb
msgid "-nograb, tells Qt that it must never grab the mouse or the keyboard. Need QT_DEBUG."
msgstr "-nograb, 告诉Qt,它绝不能抓住鼠标或键盘.需要QT_DEBUG."

#: lclstrconsts.rsqtoptionreverse
msgid "-reverse, sets the application's layout direction to Qt::RightToLeft."
msgstr "-reverse, 设置应用程序的布局方向 Qt::RightToLeft."

#: lclstrconsts.rsqtoptionsession
msgid "-session session, restores the application from an earlier session."
msgstr "-session session, 恢复以前的会话中的应用."

#: lclstrconsts.rsqtoptionstyle
msgid "-style style or -style=style, sets the application GUI style. Possible values are motif, windows, and platinum. If you compiled Qt with additional styles or have additional styles as plugins these will be available to the -style  command line option. NOTE: Not all styles are available on all platforms. If style param does not exist Qt will start an application with default common style (windows)."
msgstr "-style style or -style=style, 设置应用程序GUI样式.可用的的参数是主题,窗口和灰度。如果你编译Qt和额外的风格或有其他风格的插件将这些风格的命令行选项可用。注意:并不是所有的款式都在所有的平台上都可用。如果样式参数不存在Qt应用程序将开始与默认的常见的样式(窗口)。."

#: lclstrconsts.rsqtoptionstylesheet
msgid "-stylesheet stylesheet or -stylesheet=stylesheet, sets the application Style Sheet. The value must be a path to a file that contains the Style Sheet. Note: Relative URLs in the Style Sheet file are relative to the Style Sheet file's path."
msgstr "-stylesheet stylesheet or -stylesheet=stylesheet, sets the application Style Sheet. 设置应用程序的样式表.该值必须是一个文件包含样式表的路径.注意:在样式表文件的相对路径是相对于样式表文件的路径。."

#: lclstrconsts.rsqtoptionsync
msgid "-sync (only under X11), switches to synchronous mode for debugging."
msgstr "-sync (只有在X11下), 为了调试切换为同步模式."

#: lclstrconsts.rsqtoptionwidgetcount
msgid "-widgetcount, prints debug message at the end about number of widgets left undestroyed and maximum number of widgets existed at the same time."
msgstr "-widgetcount, 打印调试信息在结束对部件的数量和保留的最大同时存在的部件数目。."

#: lclstrconsts.rsqtoptionx11bgcolor
msgid "-bg or -background color, sets the default background color and an application palette (light and dark shades are calculated)."
msgstr "-bg or -background color, 设置默认背景色和应用程序调色板 (亮的和暗的阴影将被计算出来)."

#: lclstrconsts.rsqtoptionx11btncolor
msgid "-btn or -button color, sets the default button color."
msgstr "-btn or -button color, 设置默认按钮颜色."

#: lclstrconsts.rsqtoptionx11cmap
msgid "-cmap, causes the application to install a private color map on an 8-bit display."
msgstr "-cmap, 导致应用程序在8位显示上安装一个私有的颜色映射."

#: lclstrconsts.rsqtoptionx11display
msgid "-display display, sets the X display (default is $DISPLAY)."
msgstr "-display display, 设置X显示(默认为$DISPLAY)."

#: lclstrconsts.rsqtoptionx11fgcolor
msgid "-fg or -foreground color, sets the default foreground color."
msgstr "-fg or -foreground color, 设置默认前景色."

#: lclstrconsts.rsqtoptionx11font
msgid "-fn or -font font, defines the application font. The font should be specified using an X logical font description."
msgstr "-fn or -font font, 定义应用程序的字体。这个字体必须使用X逻辑字体描述来北指定."

#: lclstrconsts.rsqtoptionx11geometry
msgid "-geometry geometry, sets the client geometry of the first window that is shown."
msgstr "-geometry geometry, 设置主窗口部件的客户端位置和大小."

#: lclstrconsts.rsqtoptionx11im
msgid "-im, sets the input method server (equivalent to setting the XMODIFIERS environment variable)."
msgstr "-im, 设置输入法 (相当于xmodifiers设置环境变量)."

#: lclstrconsts.rsqtoptionx11inputstyle
msgid "-inputstyle, defines how the input is inserted into the given widget, e.g. onTheSpot makes the input appear directly in the widget, while overTheSpot makes the input appear in a box floating over the widget and is not inserted until the editing is done."
msgstr "-inputstyle, 定义了如何输入插入给定的组件, 例如. 现场使输入直接出现在窗口上, 而在现场拖动已经现成的控件浮动在窗口上."

#: lclstrconsts.rsqtoptionx11name
msgid "-name name, sets the application name."
msgstr "-name name, 设置应用程序名称."

#: lclstrconsts.rsqtoptionx11ncols
msgid "-ncols count, limits the number of colors allocated in the color cube on an 8-bit display, if the application is using the QApplication::ManyColor color specification. If count is 216 then a 6x6x6 color cube is used (i.e. 6 levels of red, 6 of green, and 6 of blue); for other values, a cube approximately proportional to a 2x3x1 cube is used."
msgstr "-ncols count, 如果应用程序使用QApplication::ManyColor颜色规格，在8位显示中限制颜色立方体重分配的颜色。如果count为216，那么一个6x6x6的颜色立方体将被使用(也就是说6级红色，6级绿色和6级蓝色)，对于其它值，与2x3x1立方体成比例的立方体将被使用"

#: lclstrconsts.rsqtoptionx11title
msgid "-title title, sets the application title."
msgstr "-title title, 设置应用程序标题"

#: lclstrconsts.rsqtoptionx11visual
msgid "-visual TrueColor, forces the application to use a TrueColor visual on an 8-bit display."
msgstr "-visual TrueColor, 强制应用程序在8位显示上使用真彩图像"

#: lclstrconsts.rsrasterimageendupdate
msgid "Endupdate while no update in progress"
msgstr "Endupdate虽然没有更新进展"

#: lclstrconsts.rsrasterimagesaveinupdate
msgid "Cannot save image while update in progress"
msgstr "无法保存图像时更新进展"

#: lclstrconsts.rsrasterimageupdateall
msgid "Cannot begin update all when canvas only update in progress"
msgstr "不能开始更新所有画布时只更新进展"

#: lclstrconsts.rsredcolorcaption
msgid "Red"
msgstr "红色"

#: lclstrconsts.rsrefreshrecordshint
msgid "Refresh"
msgstr "刷新"

#: lclstrconsts.rsreplace
msgid "Replace"
msgstr "替换"

#: lclstrconsts.rsreplaceall
msgid "Replace all"
msgstr "全部替换"

#: lclstrconsts.rsresourcenotfound
msgctxt "lclstrconsts.rsresourcenotfound"
msgid "Resource %s not found"
msgstr "资源 %s 没找到"

#: lclstrconsts.rsscrollbarcolorcaption
msgid "ScrollBar"
msgstr "滚动条"

#: lclstrconsts.rsscrollbaroutofrange
msgid "ScrollBar property out of range"
msgstr "滚动条属性超出范围"

#: lclstrconsts.rsselectcolortitle
msgid "Select color"
msgstr "选择颜色"

#: lclstrconsts.rsselectfonttitle
msgid "Select a font"
msgstr "选择字体"

#: lclstrconsts.rssilvercolorcaption
msgid "Silver"
msgstr "银白色"

#: lclstrconsts.rssize
msgid "  size "
msgstr "  大小 "

#: lclstrconsts.rsskybluecolorcaption
msgid "Sky Blue"
msgstr "天蓝色"

#: lclstrconsts.rstealcolorcaption
msgid "Teal"
msgstr ""

#: lclstrconsts.rstext
msgid "Text"
msgstr "文本"

#: lclstrconsts.rstiff
msgid "Tagged Image File Format"
msgstr "标记图像文件格式"

#: lclstrconsts.rsunabletoloaddefaultfont
msgid "Unable to load default font"
msgstr "无法加载默认字体"

#: lclstrconsts.rsunknownerrorpleasereportthisbug
msgid "Unknown Error, please report this bug"
msgstr "未知错误,请报告这个错误"

#: lclstrconsts.rsunknownpictureextension
msgid "Unknown picture extension"
msgstr "未知的图像扩展"

#: lclstrconsts.rsunknownpictureformat
msgid "Unknown picture format"
msgstr "未知的图像格式"

#: lclstrconsts.rsunsupportedbitmapformat
msgid "Unsupported bitmap format."
msgstr "不支持的位图格式."

#: lclstrconsts.rsunsupportedclipboardformat
msgid "Unsupported clipboard format: %s"
msgstr "不支持的剪贴板格式: %s"

#: lclstrconsts.rswarningunreleaseddcsdump
msgid " WARNING: There are %d unreleased DCs, a detailed dump follows:"
msgstr " 警告: 这儿 %d 未释放DCs转储, 详细如下:"

#: lclstrconsts.rswarningunreleasedgdiobjectsdump
msgid " WARNING: There are %d unreleased GDIObjects, a detailed dump follows:"
msgstr " 警告: 这儿 %d 未释放GDI对象转储, 详细如下:"

#: lclstrconsts.rswarningunreleasedmessagesinqueue
msgid " WARNING: There are %d messages left in the queue! I'll free them"
msgstr " 警告: 这儿 %d 队列消息! 我将免费使用"

#: lclstrconsts.rswarningunreleasedtimerinfos
msgid " WARNING: There are %d TimerInfo structures left, I'll free them"
msgstr " 警告: 这儿 %d 时间过期, 我将免费使用"

#: lclstrconsts.rswarningunremovedpaintmessages
msgid " WARNING: There are %s unremoved LM_PAINT/LM_GtkPAINT message links left."
msgstr " 警告: 这儿 %s 保留 LM_PAINT/LM_GtkPAINT 消息连接了."

#: lclstrconsts.rswhitecolorcaption
msgid "White"
msgstr "白色"

#: lclstrconsts.rswholewordsonly
msgid "Whole words only"
msgstr "匹配字"

#: lclstrconsts.rswin32error
msgid "Error:"
msgstr "错误:"

#: lclstrconsts.rswin32warning
msgid "Warning:"
msgstr "警告:"

#: lclstrconsts.rswindowcolorcaption
msgid "Window"
msgstr "窗口"

#: lclstrconsts.rswindowframecolorcaption
msgid "Window Frame"
msgstr "窗口框架"

#: lclstrconsts.rswindowtextcolorcaption
msgid "Window Text"
msgstr "窗口文本"

#: lclstrconsts.rsyellowcolorcaption
msgid "Yellow"
msgstr "黄色"

#: lclstrconsts.scannotfocus
msgid "Cannot focus a disabled or invisible window"
msgstr "一个无法使用或不可见的窗口"

#: lclstrconsts.sduplicatemenus
msgid "Duplicate menus"
msgstr "复制菜单"

#: lclstrconsts.sinvalidactioncreation
msgid "Invalid action creation"
msgstr "创建值无效"

#: lclstrconsts.sinvalidactionenumeration
msgid "Invalid action enumeration"
msgstr "枚举值无效"

#: lclstrconsts.sinvalidactionregistration
msgid "Invalid action registration"
msgstr "登记无效操作"

#: lclstrconsts.sinvalidactionunregistration
msgid "Invalid action unregistration"
msgstr "撤销无效操作"

#: lclstrconsts.sinvalidcharset
msgid "The char set in mask \"%s\" is not valid!"
msgstr "伪指令 \"%s\" 是无效的!"

#: lclstrconsts.sinvalidimagesize
msgid "Invalid image size"
msgstr "无效的图像尺寸"

#: lclstrconsts.sinvalidindex
msgid "Invalid ImageList Index"
msgstr "无效的ImageList指数"

#: lclstrconsts.smenuindexerror
msgid "Menu index out of range"
msgstr "菜单索引超出范围"

#: lclstrconsts.smenuitemisnil
msgid "MenuItem is nil"
msgstr "子菜单为空"

#: lclstrconsts.smenunotfound
msgid "Sub-menu is not in menu"
msgstr "菜单中无子菜单"

#: lclstrconsts.smkcalt
msgid "Alt+"
msgstr ""

#: lclstrconsts.smkcbksp
msgid "BkSp"
msgstr ""

#: lclstrconsts.smkcctrl
msgid "Ctrl+"
msgstr ""

#: lclstrconsts.smkcdel
msgid "Del"
msgstr ""

#: lclstrconsts.smkcdown
msgctxt "lclstrconsts.smkcdown"
msgid "Down"
msgstr ""

#: lclstrconsts.smkcend
msgctxt "lclstrconsts.smkcend"
msgid "End"
msgstr ""

#: lclstrconsts.smkcenter
msgid "Enter"
msgstr ""

#: lclstrconsts.smkcesc
msgid "Esc"
msgstr ""

#: lclstrconsts.smkchome
msgctxt "lclstrconsts.smkchome"
msgid "Home"
msgstr ""

#: lclstrconsts.smkcins
msgid "Ins"
msgstr ""

#: lclstrconsts.smkcleft
msgctxt "lclstrconsts.smkcleft"
msgid "Left"
msgstr ""

#: lclstrconsts.smkcmeta
msgid "Meta+"
msgstr ""

#: lclstrconsts.smkcpgdn
msgid "PgDn"
msgstr ""

#: lclstrconsts.smkcpgup
msgid "PgUp"
msgstr ""

#: lclstrconsts.smkcright
msgctxt "lclstrconsts.smkcright"
msgid "Right"
msgstr ""

#: lclstrconsts.smkcshift
msgid "Shift+"
msgstr ""

#: lclstrconsts.smkcspace
msgid "Space"
msgstr ""

#: lclstrconsts.smkctab
msgctxt "lclstrconsts.smkctab"
msgid "Tab"
msgstr ""

#: lclstrconsts.smkcup
msgctxt "lclstrconsts.smkcup"
msgid "Up"
msgstr ""

#: lclstrconsts.snomdiform
msgid "No MDI form present."
msgstr "无MDI窗体"

#: lclstrconsts.snotimers
msgid "No timers available"
msgstr "无可用的计时器"

#: lclstrconsts.sparexpected
msgid "Wrong token type: %s expected"
msgstr "错误的标记类型: %s 预期的"

#: lclstrconsts.sparinvalidfloat
msgid "Invalid floating point number: %s"
msgstr "无效的浮点数: %s"

#: lclstrconsts.sparinvalidinteger
msgid "Invalid integer number: %s"
msgstr "无效的整数: %s"

#: lclstrconsts.sparlocinfo
msgid " (at %d,%d, stream offset %.8x)"
msgstr "在 %d,%d, 流偏移量 %.8x"

#: lclstrconsts.sparunterminatedbinvalue
msgid "Unterminated byte value"
msgstr "未终止的字节数值"

#: lclstrconsts.sparunterminatedstring
msgid "Unterminated string"
msgstr "未终止的字符串"

#: lclstrconsts.sparwrongtokensymbol
msgid "Wrong token symbol: %s expected but %s found"
msgstr "错误的标记类型: %s 预期的 %s 发现"

#: lclstrconsts.sparwrongtokentype
msgid "Wrong token type: %s expected but %s found"
msgstr "错误的标记类型: %s 预期的 %s 发现"