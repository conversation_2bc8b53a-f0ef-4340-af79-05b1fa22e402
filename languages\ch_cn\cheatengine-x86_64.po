﻿msgstr "→今天是每年的4月1日愚人节！没想到今天还有人在用CE！"
msgid "Does not affect the standalone trainer because that one is protected by default"
msgstr "Content-Type: text/plain; charset=UTF-8"

msgid "Tip: When doubleclicking the value and it represents an address, then you can hold shift to make it show in the disassembler, and ctrl to make it show in the memory view. Else Cheat Engine will guess it for you based on if it's executable memory or not"
msgstr "Tip: When doubleclicking the value and it represents an address, then you can hold shift to make it show in the disassembler, and ctrl to make it show in the memory view. Else Cheat Engine will guess it for you based on if it's executable memory or not"

#: aboutunit.rsareyousureyouwanttolaunchdbvm
msgid "Are you sure you want to launch DBVM? You seem to be running in 32-bit, so don't really need it that badly (Except for ultimap and cloaked operations)"
msgstr "你确定要启动 DBVM? 你的系统是32位的, 所以最好不要使用它(尤其是 ultimap 和 cloaked 操作)"

#: aboutunit.rsdidyoureallythinkyoudfindaneastereggbydoingthiswel
msgid "Did you really think you'd find an easter egg by doing this? Well, you know what? You where right!"
msgstr "你这么做是想发现复活节彩蛋吗? 你怎么做到的? 恭喜你, 你找到了!"

#: aboutunit.rslaunchdbvmwasnotassigned
msgid "launchdbvm was not assigned"
msgstr "未分配要启动的 DBVM"

#: tformsettings.cbusethreadforfreeze.caption
msgid "Use thread for freeze"
msgstr "使用线程冻结"

#: tmainform.miluadocumentation.caption
msgid "Lua documentation"
msgstr "Cheat Engine Lua文件"

#: tmemorybrowser.miunexpectedexceptionbreakifinregion.caption
msgid "Only in specified regions"
msgstr "仅在指定区域"

#: aboutunit.rsthismeansthatyourecurrentlynotrunningdbvm
msgid "This means that you're currently not running dbvm, but that your system is capable of running it"
msgstr "这表示你没有运行 DBVM, 但你的系统随时可以运行它"

#: aboutunit.rsthismeansthatyoursystemisrunningdbvm
msgid "This means that your system is running dbvm. This means ce will make use of some advanced tools that are otherwise unavailable"
msgstr "这表示你的系统正在运行 DBVM.以及 CE 还可以使用一些更先进的工具"

#: aboutunit.rsthismeansthatyouwillneedanewcpuinteltobeabletouset
msgid "This means that you will need a new cpu (intel) to be able to use the advanced dbvm options"
msgstr "这表示你需要一块最新的CPU (Intel) 才能使用 DBVM 高级选项"

#: aboutunit.rsyoursystemdoesnotsupportdbvm
msgid "Your system DOES NOT support DBVM"
msgstr "你的系统不支持 DBVM"

#: aboutunit.rsyoursystemisrunningdbvmversion
msgid "Your system is running DBVM version %s"
msgstr "你的系统正在运行 DBVM.版本: %s"

#: aboutunit.rsyoursystemsupportsdbvm
msgid "Your system supports DBVM"
msgstr "你的系统支持 DBVM"

#: tmemorybrowser.midebugbreak.caption
msgctxt "TMEMORYBROWSER.MIDEBUGBREAK.CAPTION"
msgid "Break"
msgstr "中断"

#: formsettingsunit.rsattachtoforegroundprocess
msgid "Attach to current foreground process"
msgstr "附加到当前进程"

#: tformsettings.lblrepeatdelay.caption
msgid "Repeat scan delay"
msgstr "重复扫描延迟"

#: tmemorybrowser.menuitem29.caption
msgid "Compare data/structures"
msgstr "分析数据/(新)遍历"

#: tformsettings.cbluapassivegarbagecollection.caption
msgid "Passive garbage collection"
msgstr "被动的垃圾收集"

#: tformsettings.cbluagarbagecollectall.caption
msgid "Collect all garbage every few seconds"
msgstr "每隔几秒钟收集一次垃圾"

#: tformsettings.cbluaonlycollectwhenlarger.caption
msgid "Only when the size is bigger than:"
msgstr "只有当尺寸大于"

#: tformsettings.label20.caption
msgid "Megabyte"
msgstr ":兆字节"

#: accesscheck.rsbutyoudohavemodifyrights
msgid "But you do have modify rights"
msgstr "但你有修改的权限"

#: accesscheck.rsnodeleterights
msgid "No delete rights"
msgstr "无删除权限"

#: tmainform.cbluaformula.caption
msgid "Lua formula"
msgstr "Lua公式"

#: accesscheck.rsnofilecreationrightsornofileoverwriterights
msgid "No file creation rights or no file overwrite rights"
msgstr "无创建文件或覆盖文件的权限"

#: accesscheck.rsnofiledeletionrights
msgid "No file deletion rights"
msgstr "无删除文件的权限"

#: accesscheck.rsnofilemodificationrights
msgid "No file modification rights"
msgstr "无修改文件的权限"

#: tformsettings.cbskip_page_writecombine.caption
msgid "Don't scan memory that is protected with the Write Combine option"
msgstr "不要扫描受写组合选项保护的内存"


#: accessedmemory.rsamerror
msgctxt "accessedmemory.rsamerror"
msgid "Error"
msgstr "错误"

#: accessedmemory.rsamyoucantsaveanemptylist
msgid "You can't save an empty list"
msgstr "你无法保存一个空白列表"

#: addresslist.rsactive
msgctxt "addresslist.rsactive"
msgid "Active"
msgstr "激活"

#: addresslist.rsaddress
msgctxt "addresslist.rsaddress"
msgid "Address"
msgstr "地址"

#: addresslist.rsaladdaddress
msgid "Add address"
msgstr "添加地址"

#: addresslist.rsalautoassemblescritp
msgid "Auto Assemble script"
msgstr "自动汇编脚本"

#: addresslist.rsalnodescription
msgctxt "addresslist.rsalnodescription"
msgid "No description"
msgstr "无描述"

#: addresslist.rschangedescription
msgid "Change Description"
msgstr "更改描述"

#: addresslist.rschangevalue
msgctxt "addresslist.rschangevalue"
msgid "Change Value"
msgstr "改变数值"

#: addresslist.rsdescription
msgctxt "addresslist.rsdescription"
msgid "Description"
msgstr "描述"

#: addresslist.rsdoyouwanttodeletetheselectedaddress
msgid "Do you want to delete the selected address?"
msgstr "你想要删除选中的地址吗?"

#: addresslist.rsdoyouwanttodeletetheselectedaddresses
msgid "Do you want to delete the selected addresses?"
msgstr "你想要删除选中的地址吗?"

#: addresslist.rsnotallvaluetypescouldhandlethevalue
msgid "Not all value types could handle the value %s"
msgstr "并不是所有的数值类型都能够处理 %s 这种数值"

#: addresslist.rsscript
msgid "<script>"
msgstr "<脚本>"

#: addresslist.rsthevaluecouldnotbeparsed
msgid "The value %s could not be parsed"
msgstr "无法解析数值 %s"

#: addresslist.rstype
msgctxt "addresslist.rstype"
msgid "Type"
msgstr "类型"

#: addresslist.rsvalue
msgctxt "addresslist.rsvalue"
msgid "Value"
msgstr "数值"

#: addresslist.rswhatvaluetochangethisto
msgid "what value to change this to?"
msgstr "请输入新的数值:"

#: addresslist.rswhatwillbethenewdescription
msgid "What will be the new description?"
msgstr "请输入新的描述:"

#: addressparser.rsapthisisnotavalidaddress
msgid "This is not a valid address"
msgstr "这不是一个有效的地址"

#: advancedoptionsunit.rsaocheatenginefailedtogetintotheconfigofselectedprogram
msgid "Cheat Engine failed to get into the config of the selected program."
msgstr "Cheat Engine 获取所选程序的配置失败."

#: advancedoptionsunit.rsaoerrorwhiletryingtocreatethesharedkeystructureetc
msgid "Error while trying to create the shared key structure! (Which efficiently renders this whole feature useless)"
msgstr "尝试创建共享密匙结构时失败! (有效的表现出该功能是无用的)"

#: advancedoptionsunit.rsaoyoucanonlyloadexefiles
msgctxt "advancedoptionsunit.rsaoyoucanonlyloadexefiles"
msgid "You can only load EXE files"
msgstr "你只能加载 EXE 文件"

#: advancedoptionsunit.rsareyousureyouwishtodeletetheseentries
msgid "Are you sure you wish to delete these entries?"
msgstr "你确定要删除这些项目吗?"

#: advancedoptionsunit.rsdelete
msgctxt "advancedoptionsunit.rsdelete"
msgid "Delete"
msgstr "删除"

#: advancedoptionsunit.rsgivethenewnameofthisentry
msgid "Give the new name of this entry"
msgstr "请输入一个新的地址"

#: advancedoptionsunit.rsnewname
msgid "New name"
msgstr "新名字"

#: advancedoptionsunit.rspaused
msgid "paused"
msgstr "已暂停"

#: tformsettings.miunexpectedbreakpointsignore.caption
msgid "Ignore"
msgstr "忽略"

#: advancedoptionsunit.rspausethegame
msgctxt "advancedoptionsunit.rspausethegame"
msgid "Pause the game"
msgstr "暂停游戏"

#: advancedoptionsunit.rsresumethegame
msgid "Resume the game"
msgstr "恢复游戏"

#: tformsettings.miunexpectedbreakpointsbreakwheninsideregion.caption
msgid "Break when inside specific regions"
msgstr "当区域内的特异性突变"

#: tfrmsetuppsnnode.cbautotrustchildren.caption
msgid "Automatically trust stability of all new childnodes"
msgstr "自动信任所有新子节点的稳定性"

#: advancedoptionsunit.rsthememoryatthisaddresscouldntbewritten
msgctxt "advancedoptionsunit.rsthememoryatthisaddresscouldntbewritten"
msgid "The memory at this address couldn't be written"
msgstr "该内存地址无法写入"

#: advancedoptionsunit.straddressalreadyinthelist
msgctxt "advancedoptionsunit.straddressalreadyinthelist"
msgid "This address is already in the list"
msgstr "列表中存在相同的地址"

#: advancedoptionsunit.stralreadyinthelist
msgid "This byte is already part of another opcode already present in the list"
msgstr "列表中现有的代码已经是另一个操作码的一部分了"

#: advancedoptionsunit.strcecode
msgid "Cheat Engine code:"
msgstr "Cheat Engine 代码:"

#: advancedoptionsunit.strchangeof
msgid "Change of "
msgstr "替换了 "

#: advancedoptionsunit.strcode
msgid "Code :"
msgstr "代码 :"

#: advancedoptionsunit.strcouldntrestorecode
msgid "Error when trying to restore this code!"
msgstr "还原代码错误!"

#: advancedoptionsunit.strcouldntwrite
msgctxt "advancedoptionsunit.strcouldntwrite"
msgid "The memory at this address couldn't be written"
msgstr "该内存地址无法写入"

#: advancedoptionsunit.strfindwhatcodeaccesses
msgid "Find out what addresses this code accesses"
msgstr "找出代码访问的地址"

#: advancedoptionsunit.strfindwhatcodereads
msgid "Find out what addresses this code reads from"
msgstr "找出代码读取的地址"

#: advancedoptionsunit.strfindwhatcodewrites
msgctxt "advancedoptionsunit.strfindwhatcodewrites"
msgid "Find out what addresses this code writes to"
msgstr "找出代码改写的地址"

#: advancedoptionsunit.strnamececode
msgid "What name do you want to give this code?"
msgstr "你要给代码取个什么名字呢?"

#: advancedoptionsunit.strnotreadable
msgid "This address is not readable"
msgstr "该地址不可读"

#: processwindowunit.rsapplications
msgid "Applications"
msgstr "应用程序"

#: processwindowunit.rsprocesses
msgid "Processes"
msgstr "当前进程"

#: processwindowunit.rswindows
msgid "Windows"
msgstr "视窗"

#: advancedoptionsunit.strnotthesame
msgid "The memory at this address isn't what it should be! Continue?"
msgstr "内存中不存在此地址！要继续吗?"

#: advancedoptionsunit.strnotwhatitshouldbe
msgid "The memory at this address is'nt what it should be! Continue?"
msgstr "内存中不存在此地址！要继续吗?"

#: advancedoptionsunit.strpartofopcodeinthelist
msgid "At least one of these bytes is already in the list"
msgstr "至少有一个字节已经在列表里了"

#: advancedoptionsunit.strselectexefor3d
msgid "Select the executable of the Direct-3D game"
msgstr "请选择可执行的 Direct-3D 游戏"

#: assemblerarm.rscannotbeencoded
msgid " can not be encoded using 8 bits and double rotate"
msgstr " 不能使用8位字节/双8位字节编码"

#: assemblerarm.rsdistanceistoobig
msgid "Distance is too big"
msgstr "间隔过大"

#: assemblerarm.rsinvaliddestinationregister
msgid "Invalid destination register"
msgstr "目标寄存器无效"

#: assemblerarm.rsinvalidfirstoperandregister
msgid "Invalid first operand register"
msgstr "操作数寄存器的第一操作数无效"

#: assemblerarm.rsinvalidopcode
msgid "Invalid opcode"
msgstr "操作码无效"

#: assemblerarm.rsinvalidparameter1
msgid "Invalid parameter 1"
msgstr "参数 1无效"

#: assemblerarm.rsinvalidparameter2
msgid "Invalid parameter 2"
msgstr "参数 2无效"

#: assemblerarm.rsinvalidparameter3
msgid "Invalid parameter 3"
msgstr "参数 3无效"

#: assemblerarm.rsinvalidparameter4
msgid "Invalid parameter 4"
msgstr "参数 4无效"

#: assemblerarm.rsinvalidparameters
msgid "invalid parameters"
msgstr "参数无效"

#: assemblerarm.rsinvalidregister
msgctxt "assemblerarm.rsinvalidregister"
msgid "Invalid register"
msgstr "寄存器无效"

#: assemblerarm.rsinvalidregisterinregisterlist
msgid "Invalid register in register list:"
msgstr "列表中以下寄存器无效:"

#: assemblerarm.rsinvalidregisterlist
msgid "Invalid register list"
msgstr "寄存器列表无效"

#: assemblerarm.rsinvalidshift
msgid "Invalid shift"
msgstr "右移无效"

#: assemblerarm.rsinvalidshiftparameters
msgid "invalid shift parameters"
msgstr "移位参数无效"

#: assemblerarm.rsinvalidshiftregister
msgid "Invalid shift register"
msgstr "移位寄存器无效"

#: assemblerarm.rsthedestinationaddressmustbedividableby4
msgid "The destination address must be dividable by 4"
msgstr "目标地址必须能被4整除"

#: assemblerarm.rsthedistanceistoobig
msgid "The distance is too big"
msgstr "间隔过大"

#: assemblerarm.rsthevalue
msgid "The value "
msgstr "数值 "

#: assemblerarm.rsthisinstructionclassdoesnotallow
msgid "This instruction class does not allow a register based shift"
msgstr "这个类型的指令不允许右移寄存器"

#: assemblerarm.rstodochangethistoa12byteinstruction
msgid "Todo: Change this to a 12 byte instruction: LDR/STR [PC,#16] - B PC - DD offset"
msgstr "Todo: 更改此 12 字节指令: LDR/STR [PC,#16] - B PC - DD 偏移"

#: assemblerunit.rsassemblererror
msgid "Assembler error"
msgstr "汇编器错误"

#: assemblerunit.rsidontunderstandwhatyoumeanwith
msgid "I don't understand what you mean with "
msgstr "我不明白你的意思 "

#: assemblerunit.rsinvalid
msgid "Invalid"
msgstr "无效"

#: assemblerunit.rsinvalidaddress
msgctxt "assemblerunit.rsinvalidaddress"
msgid "Invalid address"
msgstr "无效的地址"

#: assemblerunit.rsinvalidmultiplier
msgid "Invalid multiplier"
msgstr "无效的乘法"

#: assemblerunit.rsinvalidregister
msgctxt "assemblerunit.rsinvalidregister"
msgid "Invalid register"
msgstr "无效的寄存器"

#: assemblerunit.rsnegativeregisterscannotbeencoded
msgid "Negative registers can not be encoded"
msgstr "负数的寄存器不能编码"

#: assemblerunit.rsoffsettoobig
msgid "offset too big"
msgstr "偏移过大"

#: assemblerunit.rstheassemblertriedtosetaregistevaluethatistoohigh
msgid "The assembler tried to set a register value that is too high"
msgstr "汇编器尝试设置的寄存器值过大"

#: assemblerunit.rswtfisa
msgid "WTF is a "
msgstr "WTF is a "

#: autoassembler.rsaaerror
msgid "Error: "
msgstr "错误: "

#: autoassembler.rsaaerrorinthestructuredefinitionof
msgid "Error in the structure definition of %s at line %d"
msgstr "错误的结构定义 %s 错误行 %d"

#: autoassembler.rsaaerrorwhilesacnningforaobs
msgid "Error while scanning for AOB's : "
msgstr "当扫描AOB时发生错误 : "

#: autoassembler.rsaaisareservedword
msgid "%s is a reserved word"
msgstr "%s 是一个保留字"

#: autoassembler.rsaaluaerrorinthescriptatline
msgid "Lua error in the script at line "
msgstr "Lua 脚本错误行 "

#: autoassembler.rsaamodulenotfound
msgid "module not found:"
msgstr "模块未找到:"

#: autoassembler.rsaanoendfound
msgid "No end found"
msgstr "END 未找到"

#: autoassembler.rsaanoideawhatxis
msgid "No idea what %s is"
msgstr "不知道 %s 是什么"

#: autoassembler.rsaathearrayofbytenamed
msgid "The array of byte named %s could not be found"
msgstr "名称为 %s 的字节数组无法找到"

#: autoassembler.rscouldnotbefound
msgctxt "autoassembler.rscouldnotbefound"
msgid "%s could not be found"
msgstr "%s 未找到"

#: autoassembler.rscouldnotbeinjected
msgid "%s could not be injected"
msgstr "%s 无法注入"

#: autoassembler.rsdefinealreadydefined
msgid "Define %s already defined"
msgstr "%s 已定义"

#: autoassembler.rserrorinline
msgid "Error in line %s (%s) :%s"
msgstr "出错的行 %s (%s) :%s"

#: autoassembler.rsfailuretoallocatememory
msgid "Failure to allocate memory"
msgstr "分配内存失败"

#: autoassembler.rsforwardjumpwithnolabeldefined
msgid "Forward jump with no label defined"
msgstr "要跳转的标签尚未定义"

#: autoassembler.rsgoto
msgid "Go to "
msgstr "转到 "

#: autoassembler.rsinvalidaddressforreadmem
msgid "Invalid address for ReadMem"
msgstr "ReadMem 设定的地址无效"

#: autoassembler.rsinvalidsizeforreadmem
msgid "Invalid size for ReadMem"
msgstr "ReadMem 设定的大小无效"

#: autoassembler.rsisbeingredeclared
msgid "%s is being redeclared"
msgstr "%s 正在重新声明"

#: autoassembler.rsisnotavalidbytestring
msgid "%s is not a valid bytestring"
msgstr "%s 不是有效的字节串"

#: autoassembler.rsisnotavalididentifier
msgid "%s is not a valid identifier"
msgstr "%s 标识符无效"

#: autoassembler.rsisnotavalidsize
msgid "%s is not a valid size"
msgstr "%s 是无效的大小"

#: autoassembler.rslabelisbeingdefinedmorethanonce
msgid "label %s is being defined more than once"
msgstr "label %s 被重复定义"

#: autoassembler.rslabelisnotdefinedinthescript
msgid "label %s is not defined in the script"
msgstr "label %s 脚本中未定义"

#: autoassembler.rsneedtousekernelmodereadwriteprocessmemory
msgid "You need to use kernelmode read/writeprocessmemory if you want to use KALLOC"
msgstr "如果要使用KALLOC, 你需要使用 内核模式 读写进程内存"

#: autoassembler.rsnotallinstructionscouldbeinjected
msgid "Not all instructions could be injected"
msgstr "并不是所有的指令都能被注入"

#: autoassembler.rssorrybutwithoutthedriverkallocwillnotfunction
msgid "Sorry, but without the driver KALLOC will not function"
msgstr "抱歉, 没有驱动KALLOC无法正常工作"

#: autoassembler.rssyntaxerror
msgid "Syntax error"
msgstr "语法错误"

#: autoassembler.rssyntaxerrorfullaccessaddresssize
msgid "Syntax error. FullAccess(address,size)"
msgstr "语法错误. FullAccess(address,size)"

#: autoassembler.rstheaddressincreatethreadisnotvalid
msgid "The address in createthread(%s) is not valid"
msgstr "createthread(%s) 中的地址无效"

#: autoassembler.rstheaddressinloadbinaryisnotvalid
msgid "The address in loadbinary(%s,%s) is not valid"
msgstr "loadbinary(%s,%s) 中的地址无效"

#: autoassembler.rsthearrayofbytecouldnotbefound
msgid "The array of byte '%s' could not be found"
msgstr "没有找到字节数组 '%s'"

#: autoassembler.rsthebytesatarenotwhatwasexpected
msgid "The bytes at %s are not what was expected"
msgstr "字节 %s 无效"

#: autoassembler.rsthecodeinjectionwassuccessfull
msgid "The code injection was successfull"
msgstr "代码注入成功"

#: autoassembler.rsthefiledoesnotexist
msgid "The file %s does not exist"
msgstr "这个文件 %s 不存在"

#: autoassembler.rsthefollowingkerneladdresseswhereallocated
msgid "The following kernel addresses where allocated"
msgstr "已分配以下内核地址"

#: autoassembler.rstheidentifierhasalreadybeendeclared
msgid "The identifier %s has already been declared"
msgstr "标识符 %s 已声明"

#: autoassembler.rsthememoryatcannotberead
msgid "The memory at +%s can not be read"
msgstr "内存 +%s 无法读取"

#: autoassembler.rsthememoryatcouldnotbefullyread
msgid "The memory at %s could not be fully read"
msgstr "%s 内存无法完全读取"

#: autoassembler.rsthereiscodedefinedwithoutspecifyingtheaddressitbel
msgid "There is code defined without specifying the address it belongs to"
msgstr "代码定义未指定地址,它位于"

#: autoassembler.rsthisaddressspecifierisnotvalid
msgid "This address specifier is not valid"
msgstr "地址符无效"

#: autoassembler.rsthiscodecanbeinjectedareyousure
msgid "This code can be injected. Are you sure?"
msgstr "可以注入代码. 是否注入?"

#: autoassembler.rsthisinstructioncantbecompiled
msgid "This instruction can't be compiled"
msgstr "该指令无法编译"

#: autoassembler.rswassupposedtobeaddedtothesymbollistbutitisntdeclar
msgid "%s was supposed to be added to the symbollist, but it isn't declared"
msgstr "%s 可加入到符号表, 但它没有声明"

#: autoassembler.rswrongsyntaxallocidentifiersizeinbytes
msgid "Wrong syntax. ALLOC(identifier,sizeinbytes)"
msgstr "语法错误. ALLOC(identifier,sizeinbytes)"

#: autoassembler.rswrongsyntaxaobscanmodulename11223355
msgid "Wrong syntax. AOBSCANMODULE(name, module, 11 22 33 ** 55)"
msgstr "语法错误. AOBSCANMODULE(name, module, 11 22 33 ** 55)"

#: autoassembler.rswrongsyntaxaobscanname11223355
msgid "Wrong syntax. AOBSCAN(name,11 22 33 ** 55)"
msgstr "语法错误. AOBSCAN(name,11 22 33 ** 55)"

#: autoassembler.rswrongsyntaxaobscanregion
msgid "Wrong syntax. AOBSCANREGION(name, startaddress, stopaddress, 11 22 33 ** 55)"
msgstr "语法错误. AOBSCANREGION(name, startaddress, stopaddress, 11 22 33 ** 55)"

#: autoassembler.rswrongsyntaxassertaddress1122335566
msgid "Wrong syntax. ASSERT(address,11 22 33 ** 55 66)"
msgstr "语法错误. ASSERT(address,11 22 33 ** 55 66)"

#: autoassembler.rswrongsyntaxcreatethreadaddress
msgid "Wrong syntax. CreateThread(address)"
msgstr "语法错误. CreateThread(address)"

#: autoassembler.rswrongsyntaxdefinenamewhatever
msgid "Wrong syntax. DEFINE(name,whatever)"
msgstr "语法错误. DEFINE(name,whatever)"

#: autoassembler.rswrongsyntaxglobalallocnamesize
msgid "Wrong syntax. GLOBALALLOC(name,size)"
msgstr "语法错误. GLOBALALLOC(name,size)"

#: autoassembler.rswrongsyntaxincludefilenamecea
msgid "Wrong syntax. Include(filename.cea)"
msgstr "语法错误. Include(filename.cea)"

#: autoassembler.rswrongsyntaxkallocidentifiersizeinbytes
msgid "Wrong syntax. kalloc(identifier,sizeinbytes)"
msgstr "语法错误. kalloc(identifier,sizeinbytes)"

#: autoassembler.rswrongsyntaxloadbinaryaddressfilename
msgid "Wrong syntax. LoadBinary(address,filename)"
msgstr "语法错误. LoadBinary(address,filename)"

#: autoassembler.rswrongsyntaxloadlibraryfilename
msgid "Wrong syntax. LoadLibrary(filename)"
msgstr "语法错误. LoadLibrary(filename)"

#: autoassembler.rswrongsyntaxluacall
msgid "Wrong Syntax. LuaCall(luacommand)"
msgstr "语法错误. LuaCall(luacommand)"

#: autoassembler.rswrongsyntaxreadmemaddresssize
msgid "Wrong syntax. ReadMem(address,size)"
msgstr "语法错误. ReadMem(address,size)"

#: autoassembler.rswrongsyntaxreassemble
msgid "Wrong syntax. Reassemble(address)"
msgstr "语法错误. Reassemble(address)"

#: autoassembler.rswrongsyntaxsharedallocnamesize
msgid "Wrong syntax. SHAREDALLOC(name,size)"
msgstr "语法错误. SHAREDALLOC(name,size)"

#: autoassembler.rsxcouldnotbefound
msgctxt "autoassembler.rsxcouldnotbefound"
msgid "%s could not be found"
msgstr "%s 无法找到"

#: autoassembler.rsyoucanonlyhaveonedisablesection
msgid "You can only have one disable section"
msgstr "只能有一个 [Disable] 字段"

#: autoassembler.rsyoucanonlyhaveoneenablesection
msgid "You can only have one enable section"
msgstr "只能有一个 [Enable] 字段"

#: autoassembler.rsyouhavntspecifiedadisablesection
msgid "You havn't specified a disable section"
msgstr "你没指定 [Disable] 字段"

#: autoassembler.rsyouhavntspecifiedaenablesection
msgid "You havn't specified a enable section"
msgstr "你没指定 [Enable] 字段"

#: bigmemallochandler.rsallocerror
msgid "VirtualAlloc failed. You probably don't have enough system memory free. Either install more RAM, or increase the maximum allowed paging size"
msgstr "VirtualAlloc 失败. 你可能没有足够可用的系统内存. 要么安装更多的内存要么增加虚拟内存的大小"

#: bigmemallochandler.rsbmavirtualallocfailedyouprobablydonthaveenoughtvirtualmemoryfreeetc
msgid "VirtualAlloc failed. You probably don't have enough virtual memory free. Use the 64-bit version instead"
msgstr "VirtualAlloc 失败. 你可能没有足够可用的系统内存. 现在改用64位版本的代替"

#: bigmemallochandler.rscepointerscanmemorymanager
msgid "CE Pointerscan memory manager"
msgstr "\"CE 指针扫描\"内存管理器"

#: byteinterpreter.rsbibyte
msgid "(byte)"
msgstr "(字节)"

#: byteinterpreter.rsbidouble
msgid "(double)"
msgstr "(双浮点)"

#: byteinterpreter.rsbidword
msgid "(dword)"
msgstr "(双字)"

#: byteinterpreter.rsbifloat
msgid "(float)"
msgstr "(单浮点)"

#: byteinterpreter.rsbiqword
msgid "(qword)"
msgstr "(四字)"

#: byteinterpreter.rsbiword
msgid "(word)"
msgstr "(单字)"

#: cedebugger.rscontinue
msgid "Continue?"
msgstr "继续吗?"

#: cedebugger.rsdebugerror
msgid "I couldn't attach the debugger to this process! You could try to open the process using the processpicker and try that! If that also doesn't work check if you have debugging rights."
msgstr "调试器无法附加到这个进程中! 你可以尝试使用进程选择器打开该进程! 如果仍然无法工作,请检查有无调试的权限.可能当前的进程有驱动保护,或者该进程可能被其它程序占领！请尝试,打开设置,其它,把四个选项全部打钩确定然后再次测试！"

#: cedebugger.rsdonotclosece
msgid "If you close Cheat Engine while the game is running, the game will close too. Are you sure you want to do this?"
msgstr "如果你在游戏运行过程中关闭了 Cheat Engine, 游戏也会关闭. 是否继续?"

#: cedebugger.rspleasetargetanotherprocess
msgid "Please target another process"
msgstr "请打开其它进程"

#: cedebugger.rsthiswillattachthedebuggerofcheatenginetothecurrent
msgid "This will attach the debugger of Cheat Engine to the current process."
msgstr "将会使用 Cheat Engine 的调试器附加当前进程."

#: cedebugger.rsyoumustfirstopenaprocess
msgid "You must first open a process"
msgstr "你必须先打开一个进程"

#: cefuncproc.rsalt
msgctxt "cefuncproc.rsalt"
msgid "Alt"
msgstr "Alt"

#: cefuncproc.rsapplicationskey
msgctxt "cefuncproc.rsapplicationskey"
msgid "Applications key"
msgstr "Applications key"

#: cefuncproc.rsbackspace
msgctxt "cefuncproc.rsbackspace"
msgid "Backspace"
msgstr "Backspace"

#: cefuncproc.rsbreak
msgctxt "cefuncproc.rsbreak"
msgid "Break"
msgstr "Break"

#: cefuncproc.rscapslock
msgctxt "cefuncproc.rscapslock"
msgid "Caps Lock"
msgstr "Caps Lock"

#: cefuncproc.rscefpdllinjectionfailedsymbollookuperror
msgid "Dll injection failed: symbol lookup error"
msgstr "Dll 注入失败: 符号查找错误"

#: cefuncproc.rscefpicantgettheprocesslistyouarepropablyuseinwindowsntetc
msgctxt "cefuncproc.rscefpicantgettheprocesslistyouarepropablyuseinwindowsntetc"
msgid "I can't get the process list. You are propably using windows NT. Use the window list instead!"
msgstr "无法获取进程列表, 你使用的或许是 windows NT, 现在改用窗口列表代替!"

#: cefuncproc.rsclear
msgctxt "cefuncproc.rsclear"
msgid "Clear"
msgstr "清除"

#: cefuncproc.rsctrl
msgctxt "cefuncproc.rsctrl"
msgid "Ctrl"
msgstr "Ctrl"

#: cefuncproc.rsdeletekey
msgid "Delete "
msgstr "Delete "

#: cefuncproc.rsdownarrow
msgctxt "cefuncproc.rsdownarrow"
msgid "Down Arrow"
msgstr "Down Arrow"

#: cefuncproc.rsend
msgctxt "cefuncproc.rsend"
msgid "End"
msgstr "End"

#: cefuncproc.rsenter
msgctxt "cefuncproc.rsenter"
msgid "Enter"
msgstr "Enter"

#: cefuncproc.rsesc
msgctxt "cefuncproc.rsesc"
msgid "Esc"
msgstr "Esc"

#: cefuncproc.rsexecute
msgctxt "cefuncproc.rsexecute"
msgid "Execute"
msgstr "Execute"

#: cefuncproc.rsfailedexecutingthefunctionofthedll
msgid "Failed executing the function of the dll"
msgstr "执行 DLL 函数失败"

#: cefuncproc.rsfailedinjectingthedll
msgid "Failed injecting the DLL"
msgstr "注入 DLL 失败"

#: cefuncproc.rsfailedtoallocatememory
msgid "Failed to allocate memory"
msgstr "无法分配内存"

#: cefuncproc.rsfailedtoexecutethedllloader
msgid "Failed to execute the dll loader"
msgstr "可执行程序载入 DLL 失败"

#: cefuncproc.rsfailedtoinjectthedllloader
msgid "Failed to inject the dll loader"
msgstr "程序加载注入的 DLL 失败"

#: cefuncproc.rsgetprocaddressnotfound
msgid "GetProcAddress not found"
msgstr "GetProcAddress 未找到"

#: cefuncproc.rshelp
msgctxt "cefuncproc.rshelp"
msgid "Help"
msgstr "Help"

#: cefuncproc.rshome
msgctxt "cefuncproc.rshome"
msgid "Home"
msgstr "Home"

#: cefuncproc.rsicantgettheprocesslistyouarepropablyusingwindowsnt
msgctxt "cefuncproc.rsicantgettheprocesslistyouarepropablyusingwindowsnt"
msgid "I can't get the process list. You are propably using windows NT. Use the window list instead!"
msgstr "无法获取进程列表, 你使用的或许是 windows NT, 现在改用窗口列表代替!"

#: cefuncproc.rsinsert
msgctxt "cefuncproc.rsinsert"
msgid "Insert"
msgstr "Insert"

#: cefuncproc.rsleftarrow
msgctxt "cefuncproc.rsleftarrow"
msgid "Left Arrow"
msgstr "Left Arrow"

#: cefuncproc.rsleftmb
msgid "Left MB"
msgstr "Left MB"

#: cefuncproc.rsleftwindowskey
msgctxt "cefuncproc.rsleftwindowskey"
msgid "Left Windows key"
msgstr "Left Windows key"

#: cefuncproc.rsloadlibraryanotfound
msgid "LoadLibraryA not found"
msgstr "LoadLibraryA 未找到"

#: cefuncproc.rsmiddlemb
msgid "Middle MB"
msgstr "Middle MB"

#: cefuncproc.rsnokernel32dllloaded
msgid "No kernel32.dll loaded"
msgstr "未加载 kernel32.dll"

#: cefuncproc.rsnotconvertable
msgid "Not convertable"
msgstr "无 convertable"

#: cefuncproc.rsnotsupportedinthisversion
msgid "not supported in this version"
msgstr "不支持此版本"

#: cefuncproc.rsnumeric
msgctxt "cefuncproc.rsnumeric"
msgid "numeric"
msgstr "numeric"

#: cefuncproc.rsnumlock
msgctxt "cefuncproc.rsnumlock"
msgid "Num Lock"
msgstr "Num Lock"

#: cefuncproc.rspagedown
msgctxt "cefuncproc.rspagedown"
msgid "Page Down"
msgstr "Page Down"

#: cefuncproc.rspageup
msgctxt "cefuncproc.rspageup"
msgid "Page Up"
msgstr "Page Up"

#: cefuncproc.rspause
msgctxt "cefuncproc.rspause"
msgid "Pause"
msgstr "Pause"

#: cefuncproc.rsposition
msgid " Position"
msgstr " Position"

#: cefuncproc.rsprint
msgctxt "cefuncproc.rsprint"
msgid "Print"
msgstr "Print"

#: cefuncproc.rsprintscreen
msgctxt "cefuncproc.rsprintscreen"
msgid "Print Screen"
msgstr "Print Screen"

#: cefuncproc.rsrightarrow
msgctxt "cefuncproc.rsrightarrow"
msgid "Right Arrow"
msgstr "Right Arrow"

#: cefuncproc.rsrightmb
msgid "Right MB"
msgstr "Right MB"

#: cefuncproc.rsrightwindowskey
msgctxt "cefuncproc.rsrightwindowskey"
msgid "Right Windows key"
msgstr "Right Windows key"

#: cefuncproc.rsscrolllock
msgctxt "cefuncproc.rsscrolllock"
msgid "Scroll Lock"
msgstr "Scroll Lock"

#: cefuncproc.rsselect
msgctxt "cefuncproc.rsselect"
msgid "Select"
msgstr "Select"

#: cefuncproc.rsseparator
msgctxt "cefuncproc.rsseparator"
msgid "Separator"
msgstr "Separator"

#: cefuncproc.rsshift
msgctxt "cefuncproc.rsshift"
msgid "Shift"
msgstr "Shift"

#: cefuncproc.rsspacebar
msgctxt "cefuncproc.rsspacebar"
msgid "Space bar"
msgstr "Space bar"

#: cefuncproc.rstab
msgctxt "cefuncproc.rstab"
msgid "Tab"
msgstr "Tab"

#: cefuncproc.rstheinjectionthreadtooklongerthan10secondstoexecute
msgid "The injection thread took longer than 10 seconds to execute. Injection routine not freed"
msgstr "注入线程需要较长的时间(超过10秒来)来执行, 注入程序未释放"

#: cefuncproc.rsunknownerrorduringinjection
msgid "Unknown error during injection"
msgstr "在注入过程中发生未知错误"

#: cefuncproc.rsuparrow
msgctxt "cefuncproc.rsuparrow"
msgid "Up Arrow"
msgstr "Up Arrow"

#: ceguicomponents.rsinvalidformdata
msgid "Invalid formdata"
msgstr "无效的formdata"

#: celazysocket.rsdisconnectedwhilereceivingdata
msgid "Disconnected while receiving data"
msgstr "接收数据时断开连接"

#: celazysocket.rsdisconnectedwhilesendingdata
msgid "Disconnected while sending data"
msgstr "发送数据时断开连接"

#: celazysocket.rserrorwhilereceivingdata
msgid "Error while receiving data: "
msgstr "接收数据时出错: "

#: celazysocket.rserrorwhilesendingdata
msgid "Error while sending data: "
msgstr "发送数据时出错: "

#: celazysocket.rstimeoutwhilereceivingdata
msgid "Timeout while receiving data"
msgstr "接收数据超时"

#: celazysocket.rstimeoutwhilesendingdata
msgid "Timeout while sending data"
msgstr "发送数据超时"

#: celazysocket.rswhoopdeedoo
msgid "Whoopdeedoo"
msgstr "Whoopdeedoo"

#: changeoffsetunit.rsthisisnotanvalidvalue
msgctxt "changeoffsetunit.rsthisisnotanvalidvalue"
msgid "This is not an valid value"
msgstr "不是一个有效的数值"

#: customtypehandler.rsacustomfunctiontypewithnamealreadyexists
msgid "A custom function type with name %s already exists"
msgstr "已存在名称为 %s 的自定义函数类型"

#: customtypehandler.rsacustomtypewithnamealreadyexists
msgid "A custom type with name %s already exists"
msgstr "已存在名称为 %s 的自定义类型"

#: customtypehandler.rsbytesizeis0
msgid "bytesize is 0"
msgstr "字节大小为 0"

#: customtypehandler.rscthinvalidnumberofparameters
msgid "Invalid number of parameters"
msgstr "无效的参数数目"

#: customtypehandler.rscthparameter3isnotavalidfunction
msgid "Parameter 3 is not a valid function"
msgstr "参数 3 不是一个有效的函数"

#: customtypehandler.rscthparameter4isnotavalidfunction
msgid "Parameter 4 is not a valid function"
msgstr "参数 4 不是一个有效的函数"

#: customtypehandler.rsfailurecreatingluaobject
msgid "Failure creating lua object"
msgstr "创建 lua 对象失败"

#: customtypehandler.rsinvalidfunctiontypename
msgid "invalid functiontypename"
msgstr "无效的函数类型名称"

#: customtypehandler.rsinvalidtypename
msgid "invalid typename"
msgstr "无效的类型名称"

#: customtypehandler.rsonlyreturntypenamebytecountandfunctiontypename
msgid "Only return typename, bytecount and functiontypename"
msgstr "只能返回类型名称、字节总数和函数类型名称"

#: customtypehandler.rsundefinederror
msgctxt "customtypehandler.rsundefinederror"
msgid "Undefined error"
msgstr "未定义的错误"

#: d3dhookunit.rsd3dhookfailuretomapthesharedmemoryobject
msgid "D3DHook: Failure to map the shared memory object"
msgstr "D3DHook: 无法映射对象到共享内存"

#: d3dhookunit.rsd3dhookfailuretoopenthesharedmemoryobject
msgid "D3DHook: Failure to open the shared memory object"
msgstr "D3DHook: 共享内存无法打开对象"

#: d3dhookunit.rsthed3dhookobjecthasnotbeencreatedyet
msgid "The d3dhook object has not been created yet"
msgstr "D3DHook 对象尚未创建"

#: dbk32functions.rsapcrules
msgid "APC rules"
msgstr "APC 规则"

#: dbk32functions.rscouldnotlaunchdbvm
msgid "Could not launch DBVM: The Intel-VT feature has been disabled in your BIOS"
msgstr "无法运行 DBVM: 你的 BIOS禁用了 Intel-VT 功能"

#: dbk32functions.rsdbk32error
msgid "DBK32 error"
msgstr "DBK32 错误"

#: dbk32functions.rsdbkerror
msgid "DBK Error"
msgstr "DBK 错误"

#: dbk32functions.rsdrivererror
msgid "Driver error"
msgstr "驱动错误"

#: dbk32functions.rsfailuretoconfigurethedriver
msgid "Failure to configure the driver"
msgstr "配置驱动失败"

#: dbk32functions.rsinvalidmsraddress
msgid "Invalid MSR address:"
msgstr "MSR 地址无效"

#: dbk32functions.rsmsrsareunavailable
msgid "msrs are unavailable"
msgstr "MSRS 不可用"

#: dbk32functions.rspleaserebootandpressf8duringboot
msgid ""
"Please reboot and press F8 during boot. Then choose \"allow unsigned drivers\". \n"
"Alternatively you could sign the driver yourself.\n"
"Just buy yourself a class 3 business signing certificate and sign the driver. Then you'll never have to reboot again to use this driver\n"
msgstr "请重新启动系统并开机按\"F8\"进入. 然后选择\"禁用驱动程序签名强制\". \n或者你可以购买驱动数字证书. \n自己给驱动签名. 然后你将永远不需要重新启动系统使用这个驱动 \n"

#: dbk32functions.rspleaserunthe64bitversionofce
msgid "Please run the 64-bit version of Cheat Engine"
msgstr "请运行64位版本的Cheat Engine"

#: dbk32functions.rsthedrivercouldntbeopened
msgid "The driver couldn't be opened! It's not loaded or not responding. Luckely you are running dbvm so it's not a total waste. Do you wish to force load the driver?"
msgstr "无法打开驱动!驱动没有加载或无响应. Luckely you are running dbvm so it's not a total waste. 你想要强制加载驱动吗？"

#: dbk32functions.rsthedrivercouldntbeopenedtryagain
msgid "The driver couldn't be opened! It's not loaded or not responding. I recommend to reboot your system and try again (If you're on 64-bit windows, you might want to use dbvm)"
msgstr "无法打开驱动!驱动没有加载或无响应. 我建议您重新启动您的系统并再次尝试(如果你的Windows是64位, 你可能需要使用DBVM)"

#: dbk32functions.rsthedriverfailedtosuccessfullyinitialize
msgid "The driver failed to successfully initialize. Some functions may not completely work"
msgstr "驱动未能成功初始化. 可能会有些功能无法正常工作"

#: dbk32functions.rsthedriverthatiscurrentlyloaded
msgid "The driver that is currently loaded belongs to a different version of Cheat Engine. Please unload this driver or reboot."
msgstr "当前加载的驱动属于其他版本的 Cheat Engine. 请卸载这个驱动或重新启动系统."

#: dbk32functions.rstheservicecouldntgetopened
msgid "The service couldn't get opened and also couldn't get created. Check if you have the needed rights to create a service, or call your system admin (Who'll probably beat you up for even trying this). Untill this is fixed you won't be able to make use of the enhancements the driver gives you"
msgstr "无法打开也无法创建此服务. 请与这台机器的管理员联系以查明您是否拥有所需要的权限 (Who'll probably beat you up for even trying this). 在此之前,您将无法使用驱动提供的增强功能"

#: dbk32functions.rsyouaremissingthedriver
msgid "You are missing the driver. Try reinstalling cheat engine, and try to disable your anti-virus before doing so."
msgstr "你的驱动已丢失. 请重新安装 Cheat Engine, 并且在安装之前禁用杀毒软件."

#: dbk64secondaryloader.rserrorwhiletryingtoloadthedriveratpart
msgid "Error while trying to load the driver at part "
msgstr "尝试加载驱动时发生错误 "

#: dbk64secondaryloader.rsmoduleloaderdailedtomdbk64systomemoryap
msgid "ModuleLoader failed to map dbk64.sys to memory"
msgstr "ModuleLoader 映射 dbk64.sys 到内存失败"

#: dbk64secondaryloader.rsseemslikedbvmisntloadedafterall
msgid "seems like dbvm isn't loaded after all"
msgstr "DBVM 似乎未能完全加载"

#: dbk64secondaryloader.rssuccessthedriverhasbeenloaded
msgid "Success. The driver has been loaded thanks to dbvm"
msgstr "成功. 已加载 dbvm 驱动"

#: dbk64secondaryloader.rsthedriverfailedtoinitialize
msgid "The driver failed to initialize"
msgstr "无法初始化驱动"

#: dbvmloadmanual.rschecking
msgid "<Checking>"
msgstr "<检查中>"

#: dbvmloadmanual.rscpu
msgid "CPU "
msgstr "CPU "

#: dbvmloadmanual.rscpualreadyrunningdbvm
msgid "This cpu is already running DBVM"
msgstr "此 CPU 已在运行 DBVM"

#: dbvmloadmanual.rsloaded
msgid "Loaded:"
msgstr "已加载:"

#: dbvmloadmanual.rsnotloaded
msgid "Not loaded"
msgstr "未加载"

#: debugeventhandler.rsdebughandleaccessviolationdebugeventnow
msgid "Debug HandleAccessViolationDebugEvent now"
msgstr "现在调试 HandleAccessViolationDebugEvent"

#: debugeventhandler.rsspecialcase
msgid "Special case"
msgstr "特例"

#: debuggertypedefinitions.rsbreak
msgctxt "debuggertypedefinitions.rsbreak"
msgid "Break"
msgstr "中断"

#: debuggertypedefinitions.rsbreakandtrace
msgctxt "debuggertypedefinitions.rsbreakandtrace"
msgid "Break and trace"
msgstr "中断和跟踪"

#: debuggertypedefinitions.rschangereg
msgid "Change reg"
msgstr "更改注册"

#: debuggertypedefinitions.rsexceptionbreakpoint
msgid "Exception Breakpoint"
msgstr "异常断点"

#: debuggertypedefinitions.rsfindcode
msgid "Find code"
msgstr "查找代码"

#: debuggertypedefinitions.rsfindcodeaccess
msgid "Find code access"
msgstr "查找访问码"

#: debuggertypedefinitions.rshardwarebreakpoint
msgid "Hardware Breakpoint"
msgstr "硬件断点"

#: debuggertypedefinitions.rsonexecute
msgid "On Execute"
msgstr "可执行"

#: debuggertypedefinitions.rsonreadwrite
msgid "On Read/Write"
msgstr "读/写"

#: debuggertypedefinitions.rsonwrite
msgid "On Write"
msgstr "写入"

#: debuggertypedefinitions.rssoftwarebreakpoint
msgid "Software Breakpoint"
msgstr "软件断点"

#: debughelper.rsaddbreakpointaninvaliddebugregisterisused
msgid "AddBreakpoint: An invalid debug register is used"
msgstr "添加断点: 使用了一个无效的调试寄存器"

#: debughelper.rsall4debugregistersarecurrentlyusedupfreeoneandtrya
msgid "All debug registers are currently used up. Free one and try again"
msgstr "目前调试寄存器都已使用. 请释放1个后再尝试"

#: debughelper.rsalldebugregistersareusedup
msgid "All debug registers are used up"
msgstr "所有调试寄存器都已被使用"

#: debughelper.rsalldebugregistersareusedupdoyouwanttouseasoftwarebp
msgid "All debug registers are used up. Do you want to use a software breakpoint?"
msgstr "所有调试寄存器都已被使用. 你想使用软件断点吗?"

#: debughelper.rsbreakpointerror
msgid "Breakpoint error:"
msgstr "断点错误:"

#: debughelper.rscalledfromanunexpectedthread
msgid "Called from an unexpected thread"
msgstr "异常线程调用"

#: debughelper.rscalledfromdebuggerthread
msgid "Called from debugger thread"
msgstr "从调试器线程调用"

#: debughelper.rscalledfrommainthread
msgid "Called from main thread"
msgstr "从主线程调用"

#: debughelper.rscreateprocessfailed
msgid "CreateProcess failed:%s"
msgstr "创建进程失败: %s"

#: debughelper.rsdebuggerattachtimeout
msgid "Debugger attach timeout"
msgstr "调试器附加超时"

#: debughelper.rsdebuggercrash
msgid "Debugger Crash"
msgstr "调试器崩溃"

#: debughelper.rsdebuggerfailedtoattach
msgid "Debugger failed to attach"
msgstr "调试器附加失败"

#: debughelper.rsdebuggerinterfacedoesnotsupportsoftwarebreakpoints
msgid "Debugger interface %s does not support software breakpoints"
msgstr "调试器接口 %s 不支持软件断点"

#: debughelper.rsdebuggerthreadisatpoint
msgid "debuggerthread is at point "
msgstr "debuggerthread 是断点 "

#: debughelper.rslastlocation
msgid " (Last location:"
msgstr " (最后的位置:"

#: debughelper.rsno
msgctxt "debughelper.rsno"
msgid "No"
msgstr "否"

#: debughelper.rsnoform
msgid "No form"
msgstr "不从"

#: debughelper.rsonlythedebuggerthreadisallowedtosetthecurrentthread
msgid "Only the debugger thread is allowed to set the current thread"
msgstr "仅允许调试器线程设定当前线程"

#: debughelper.rsoutofhwbreakpoints
msgid "All debug registers are used up and this debugger interface does not support software Breakpoints. Remove some and try again"
msgstr "所有调试寄存器都已经使用, 此调试器接口不支持软件断点. 删除一些, 再试一次"

#: debughelper.rsseconds
msgid " seconds"
msgstr " 秒"

#: debughelper.rsthedebuggerattachhastimedout
msgid "The debugger attach has timed out. This could indicate that the target has crashed, or that your system is just slow. Do you wish to wait another "
msgstr "调试器附加已超时. 这意味着目标可能已崩溃, 或者你的系统运行缓慢. 你想要等待吗 预计 "

#: debughelper.rsthefollowingopcodesaccessed
msgid "The following opcodes accessed %s"
msgstr "下列操作码访问了 %s"

#: debughelper.rsthefollowingopcodeswriteto
msgid "The following opcodes write to %s"
msgstr "下列操作码写入到 %s"

#: debughelper.rsthisdebuggerinterfacedoesntsupportbreakonentryyet
msgid "This debugger interface :'%s' doesn't support Break On Entry yet"
msgstr "此调试器接口: '%s' 尚不支持中断于入口点"

#: debughelper.rsunreadableaddress
msgid "Unreadable address"
msgstr "无法读取地址"

#: debughelper.rsunreadablememoryunabletosetsoftwarebreakpoint
msgid "Unreadable memory. Unable to set software breakpoint"
msgstr "无法读取内存. 无法设置软件断点"

#: debughelper.rsyes
msgctxt "debughelper.rsyes"
msgid "Yes"
msgstr "是"

#: disassemblerviewlinesunit.rscall
msgid "(Call)"
msgstr "(调用)"

#: disassemblerviewlinesunit.rscon
msgid "(Conditional)"
msgstr "(条件)"

#: disassemblerviewlinesunit.rsinvaliddisassembly
msgid "Invalid disassembly"
msgstr "无效的反汇编"

#: disassemblerviewlinesunit.rsmemory
msgid "(Code/Data)"
msgstr "(代码/数据)"

#: disassemblerviewlinesunit.rsun
msgid "(Unconditional)"
msgstr "(无条件)"

#: disassemblerviewunit.rsaddress
msgctxt "disassemblerviewunit.rsaddress"
msgid "Address"
msgstr "地址"

#: disassemblerviewunit.rsbytes
msgctxt "disassemblerviewunit.rsbytes"
msgid "字节"
msgstr "字节"

#: disassemblerviewunit.rscomment
msgctxt "disassemblerviewunit.rscomment"
msgid "Comment"
msgstr "注释"

#: disassemblerviewunit.rsopcode
msgid "Opcode"
msgstr "操作码"

#: disassemblerviewunit.rspleaseopenaprocessfirst
msgid "Please open a process first"
msgstr "请先打开一个进程"

#: disassemblerviewunit.rssymbolsarebeingloaded
msgid "Symbols are being loaded (%d %%)"
msgstr "正在加载符号表 (%d %%)"

#: dissectcodethread.rsinvaliddissectcodefile
msgid "Invalid dissect code file"
msgstr "无效的解析代码文件"

#: dissectcodethread.rstdissectcodethreadgetcalllistcalledwithanonemptylist
msgid "TDissectCodeThread.getCallList called with a non empty list"
msgstr "TDissectCodeThread 列表不能为空"

#: dissectcodeunit.rsdone
msgctxt "dissectcodeunit.rsdone"
msgid "done"
msgstr "完成"

#: dissectcodeunit.rspleaseselectsomethingtoscan
msgid "Please select something to scan"
msgstr "请选择要扫描的内容"

#: dissectcodeunit.rsstart
msgctxt "dissectcodeunit.rsstart"
msgid "Start"
msgstr "开始"

#: dissectcodeunit.rsstop
msgctxt "dissectcodeunit.rsstop"
msgid "Stop"
msgstr "停止"

#: dotnettypes.rsdnt2byte
msgid "2 Byte"
msgstr "2 字节"

#: dotnettypes.rsdnt2bytesigned
msgid "2 Byte (Signed)"
msgstr "2 字节 (有符号)"

#: dotnettypes.rsdnt4byte
msgctxt "dotnettypes.rsdnt4byte"
msgid "4 Bytes"
msgstr "4 字节"

#: dotnettypes.rsdnt4bytesigned
msgid "4 Byte (Signed)"
msgstr "4 Byte (有符号)"

#: dotnettypes.rsdnt8byte
msgid "8 Byte"
msgstr "8 字节"

#: dotnettypes.rsdnt8bytesigned
msgid "8 Byte (Signed)"
msgstr "8 字节 (有符号)"

#: dotnettypes.rsdntarray
msgid "Array"
msgstr "数组"

#: dotnettypes.rsdntbool
msgid "bool"
msgstr "布尔型"

#: dotnettypes.rsdntbyte
msgctxt "dotnettypes.rsdntbyte"
msgid "Byte"
msgstr "字节"

#: dotnettypes.rsdntbytesigned
msgid "Byte (Signed)"
msgstr "字节 (有符号)"

#: dotnettypes.rsdntchar
msgid "char"
msgstr "字符型"

#: dotnettypes.rsdntclass
msgid "Class"
msgstr "类"

#: dotnettypes.rsdntcmod_opt
msgid "CMOD_OPT"
msgstr "CMOD_OPT"

#: dotnettypes.rsdntcmod_reqd
msgid "CMOD_REQD"
msgstr "CMOD_REQD"

#: dotnettypes.rsdntdouble
msgctxt "dotnettypes.rsdntdouble"
msgid "Double"
msgstr "双浮点"

#: dotnettypes.rsdntend
msgid "END"
msgstr "END"

#: dotnettypes.rsdntfloat
msgctxt "dotnettypes.rsdntfloat"
msgid "Float"
msgstr "单浮点"

#: dotnettypes.rsdntfunctionpointer
msgid "Function Pointer"
msgstr "函数指针"

#: dotnettypes.rsdntgenericinst
msgid "GenericInst"
msgstr "GenericInst"

#: dotnettypes.rsdntinternal
msgid "Internal"
msgstr "内置"

#: dotnettypes.rsdntmax
msgid "MAX"
msgstr "MAX"

#: dotnettypes.rsdntmodifier
msgid "Modifier"
msgstr "修饰符"

#: dotnettypes.rsdntmvar
msgid "MVar"
msgstr "MVar"

#: dotnettypes.rsdntobject
msgid "Object"
msgstr "对象"

#: dotnettypes.rsdntpinned
msgid "Pinned"
msgstr "Pinned"

#: dotnettypes.rsdntpointer
msgctxt "dotnettypes.rsdntpointer"
msgid "Pointer"
msgstr "指针"

#: dotnettypes.rsdntreference
msgid "Reference"
msgstr "引用"

#: dotnettypes.rsdntsentinel
msgid "Sentinel"
msgstr "标记"

#: dotnettypes.rsdntsigned
msgctxt "dotnettypes.rsdntsigned"
msgid "Signed"
msgstr "有符号"

#: dotnettypes.rsdntstring
msgctxt "dotnettypes.rsdntstring"
msgid "String"
msgstr "字符串"

#: dotnettypes.rsdntszarray
msgid "SZ Array"
msgstr "SZ 数组"

#: dotnettypes.rsdnttypedbyref
msgid "Typed By Ref"
msgstr "Ref 类型"

#: dotnettypes.rsdntunsigned
msgid "Unsigned"
msgstr "无符号"

#: dotnettypes.rsdntvaluetype
msgid "ValueType"
msgstr "ValueType"

#: dotnettypes.rsdntvar
msgid "Var"
msgstr "Var"

#: dotnettypes.rsdntvoid
msgid "void"
msgstr "void"

#: driverlist.rsdlnothingfound
msgctxt "driverlist.rsdlnothingfound"
msgid "nothing found"
msgstr "什么都没找到"

#: extratrainercomponents.rsundefineddescription
msgid "undefined description"
msgstr "未定义描述"

#: extratrainercomponents.rsundefinedhotkey
msgid "undefined hotkey"
msgstr "未定义热键"

#: feces.rsbcryptcouldnotbeused
msgid "bcrypt could not be used"
msgstr "无法使用 bcrypt(J跨平台加密工具)"

#: feces.rscheatenginesignaturefiles
msgid "Cheat engine signature files"
msgstr "Cheat engine 签名文件"

#: feces.rscouldnotopenthealgorithmprovider
msgid "Could not open the algorithm provider"
msgstr "无法打开数字签名算法提供者"

#: feces.rsfailedcreatinghasalgorithmprovider
msgctxt "feces.rsfailedcreatinghasalgorithmprovider"
msgid "Failed creating has algorithm provider"
msgstr "创建数字签名算法提供者失败"

#: feces.rsfailedcreatinghasalgorithmprovider2
msgctxt "feces.rsfailedcreatinghasalgorithmprovider2"
msgid "Failed creating has algorithm provider"
msgstr "创建数字签名算法提供者失败"

#: feces.rsfailedcreatinghash
msgid "Failed creating hash"
msgstr "创建签名散列值失败"

#: feces.rsfailedcreatinghash2
msgid "Failed creating hash 2"
msgstr "创建签名散列值失败 2"

#: feces.rsfailedgettingtheobjectlength
msgid "Failed getting the object length"
msgstr "获取对象长度失败"

#: feces.rsfailedhashingtable
msgid "Failed hashing table"
msgstr "无效的哈希表"

#: feces.rsfailedhashingtable2
msgid "Failed hashing table 2"
msgstr "无效的哈希表 2"

#: feces.rsfailedtofinishthehash
msgid "Failed to finish the hash"
msgstr "完成散列值失败"

#: feces.rsfailedtofinishthehash2
msgid "Failed to finish the hash 2"
msgstr "完成散列值失败 2"

#: feces.rsfailedtogethashlength
msgid "Failed to get hashlength"
msgstr "无法获取散列值长度"

#: feces.rsfailedtogethashlength2
msgid "Failed to get hashlength 2"
msgstr "无法获取散列值长度 2"

#: feces.rsfailedtogetsignaturesize
msgid "Failed to get the signature size"
msgstr "无法获取签名大小"

#: feces.rsfailedtoloadcheatenginepublickey
msgid "Failed to load cheat engine public key"
msgstr "无法加载cheat engine公匙"

#: feces.rsfailedtoloadprivatekey
msgid "Failed to load private key"
msgstr "无法加载私匙"

#: feces.rsfailedtoloadthetablepublickey
msgid "Failed to load the table public key"
msgstr "无法加载公匙表"

#: feces.rsinvalidpublickey
msgid "The provided public key is invalid(Not signed by the Cheat Engine guy). Remove the signature section to load this table"
msgstr "提供的公匙是无效的(不是由 Cheat Engine 签名的). 移除签名部分以便加载此表单"

#: feces.rsnopublickey
msgid "This table's signature does not contain a PublicKey element"
msgstr "此表单的签名不包括公匙元素"

#: feces.rsnosignedhash
msgid "This table's signature does not contain a SignedHash element"
msgstr "此表单的签名不包括SignedHash元素"

#: feces.rsselectyourcheatenginesignaturefile
msgid "Select your cheat engine signature file"
msgstr "请选择你的 cheat engine 签名文件"

#: feces.rsthistablehasbeenmodified
msgid "This table has been modified. To load this table, remove the signature part with an editor (And check the file for suspicious things while you're at it)"
msgstr "表单已被修改. 加载此表单, 并使用文本编辑器删除签名 (当操作的时候请检查文件是否有可疑之处)"

#: fileaccess.rsnogetnamedsecurityinfo
msgid "no GetNamedSecurityInfo"
msgstr "无 GetNamedSecurityInfo"

#: fileaccess.rsnogetsecurityinfo
msgid "no GetSecurityInfo"
msgstr "无 GetSecurityInfo"

#: fileaccess.rsnosetentriesinacl
msgid "no SetEntriesInAcl"
msgstr "无 SetEntriesInAcl"

#: fileaccess.rsnosetnamedsecurityinfo
msgid "no SetNamedSecurityInfo"
msgstr "无 SetNamedSecurityInfo"

#: filemapping.rsdoesnotexist
msgid "%s does not exist"
msgstr "%s 不存在"

#: filemapping.rsfailedcreatingaproperview
msgid "Failed creating a proper view"
msgstr "创建适当的视图失败"

#: filemapping.rsmappingfailed
msgid "Mapping failed"
msgstr "映射失败"

#: findwindowunit.rsnothingfound
msgid "Nothing found"
msgstr "什么都没找到"

#: findwindowunit.rsthespecifiedrangeisinvalid
msgid "The specified range is invalid"
msgstr "指定的范围无效"

#: formaddresschangeunit.rsacaddoffset
msgid "Add Offset"
msgstr "添加偏移"

#: formaddresschangeunit.rsacremoveoffset
msgid "Remove Offset"
msgstr "移除偏移"

#: formaddresschangeunit.rsaddressofpointer
msgid "Address of pointer"
msgstr "指针地址"

#: formaddresschangeunit.rsfillinthenrofbytesafterthelocationthepointerpoints
msgid "Fill in the nr. of bytes after the location the pointer points to"
msgstr "填入字节之后指针所指向的地址"

#: formaddresschangeunit.rsisnotavalidoffset
msgid "%s is not a valid offset"
msgstr "%s 偏移无效"

#: formaddresschangeunit.rsnotalloffsetshavebeenfilledin
msgid "Not all offsets have been filled in"
msgstr "不是所有的偏移都已填写"

#: formaddresschangeunit.rsoffsethex
msgid "Offset (Hex)"
msgstr "偏移 (Hex)"

#: formaddresschangeunit.rsresultofnextpointer
msgid "Result of next pointer"
msgstr "下一个指针结果"

#: formaddresschangeunit.rstheoffsetyouchosebringsitto
msgid "The offset you chose brings it to"
msgstr "你选择的偏移计算出的地址为"

#: formaddresschangeunit.rsthispointerpointstoaddress
msgid "This pointer points to address"
msgstr "这个指针指向的地址"

#: formaddtocodelist.rspleasefillinavalidfromaddress
msgctxt "formaddtocodelist.rspleasefillinavalidfromaddress"
msgid "Please fill in a valid 'From' address"
msgstr "请填写有效的\"从\"地址"

#: formaddtocodelist.rspleasefillinavalidtoaddress
msgctxt "formaddtocodelist.rspleasefillinavalidtoaddress"
msgid "Please fill in a valid 'To' address"
msgstr "请填写有效的\"至\"地址"

#: formaddtocodelist.rsregion
msgid "Region"
msgstr "区域"

#: formaddtocodelist.rsregiontoadd
msgctxt "formaddtocodelist.rsregiontoadd"
msgid "Region to add"
msgstr "添加区域"

#: formchangedaddresses.rschangedaddressesby
msgid "Changed Addresses by %x"
msgstr "变动的地址为 %x"

#: formchangedaddresses.rsclose
msgctxt "formchangedaddresses.rsclose"
msgid "Close"
msgstr "关闭"

#: formchangedaddresses.rsnodescription
msgid "No Description"
msgstr "无描述"

#: formchangedaddresses.rsstop
msgctxt "formchangedaddresses.rsstop"
msgid "Stop"
msgstr "停止"

#: formdesignerunit.rsformdesignercaption
msgctxt "formdesignerunit.rsformdesignercaption"
msgid "Form Designer"
msgstr "窗体设计器"

#: formdesignerunit.rsformfilesfrmfrm
msgid "Form files(*.frm)|*.FRM"
msgstr "窗体文件(*.frm)|*.FRM"

#: formdesignerunit.rsformfileslfmlfm
msgid "Form files(*.lfm)|*.LFM"
msgstr "窗体文件(*.lfm)|*.LFM"

#: formdesignerunit.rsinvalidobject
msgid "{Invalid object}"
msgstr "{无效对象}"

#: formdesignerunit.rsshowcheckboxesforboolean
msgid "Show checkboxes for boolean"
msgstr "以布尔值复选框显示"

#: formdifferentbitsizeunit.rsthelasttimeyouscannedthenumberofbitswasandnowitisp
msgid "The last time you scanned the number of bits was %s and now it is %s. Please tell me how and how much i must change the bit offset to scan successfully. (Left+right arrow keys move the bits)"
msgstr "最后一次搜索的字节位数是 %s 而现在是 %s. 请指出应该更改多少位偏移以便成功搜索. (左/右箭头移位)"

#: formdifferentbitsizeunit.rswelldoneyoufoundaeasteregg
msgid "Well done, you found a easter egg!"
msgstr "你找到了复活节彩蛋!"

#: formfoundcodelistextraunit.rsprobablebasepointer
msgid "Probable base pointer =%s"
msgstr "指针基址可能是 =%s"

#: formfoundcodelistextraunit.rsthevalueofthepointerneededtofindthisaddressisproba
msgid "The value of the pointer needed to find this address is probably %s"
msgstr "\n您要查找的该地址的指针数值可能是 %s\n\n\n\n\n"

#: formhotkeyunit.rsalt
msgctxt "formhotkeyunit.rsalt"
msgid "Alt"
msgstr "Alt"

#: formhotkeyunit.rsapplicationskey
msgctxt "formhotkeyunit.rsapplicationskey"
msgid "Applications key"
msgstr "Applications key"

#: formhotkeyunit.rsbackspace
msgctxt "formhotkeyunit.rsbackspace"
msgid "Backspace"
msgstr "Backspace"

#: formhotkeyunit.rscapslock
msgctxt "formhotkeyunit.rscapslock"
msgid "Caps Lock"
msgstr "Caps Lock"

#: formhotkeyunit.rsclear
msgctxt "formhotkeyunit.rsclear"
msgid "Clear"
msgstr "清除"

#: formhotkeyunit.rsctrl
msgctxt "formhotkeyunit.rsctrl"
msgid "Ctrl"
msgstr "Ctrl"

#: formhotkeyunit.rsdelete
msgctxt "formhotkeyunit.rsdelete"
msgid "Delete"
msgstr "Delete"

#: formhotkeyunit.rsdownarrow
msgctxt "formhotkeyunit.rsdownarrow"
msgid "Down Arrow"
msgstr "Down Arrow"

#: formhotkeyunit.rsend
msgctxt "formhotkeyunit.rsend"
msgid "End"
msgstr "End"

#: formhotkeyunit.rsenter
msgctxt "formhotkeyunit.rsenter"
msgid "Enter"
msgstr "Enter"

#: formhotkeyunit.rsesc
msgctxt "formhotkeyunit.rsesc"
msgid "Esc"
msgstr "Esc"

#: formhotkeyunit.rsexecute
msgctxt "formhotkeyunit.rsexecute"
msgid "Execute"
msgstr "Execute"

#: formhotkeyunit.rshelp
msgctxt "formhotkeyunit.rshelp"
msgid "Help"
msgstr "Help"

#: formhotkeyunit.rshome
msgctxt "formhotkeyunit.rshome"
msgid "Home"
msgstr "Home"

#: formhotkeyunit.rsinsert
msgctxt "formhotkeyunit.rsinsert"
msgid "Insert"
msgstr "Insert"

#: formhotkeyunit.rsleftarrow
msgctxt "formhotkeyunit.rsleftarrow"
msgid "Left Arrow"
msgstr "Left Arrow"

#: formhotkeyunit.rsleftwindowskey
msgctxt "formhotkeyunit.rsleftwindowskey"
msgid "Left Windows key"
msgstr "Left Windows key"

#: formhotkeyunit.rsnumeric
msgctxt "formhotkeyunit.rsnumeric"
msgid "numeric"
msgstr "numeric"

#: formhotkeyunit.rsnumlock
msgctxt "formhotkeyunit.rsnumlock"
msgid "Num Lock"
msgstr "Num Lock"

#: formhotkeyunit.rspagedown
msgctxt "formhotkeyunit.rspagedown"
msgid "Page Down"
msgstr "Page Down"

#: formhotkeyunit.rspageup
msgctxt "formhotkeyunit.rspageup"
msgid "Page Up"
msgstr "Page Up"

#: formhotkeyunit.rspause
msgctxt "formhotkeyunit.rspause"
msgid "Pause"
msgstr "Pause"

#: formhotkeyunit.rsprint
msgctxt "formhotkeyunit.rsprint"
msgid "Print"
msgstr "Print"

#: formhotkeyunit.rsprintscreen
msgctxt "formhotkeyunit.rsprintscreen"
msgid "Print Screen"
msgstr "Print Screen"

#: formhotkeyunit.rsrightarrow
msgctxt "formhotkeyunit.rsrightarrow"
msgid "Right Arrow"
msgstr "Right Arrow"

#: formhotkeyunit.rsrightwindowskey
msgctxt "formhotkeyunit.rsrightwindowskey"
msgid "Right Windows key"
msgstr "Right Windows key"

#: formhotkeyunit.rsscrolllock
msgctxt "formhotkeyunit.rsscrolllock"
msgid "Scroll Lock"
msgstr "Scroll Lock"

#: formhotkeyunit.rsselect
msgctxt "formhotkeyunit.rsselect"
msgid "Select"
msgstr "Select"

#: formhotkeyunit.rsseparator
msgctxt "formhotkeyunit.rsseparator"
msgid "Separator"
msgstr "Separator"

#: formhotkeyunit.rsshift
msgctxt "formhotkeyunit.rsshift"
msgid "Shift"
msgstr "Shift"

#: formhotkeyunit.rsspacebar
msgctxt "formhotkeyunit.rsspacebar"
msgid "Space bar"
msgstr "Space bar"

#: formhotkeyunit.rstab
msgctxt "formhotkeyunit.rstab"
msgid "Tab"
msgstr "Tab"

#: formhotkeyunit.rsuparrow
msgctxt "formhotkeyunit.rsuparrow"
msgid "Up Arrow"
msgstr "Up Arrow"

#: formmemoryregionsunit.rscommit
msgid "Commit"
msgstr "提交"

#: formmemoryregionsunit.rsdone
msgctxt "formmemoryregionsunit.rsdone"
msgid "done"
msgstr "完成"

#: formmemoryregionsunit.rsdoyouwanttousethecopyonwritebit
msgid "Do you want to use the COPY-ON-WRITE bit?"
msgstr "你想要使用写时拷贝(Copy-On-Write)技术吗?"

#: formmemoryregionsunit.rsexecute
msgctxt "formmemoryregionsunit.rsexecute"
msgid "Execute"
msgstr "可执行"

#: formmemoryregionsunit.rsexecuteread
msgid "Execute+Read"
msgstr "可执行+读"

#: formmemoryregionsunit.rsexecutereadwrite
msgid "Execute+Read+Write"
msgstr "可执行+读+写"

#: formmemoryregionsunit.rsexecutewritecopy
msgid "Execute+Write Copy"
msgstr "可执行+写时拷贝"

#: formmemoryregionsunit.rsfailedtodelete
msgid "Failed to delete %s"
msgstr "无法删除 %s"

#: formmemoryregionsunit.rsfree
msgid "Free"
msgstr "空闲"

#: formmemoryregionsunit.rsguard
msgid "Guard"
msgstr "保护"

#: formmemoryregionsunit.rsimage
msgctxt "formmemoryregionsunit.rsimage"
msgid "Image"
msgstr "映像"

#: formmemoryregionsunit.rsmapped
msgid "Mapped"
msgstr "映射"

#: formmemoryregionsunit.rsmemoryregions
msgctxt "formmemoryregionsunit.rsmemoryregions"
msgid "Memory regions"
msgstr "内存区域"

#: formmemoryregionsunit.rsmemoryregionssaving
msgid "Memory regions - Saving(%s/%s)"
msgstr "内存区域 - 保存中(%s/%s)"

#: formmemoryregionsunit.rsnoaccess
msgctxt "formmemoryregionsunit.rsnoaccess"
msgid "No Access"
msgstr "不可访问"

#: formmemoryregionsunit.rsnocache
msgid "No Cache"
msgstr "不缓存"

#: formmemoryregionsunit.rsprivate
msgid "Private"
msgstr "私有"

#: formmemoryregionsunit.rsread
msgid "Read"
msgstr "读"

#: formmemoryregionsunit.rsreadwrite
msgid "Read+Write"
msgstr "读+写"

#: formmemoryregionsunit.rsreserve
msgid "Reserve"
msgstr "保留"

#: formmemoryregionsunit.rstherearealreadymemoryfileswiththisnamedoyouwanttod
msgid "There are already memoryfiles with this name. Do you want to delete them? (choosing no will add the file(s) to empty slots)"
msgstr "同名内存文件已存在. 删除它们吗? (选择\"否\"将添加文件至新的位置)"

#: formmemoryregionsunit.rsthereisnofreeslot
msgid "There is no free slot"
msgstr "没有可用的空余位置"

#: formmemoryregionsunit.rswritecopy
msgctxt "formmemoryregionsunit.rswritecopy"
msgid "Write Copy"
msgstr "写时拷贝"

#: formsettingsunit.rs2bytes
msgctxt "formsettingsunit.rs2bytes"
msgid "2 字节"
msgstr "2 字节"

#: formsettingsunit.rs4bytes
msgctxt "formsettingsunit.rs4bytes"
msgid "4 字节"
msgstr "4 字节"

#: formsettingsunit.rs8bytes
msgctxt "formsettingsunit.rs8bytes"
msgid "8 字节"
msgstr "8 字节"

#: formsettingsunit.rsarrayofbyte
msgctxt "formsettingsunit.rsarrayofbyte"
msgid "Array of byte"
msgstr "字节数组"

#: formsettingsunit.rsbinary
msgctxt "formsettingsunit.rsbinary"
msgid "二进制"
msgstr "二进制"

#: formsettingsunit.rsbyte
msgctxt "formsettingsunit.rsbyte"
msgid "Byte"
msgstr "字节"

#: formsettingsunit.rscancelthecurrentscan
msgid "Cancel the current scan"
msgstr "取消当前的扫描"

#: formsettingsunit.rschangedvalue
msgid "Changed Value"
msgstr "变动的数值"

#: formsettingsunit.rschangetypeto
msgid "Change type to"
msgstr "更改类型为"

#: formsettingsunit.rsdebuggeroptions
msgctxt "formsettingsunit.rsdebuggeroptions"
msgid "Debugger Options"
msgstr "调试器选项"

#: formsettingsunit.rsdebugrun
msgid "Debug->Run"
msgstr "调试->运行"

#: formsettingsunit.rsdecreasedvalue
msgid "Decreased Value"
msgstr "减少的数值"

#: formsettingsunit.rsdouble
msgctxt "formsettingsunit.rsdouble"
msgid "Double"
msgstr "双浮点"

#: formsettingsunit.rsextra
msgctxt "formsettingsunit.rsextra"
msgid "Extra"
msgstr "其它"

#: formsettingsunit.rsfloat
msgctxt "formsettingsunit.rsfloat"
msgid "Float"
msgstr "单浮点"

#: formsettingsunit.rsgeneralsettings
msgctxt "formsettingsunit.rsgeneralsettings"
msgid "General Settings"
msgstr "常规设置"

#: formsettingsunit.rshigher
msgctxt "formsettingsunit.rshigher"
msgid "Higher"
msgstr "较高"

#: formsettingsunit.rshighest
msgctxt "formsettingsunit.rshighest"
msgid "Highest"
msgstr "最高"

#: formsettingsunit.rshotkeys
msgctxt "formsettingsunit.rshotkeys"
msgid "Hotkeys"
msgstr "热键"

#: formsettingsunit.rsidle
msgctxt "formsettingsunit.rsidle"
msgid "Idle"
msgstr "空闲"

#: formsettingsunit.rsincreasedvalue
msgid "Increased Value"
msgstr "增加的数值"

#: formsettingsunit.rsisnotavalidinterval
msgid "%s is not a valid interval"
msgstr "%s 时间间隔无效"

#: formsettingsunit.rslanguages
msgctxt "formsettingsunit.rslanguages"
msgid "Languages"
msgstr "语言"

#: formsettingsunit.rslower
msgctxt "formsettingsunit.rslower"
msgid "Lower"
msgstr "较低"

#: formsettingsunit.rslowest
msgctxt "formsettingsunit.rslowest"
msgid "Lowest"
msgstr "最低"

#: formsettingsunit.rsnewlanguageset
msgid "New language set"
msgstr "设置新的语言"

#: formsettingsunit.rsnextscan
msgctxt "formsettingsunit.rsnextscan"
msgid "Next Scan"
msgstr "再次扫描"

#: formsettingsunit.rsnoname
msgid "No Name"
msgstr "未命名"

#: formsettingsunit.rsnormal
msgctxt "formsettingsunit.rsnormal"
msgid "Normal"
msgstr "标准"

#: formsettingsunit.rspausetheselectedprocess
msgid "Pause the selected process"
msgstr "暂停所选择的进程"

#: formsettingsunit.rspleasebootwithunsigneddriversallowedf8duringbootor
msgid "Please boot with unsigned drivers allowed(F8 during boot), or sign the driver yourself"
msgstr "允许使用未签名驱动程序(启动过程中按 F8 键), 或自己为驱动程序签名"

#: formsettingsunit.rsplugins
msgctxt "formsettingsunit.rsplugins"
msgid "Plugins"
msgstr "插件"

#: formsettingsunit.rspopuphidecheatengine
msgid "Popup/Hide cheat engine"
msgstr "弹出/隐藏 Cheat Engine"

#: formsettingsunit.rsrequiresdbvm
msgid "(Requires DBVM)"
msgstr "(需要 DBVM)"

#: formsettingsunit.rsrestartce
msgid "It is recommended to restart Cheat Engine for this change to take effect"
msgstr "建议重新启动Cheat Engine以便使更改生效"

#: formsettingsunit.rsscansettings
msgctxt "formsettingsunit.rsscansettings"
msgid "Scan Settings"
msgstr "扫描设置"

#: formsettingsunit.rssigning
msgid "Signing"
msgstr "符号"

#: formsettingsunit.rsspeedhackspeed
msgid "Speedhack speed"
msgstr "速度调节"

#: formsettingsunit.rstext
msgctxt "formsettingsunit.rstext"
msgid "Text"
msgstr "文本"

#: formsettingsunit.rsthescanbuffersizehastobegreaterthan0
msgid "The scanbuffer size has to be greater than 0"
msgstr "扫描缓存大小必须大于 0"

#: formsettingsunit.rsthevalueforthekeypollintervalisinvalid
msgid "the value for the keypoll interval (%s is invalid"
msgstr "热键轮询间隔 %s 无法设置"

#: formsettingsunit.rsthevalueforthewaitbetweenhotkeypressesisinvalid
msgid "the value for the wait between hotkey presses (%s is invalid"
msgstr "等候按下热键之间的间隔值 (%s 无效"

#: formsettingsunit.rsthispluginisalreadyloaded
msgid "This plugin is already loaded"
msgstr "此插件已载入"

#: formsettingsunit.rstimecritical
msgctxt "formsettingsunit.rstimecritical"
msgid "TimeCritical"
msgstr "临界"

#: formsettingsunit.rstogglebetweenfirstlastscancompare
msgid "Toggle between first/last scan compare"
msgstr "首次/最后 扫描之间切换"

#: formsettingsunit.rstogglethespeedhack
msgid "Toggle the speedhack"
msgstr "切换速度"

#: formsettingsunit.rstools
msgctxt "formsettingsunit.rstools"
msgid "Tools"
msgstr "工具"

#: formsettingsunit.rsunchangedvalue
msgid "Unchanged Value"
msgstr "未变动的数值"

#: formsettingsunit.rsundolastscan
msgid "Undo last scan"
msgstr "撤销最近一次的扫描"

#: formsettingsunit.rsunknowninitialvalue
msgid "Unknown Initial Value"
msgstr "未知的初始值"

#: formsettingsunit.rsunrandomizer
msgctxt "formsettingsunit.rsunrandomizer"
msgid "Unrandomizer"
msgstr "禁止随机"

#: formsettingsunit.rsyouhaventselectedanymemorytypethiswillresultinchea
msgid "You haven't selected any memory type. This will result in Cheat Engine finding NO memory! Are you stupid?"
msgstr "你还没有选择任何内存类型. 这将导致 Cheat Engine 无法找到内存! 你忘记了吗?"

#: formsettingsunit.strprocesswatcherwillpreventunloader
msgid "Enabling the process watcher will prevent the unloader from working"
msgstr "启用进程监视器时防止中途退出"

#: foundcodeunit.rsfctheswillsetasiftwarebreakpointint3oneverysingleopcodeetc
msgid "This will set a Software Breakpoint (int3) on every single opcode that will be reported here. Are you sure ?"
msgstr "将在每一个操作码上设置一个软件断点 (int3) 并在这里列出记录. 你确定吗 ?"

#: foundcodeunit.strclose
msgctxt "foundcodeunit.strclose"
msgid "Close"
msgstr "关闭"

#: foundlisthelper.rserror
msgctxt "foundlisthelper.rserror"
msgid "Error"
msgstr "错误"

#: foundlisthelper.rsundefinederror
msgctxt "foundlisthelper.rsundefinederror"
msgid "Undefined error"
msgstr "未定义的错误"

#: frmadconfigunit.rspercentageshown
msgid "Percentage shown"
msgstr "百分比显示"

#: frmautoinjectunit.rsareyousureyouwanttoclose
msgid "Are you sure you want to close %s ?"
msgstr "你确定要关闭 %s?"

#: frmautoinjectunit.rsautoassembler
msgid "Auto assembler"
msgstr "自动汇编脚本"

#: frmautoinjectunit.rsceafilter
msgid "Cheat Engine Assembly (*.CEA)|*.CEA|All Files ( *.* )|*.*"
msgstr "Cheat Engine 汇编 (*.CEA)|*.CEA|所有文件 (*.*)|*.*"

#: frmautoinjectunit.rscegafilter
msgid "Cheat Engine GNU Assembly (*.CEGA)|*.CEGA|All Files ( *.* )|*.*"
msgstr "Cheat Engine GNU汇编 (*.CEGA)|*.CEGA|所有文件 ( *.* )|*.*"

#: frmautoinjectunit.rscodeinjecttemplate
msgid "Code inject template"
msgstr "代码注入模板"

#: frmautoinjectunit.rscodeneedsenableanddisable
msgid "The code needs an [ENABLE] and a [DISABLE] section if you want to use this script as a table entry"
msgstr "如果你想将脚本加入到列表中则代码需要有 [ENABLE] 和 [DISABLE] 标签段"

#: frmautoinjectunit.rscoderelocationtemplate
msgid "Code relocation template"
msgstr "代码迁移模板"

#: frmautoinjectunit.rsendaddresslastbytesareincludedifnecesary
msgid "End address (last bytes are included if necessary)"
msgstr "结束地址(如果有必要最后一个字节也包括在内)"

#: frmautoinjectunit.rserrorcouldnotfinduniqueaobtriedcode
msgid "ERROR: Could not find unique AOB, tried code \""
msgstr "ERROR: 无法找到独特的 AOB, 代码可靠吗 \""

#: frmautoinjectunit.rsexecutescript
msgid "Execute script"
msgstr "运行脚本"

#: frmautoinjectunit.rsfailedallocatingmemoryforthescript
msgid "Failed allocating memory for the script"
msgstr "脚本分配内存失败"

#: frmautoinjectunit.rsfailedcreatingcallingstubforscriptlocatedataddress
msgid "Failed creating calling stub for script located at address "
msgstr "Failed creating calling stub for script located at address "

#: frmautoinjectunit.rsfailedtoaddtotablenotallcodeisinjectable
msgid "Failed to add to table. Not all code is injectable"
msgstr "无法添加到列表中. 不是所有的代码都能被注入"

#: frmautoinjectunit.rsfailedwritingthescripttotheprocess
msgid "failed writing the script to the process"
msgstr "脚本写入到进程时失败"

#: frmautoinjectunit.rsfailureloadingundercdll
msgid "Failure loading undercdll"
msgstr "加载 undercdll 失败"

#: frmautoinjectunit.rsgnuascript
msgid "GNU Assembler Script"
msgstr "GNU 汇编脚本"

#: frmautoinjectunit.rsluafilter
msgid "LUA Script (*.LUA)|*.LUA|All Files ( *.* )|*.*"
msgstr "LUA 脚本 (*.LUA)|*.LUA|所有文件 ( *.* )|*.*"

#: frmautoinjectunit.rsluascript
msgid "LUA Script"
msgstr "LUA 脚本"

#: frmautoinjectunit.rsnotallcodeisinjectable
msgid ""
"Not all code is injectable.\n"
"%s\n"
"Are you sure you wan't to edit it to this?\n"
msgstr "不是所有的代码都能被注入.\n%s\n你确定不用再修改了吗?\n"

#: frmautoinjectunit.rsonwhataddressdoyouwantthejump
msgid "On what address do you want the jump?"
msgstr "你要跳转到什么地址吗?"

#: frmautoinjectunit.rsscript
msgid "Script "
msgstr "脚本 "

#: frmautoinjectunit.rsscript1
msgid "Script 1"
msgstr "脚本 1"

#: frmautoinjectunit.rsstartaddress
msgid "Start address"
msgstr "起始地址"

#: frmautoinjectunit.rsthumbinstructionsarenotyetimplemented
msgid "Thumb instructions are not yet implemented"
msgstr "Thumb 指令尚未实现"

#: frmautoinjectunit.rswhatidentifierdoyouwanttouse
msgid "What do you want to name the symbol for the injection point?"
msgstr "你想给注入点取个什么符号名呢?"

#: frmautoinjectunit.rswritecode
msgid "Write code"
msgstr "写入代码"

#: frmbreakpointlistunit.rsbpareyousureyouwishtochangethistoapegewidebp
msgid "Are you sure you wish to change this to a pagewide breakpoint?"
msgstr "你确实想要更改这个pagewide断点吗?"

#: frmcodecavescannerunit.rsclosingthiswindowwillalsostopthescannerareyousure
msgid "Closing this window will also stop the scanner. Are you sure?"
msgstr "关闭此窗口将会停止扫描. 你确定吗?"

#: frmcodecavescannerunit.rspleaseprovideavalidsizeforthewantedcodecave
msgid "Please provide a valid size for the wanted code cave"
msgstr "请提供有效的Code Cave大小"

#: frmcodecavescannerunit.rspleaseprovideavalidstartaddress
msgid "Please provide a valid start address"
msgstr "请提供一个有效的起始地址"

#: frmcodecavescannerunit.rspleaseprovideavalidstopaddress
msgid "Please provide a valid stop address"
msgstr "请提供一个有效的结束地址"

#: frmcodecavescannerunit.rspleasetellmeyoudontneedacodecavethissmall
msgid "Please tell me you don't need a code cave this small!!!"
msgstr "请告诉我你不再需要这个区域的Code Cave!!!"

#: frmdisassemblyscanunit.rsdsclose
msgctxt "frmdisassemblyscanunit.rsdsclose"
msgid "Close"
msgstr "关闭"

#: frmdisassemblyscanunit.rsdsscanerror
msgid "scan error"
msgstr "扫描出错"

#: frmdissectwindowunit.rsdissectwindows
msgctxt "frmdissectwindowunit.rsdissectwindows"
msgid "Dissect Windows"
msgstr "分析窗口"

#: frmdissectwindowunit.rsgivethenewtextforthiswindow
msgid "Give the new text for this window"
msgstr "赋予此窗口一个新的文本"

#: frmdissectwindowunit.rsinvis
msgid "Invis"
msgstr "不可见"

#: frmdissectwindowunit.rsremoved
msgid "Removed"
msgstr "移除"

#: frmenumeratedllsunit.rsnothingfound
msgctxt "frmenumeratedllsunit.rsnothingfound"
msgid "nothing found"
msgstr "什么都没找到"

#: frmexetrainergeneratorunit.rsarchive
msgid " ARCHIVE:"
msgstr " ARCHIVE:"

#: frmexetrainergeneratorunit.rscetrainermaker
msgid "CE trainer maker"
msgstr "CE 修改器制作器"

#: frmexetrainergeneratorunit.rsdecompressor
msgid " DECOMPRESSOR:"
msgstr " DECOMPRESSOR:"

#: frmexetrainergeneratorunit.rsdefault
msgctxt "frmexetrainergeneratorunit.rsdefault"
msgid "Default"
msgstr "默认"

#: frmexetrainergeneratorunit.rsfailureonwriting
msgid "failure on writing"
msgstr "写入失败"

#: frmexetrainergeneratorunit.rsfailureopeningthetrainerforresourceupdates
msgid "Failure opening the trainer for resource updates. Make sure you do not watch the creation"
msgstr "修改器资源更新失败. 你确认不再创建吗?"

#: frmexetrainergeneratorunit.rsfastest
msgid "Fastest"
msgstr "最快"

#: frmexetrainergeneratorunit.rsgenerate
msgctxt "frmexetrainergeneratorunit.rsgenerate"
msgid "Generate"
msgstr "生成"

#: frmexetrainergeneratorunit.rsiconupdateerror
msgid "icon update error"
msgstr "图标更新错误"

#: frmexetrainergeneratorunit.rsinvalidicon
msgid "(Invalid icon)"
msgstr "(无效图标)"

#: frmexetrainergeneratorunit.rsinvalidicontype
msgid "(Invalid icon type)"
msgstr "(无效图标类型)"

#: frmexetrainergeneratorunit.rsmax
msgctxt "frmexetrainergeneratorunit.rsmax"
msgid "Max"
msgstr "最大"

#: frmexetrainergeneratorunit.rsnewfoldername
msgid "New foldername"
msgstr "新的文件夹名"

#: frmexetrainergeneratorunit.rsnone
msgctxt "frmexetrainergeneratorunit.rsnone"
msgid "None"
msgstr "无"

#: frmexetrainergeneratorunit.rssaving
msgctxt "frmexetrainergeneratorunit.rssaving"
msgid "Saving..."
msgstr "保存中..."

#: frmexetrainergeneratorunit.rsthetrainerhasbeensuccessfullygenerated
msgid "The trainer has been successfully generated"
msgstr "修改器已成功制作"

#: frmexetrainergeneratorunit.rsupdatefailed
msgid "Failure updating the trainer resource information: %d"
msgstr "更新修改器资源失败: %d"

#: frmfilepatcherunit.rsfilepatcher
msgid "File Patcher: "
msgstr "文件补丁: "

#: frmfillmemoryunit.rsfmregiontoolarge
msgid "Region too large"
msgstr "区域过大"

#: tformsettings.gbunexpectedexceptionhandling.caption
msgid "When CE starts default behaviour for unexpected breakpoints"
msgstr "当CE启动意外断点的默认行为时"

#: frmfillmemoryunit.rspleasefillinavalidfillvalue
msgid "Please fill in a valid 'Fill' value"
msgstr "请输入一个有效的\"填写\"值"

#: tmemorybrowser.miexceptionregionautoaddallocs.caption
msgctxt "TMEMORYBROWSER.MIEXCEPTIONREGIONAUTOADDALLOCS.CAPTION"
msgid "Automatically add allocated memory by CE as watched regions"
msgstr "由CE自动添加分配的内存作为监视区域"

#: frmfillmemoryunit.rspleasefillinavalidfromaddress
msgctxt "frmfillmemoryunit.rspleasefillinavalidfromaddress"
msgid "Please fill in a valid 'From' address"
msgstr "请填写有效的\"从\"地址"

#: frmfillmemoryunit.rspleasefillinavalidtoaddress
msgctxt "frmfillmemoryunit.rspleasefillinavalidtoaddress"
msgid "Please fill in a valid 'To' address"
msgstr "请填写有效的\"至\"地址"

#: frmfindstaticsunit.rsfsstructorarray
msgid "struct or array"
msgstr "结构或数组"

#: frmfindstaticsunit.rsfsunreadablepointer
msgid "Unreadable pointer"
msgstr "不可读取的指针"

#: frmfindstaticsunit.strscan
msgctxt "frmfindstaticsunit.strscan"
msgid "Scan"
msgstr "扫描"

#: frmfindstaticsunit.strstop
msgctxt "frmfindstaticsunit.strstop"
msgid "Stop"
msgstr "停止"

#: frmfindstaticsunit.strstopping
msgctxt "frmfindstaticsunit.strstopping"
msgid "Stopping..."
msgstr "正在停止..."

#: frmfloatingpointpanelunit.rsfppextended
msgctxt "frmfloatingpointpanelunit.rsfppextended"
msgid "Extended"
msgstr "扩展"

#: frmgdtunit.rsgdtaccessed
msgid "Accessed="
msgstr "Accessed="

#: frmgdtunit.rsgdtavl
msgid "AVL="
msgstr "AVL="

#: frmgdtunit.rsgdtcodesegment
msgid "Code Segment"
msgstr "代码段"

#: frmgdtunit.rsgdtconforming
msgid "Conforming="
msgstr "Conforming="

#: frmgdtunit.rsgdtdatasegment
msgid "Data Segment"
msgstr "数据段"

#: frmgdtunit.rsgdtdpl
msgid "DPL="
msgstr "DPL="

#: frmgdtunit.rsgdtexpansiondirection
msgid "Expansion direction="
msgstr "Expansion direction="

#: frmgdtunit.rsgdtnotpresent
msgid "Not present"
msgstr "不存在"

#: frmgdtunit.rsgdtreadable
msgid "Readable="
msgstr "Readable="

#: frmgdtunit.rsgdtreaderror
msgid "Read error"
msgstr "读取错误"

#: frmgdtunit.rsgdtsystemsegment
msgid "System Segment"
msgstr "系统段"

#: frmgdtunit.rsgdtwritable
msgid "Writable="
msgstr "Writable="

#: frmgroupscanalgoritmgeneratorunit.rsadd
msgctxt "frmgroupscanalgoritmgeneratorunit.rsadd"
msgid "Add"
msgstr "添加"

#: frmgroupscanalgoritmgeneratorunit.rsgsgblocksizemustbeprovided
msgid "blocksize must be provided"
msgstr "必须提供区块大小"

#: frmgroupscanalgoritmgeneratorunit.rsgsgshouldbeatleast
msgid "Should be at least %d"
msgstr "应至少 %d"

#: frmgroupscanalgoritmgeneratorunit.rspickedhint
msgid "When checked this element will get added to the addresslist. Note: If all checkboxes are disabled, ALL elements will be added"
msgstr "勾选后将会把它加进地址列表中. 注意: 如果禁用所有复选框，将会添加所有元素"

#: frmgroupscanalgoritmgeneratorunit.rswildcard
msgid "Skip nr of bytes:"
msgstr "跳过多少字节:"

#: frmidtunit.rsidt16bitinterruptgate
msgid "16-bit interrupt gate"
msgstr "16位中断门"

#: frmidtunit.rsidt16biytrapgate
msgid "16-bit trap gate"
msgstr "16位陷阱门"

#: frmidtunit.rsidt32bitinterruptgate
msgid "32-bit interrupt gate"
msgstr "32位中断门"

#: frmidtunit.rsidt32biytrapgate
msgid "32-bit trap gate"
msgstr "32位陷阱门"

#: frmidtunit.rsidtdpl
msgid " DPL="
msgstr " DPL="

#: frmidtunit.rsidtnotpresent
msgid "not present"
msgstr "不存在"

#: frmidtunit.rsidttaskgate
msgid "task gate"
msgstr "任务门"

#: frmidtunit.rsidtunkownidt
msgid "unknown IDT"
msgstr "未知 IDT"

#: frmloadmemoryunit.rsloadintomemory
msgid "Load %s into memory"
msgstr "将 %s 加载进内存"

#: frmloadmemoryunit.rsnotallthememorycanbewritteninthecurrentmemoryregion
msgid "Not all the memory can be written in the current memory region. Do you want to force as much as possible into the given region and just discard what can not be written? (Really bad idea)"
msgstr "并不是所有的内存都可以写入当前的内存区域。你想强迫尽可能多的进入给定的区域，并放弃什么不能写的吗？(真是坏主意)"

#: frmloadmemoryunit.rswrotebytesoutof
msgid "Wrote %d bytes out of %d"
msgstr "写入 %d 个字节的 %d"

#: frmloadmemoryunit.strinvalidfile
msgid "This is a invalid memory region file. I'll assume this file has no header data"
msgstr "该内存区域文件无效. 文件可能缺少头部数据(header data)"

#: frmluaengineunit.rserror
msgid "Script Error"
msgstr "脚本错误"

#: frmluaengineunit.rsleerrorinline
msgid "Error in line "
msgstr "错误行 "

#: frmluaengineunit.rsleonlyonescriptcanbedebuggedatatimeetc
msgid "Only one script can be debugged at a time. Continue executing this script without the debugger?"
msgstr "每次仅能调试一个脚本. 要继续执行这个脚本调试器吗?"

#: frmluaengineunit.rsleundefinederror
msgctxt "frmluaengineunit.rsleundefinederror"
msgid "Undefined error"
msgstr "未定义的错误"

#: frmluaengineunit.rsleuserclickedstop
msgid "User clicked stop"
msgstr "用户点击停止"

#: frmluaengineunit.rsluaengine
msgctxt "frmluaengineunit.rsluaengine"
msgid "Lua Engine"
msgstr "Lua 引擎"

#: frmmemoryallochandlerunit.rsevent1failure
msgid "Event1 failure:"
msgstr "事件1 failure:"

#: frmmemoryallochandlerunit.rsevent2failure
msgid "Event2 failure:"
msgstr "事件2 failure:"

#: frmmemoryallochandlerunit.rsevent3failure
msgid "Event3 failure:"
msgstr "事件3 failure:"

#: frmmemoryallochandlerunit.rsfailuretohook
msgid "Failure to hook"
msgstr "安装钩子失败"

#: frmmemoryallochandlerunit.rsfailuretoinitialize
msgid "Failure to initialize"
msgstr "初始化失败"

#: frmmemoryallochandlerunit.rsheapcount
msgid "Heapcount=%s"
msgstr "堆栈计数=%s"

#: frmmemoryallochandlerunit.rsqueuedmemoryeventswaiting
msgid "Queued memory events waiting: %s"
msgstr "内存事件等待队列: %s"

#: frmmemoryrecorddropdownsettingsunit.rsdddropdownotionsfor
msgid "Dropdown options for "
msgstr "下拉列表项 "

#: frmmemoryviewexunit.rsaddresszoom
msgid "Address : %s zoom : %s"
msgstr "地址 : %s 缩放 : %s"

#: frmmemoryviewexunit.rsfillintheaddressyouwanttogoto
msgctxt "frmmemoryviewexunit.rsfillintheaddressyouwanttogoto"
msgid "Fill in the address you want to go to"
msgstr "输入你想跳转的地址"

#: frmmemoryviewexunit.rsgotoaddress
msgctxt "frmmemoryviewexunit.rsgotoaddress"
msgid "Goto Address"
msgstr "转到地址"

#: frmmemviewpreferencesunit.rsbackgroundcolor
msgid "Background color"
msgstr "背景色"

#: frmmemviewpreferencesunit.rscallcolor
msgctxt "frmmemviewpreferencesunit.rscallcolor"
msgid "Call color"
msgstr "Call 颜色"

#: frmmemviewpreferencesunit.rsconditionaljumpcolor
msgctxt "frmmemviewpreferencesunit.rsconditionaljumpcolor"
msgid "Conditional jump color"
msgstr "条件跳转 颜色"

#: frmmemviewpreferencesunit.rsdcbreakpoint
msgid "Breakpoint"
msgstr "断点"

#: frmmemviewpreferencesunit.rsdchighlighted
msgctxt "frmmemviewpreferencesunit.rsdchighlighted"
msgid "Highlighted"
msgstr "高亮"

#: frmmemviewpreferencesunit.rsdchighlightedbreakpoint
msgid "Highlighted breakpoint"
msgstr "高亮背景色"

#: frmmemviewpreferencesunit.rsdchighlightedbreakpointsecondary
msgid "Highlighted breakpoint secondary"
msgstr "高亮二级断点"

#: frmmemviewpreferencesunit.rsdchighlightedsecondary
msgid "Highlighted secondary"
msgstr "高亮次级断点"

#: frmmemviewpreferencesunit.rsdchighlightedultimap2
msgid "Highlighted Ultimap2"
msgstr "高亮 Ultimap2"

#: frmmemviewpreferencesunit.rsdchighlightedultimap2secondary
msgid "Highlighted Ultimap2 secondary"
msgstr "高亮次级 Ultimap2"

#: frmmemviewpreferencesunit.rsdcnormal
msgctxt "frmmemviewpreferencesunit.rsdcnormal"
msgid "Normal"
msgstr "标准"

#: frmmemviewpreferencesunit.rsdcultimap2
msgid "Ultimap2"
msgstr "Ultimap2"

#: frmmemviewpreferencesunit.rshexadecimalcolor
msgctxt "frmmemviewpreferencesunit.rshexadecimalcolor"
msgid "Hexadecimal color"
msgstr "十六进制颜色"

#: frmmemviewpreferencesunit.rsnormalcolor
msgid "Normal color"
msgstr "标准色"

#: frmmemviewpreferencesunit.rsregistercolor
msgctxt "frmmemviewpreferencesunit.rsregistercolor"
msgid "Register color"
msgstr "寄存器颜色"

#: frmmemviewpreferencesunit.rssymbolcolor
msgctxt "frmmemviewpreferencesunit.rssymbolcolor"
msgid "Symbol color"
msgstr "符号颜色"

#: frmmemviewpreferencesunit.rsunconditionaljumpcolor
msgctxt "frmmemviewpreferencesunit.rsunconditionaljumpcolor"
msgid "Unconditional jump color"
msgstr "无条件跳转 颜色"

#: frmmergepointerscanresultsettingsunit.rscopiesthesourceptrfilestotheworkingfolder
msgid "Copies the source .ptr.# files to the working folder of the main PTR"
msgstr "复制原始 .ptr.# 文件复制到PTR的主目录里"

#: frmmergepointerscanresultsettingsunit.rslinksdirectlytothepathsofthesourceptrfiles
msgid ""
"Links directly to the paths of the source .ptr.# files.\n"
"Note: The result will be unusable by other systems that do not have the same path layout, until you generate new results\n"
msgstr ""
"直接链接到原始 .ptr # 文件的路径.\n"
"注意: 其他系统不具备相同的路径布局的话 .ptr文件将无法使用, 除非重新生成新的 .ptr\n"

#: frmmergepointerscanresultsettingsunit.rsmovesthesourceptrfilestotheworkingfolder
msgid ""
"Moves the source .ptr.# files to the working folder of the main PTR\n"
"Note: This destroys the source\n"
msgstr "移动源 .ptr.#文件 到主PTR目录\n备注: 可能会损坏源文件\n"

#: frmmodifyregistersunit.rsmodifyregisterssat
msgid "Modify registers(s) at %s"
msgstr "修改寄存器 %s"

#: frmmodifyregistersunit.rspleasefillinavalidvaluefor
msgid "Please fill in a valid value for"
msgstr "请填写一个有效的值"

#: frmpagingunit.rsfailurereadingphysicalmemory
msgid "failure reading physical memory"
msgstr "读取物理内存失败"

#: frmpagingunit.rsnotfound
msgid "Not found"
msgstr "未找到"

#: frmpointerrescanconnectdialogunit.rscouldnotberesolved
msgctxt "frmpointerrescanconnectdialogunit.rscouldnotberesolved"
msgid " could not be resolved"
msgstr " 无法解析"

#: frmpointerrescanconnectdialogunit.rshost
msgctxt "frmpointerrescanconnectdialogunit.rshost"
msgid "host:"
msgstr "主机:"

#: frmpointerscanconnectdialogunit.rshostcouldnotberesolved
msgid "host: %s could not be resolved"
msgstr "主机: %s 无法解析"

#: frmpointerscanconnectdialogunit.rsuseloadedpointermap
msgid "Use loaded pointermap:"
msgstr "使用载入的\"指针映射集\":"

#: frmprocesswatcherunit.rsconventionalids
msgid "----Conventional ID's----"
msgstr "----常规的 ID----"

#: frmprocesswatcherunit.rsfailedstartingtheprocesswatcher
msgid "Failed starting the process watcher"
msgstr "启动监视进程失败"

#: frmprocesswatcherunit.rsfirstselectaprocess
msgctxt "frmprocesswatcherunit.rsfirstselectaprocess"
msgid "First select a process!"
msgstr "请先选择一个进程!"

#: frmprocesswatcherunit.rsisntavalidprocessid
msgctxt "frmprocesswatcherunit.rsisntavalidprocessid"
msgid "%s isn't a valid processID"
msgstr "%s 进程的ID无效"

#: frmprocesswatcherunit.rspeprocess
msgid "PEPROCESS="
msgstr "PEPROCESS="

#: frmprocesswatcherunit.rsprocessid
msgid "ProcessID="
msgstr "ProcessID="

#: frmprocesswatcherunit.rsprocesswatcherthreaderror
msgid "processwatcher: thread error:%s"
msgstr "监视进程: 线程错误:%s"

#: frmprocesswatcherunit.rsthreadid
msgid "ThreadID:"
msgstr "ThreadID:"

#: frmrescanpointerunit.rsadd
msgctxt "frmrescanpointerunit.rsadd"
msgid "Add"
msgstr "添加"

#: frmrescanpointerunit.rsnotalltheendoffsetshavebeenfilledin
msgid "Not all the end offsets have been filled in"
msgstr "不是所有的结束偏移都已填写"

#: frmrescanpointerunit.rsnotallthestartoffsetshavebeenfilledin
msgid "Not all the start offsets have been filled in"
msgstr "不是所有的起始偏移都已填写"

#: frmrescanpointerunit.rsremove
msgctxt "frmrescanpointerunit.rsremove"
msgid "Remove"
msgstr "移除"

#: frmrescanpointerunit.rsrpentertheipaddressestonotifyexplicitly
msgctxt "frmrescanpointerunit.rsrpentertheipaddressestonotifyexplicitly"
msgid "Enter the IP addresses to notify explicitly"
msgstr "输入通知的IP地址"

#: frmrescanpointerunit.rsrpfirstoffset
msgid "First offset"
msgstr "第一个偏移"

#: frmrescanpointerunit.rsrpiplist
msgctxt "frmrescanpointerunit.rsrpiplist"
msgid "IP List"
msgstr "IP 列表"

#: frmrescanpointerunit.rsrplastoffset
msgctxt "frmrescanpointerunit.rsrplastoffset"
msgid "Last offset"
msgstr "最后一个偏移"

#: frmrescanpointerunit.rsrpthefunction
msgid "The function %s(base, offsets, target) has not yet been defined. Please define it first"
msgstr "函数 %s(基址, 偏移, 目标) 尚未定义. 请先定义它"

#: frmrescanpointerunit.rsrpusesavedpointermap
msgid "Use saved pointermap: "
msgstr "使用保存的\"指针映射集\": "

#: frmrescanpointerunit.rsrpusesavedpointermap2
msgctxt "frmrescanpointerunit.rsrpusesavedpointermap2"
msgid "Use saved pointermap"
msgstr "使用保存的\"指针映射集\""

#: frmresumepointerscanunit.rsentertheaddressthisscanshouldlookfor
msgid "Enter the address this scan should look for"
msgstr "输入要扫描的地址"

#: frmresumepointerscanunit.rsnewpointermap
msgid "New pointermap"
msgstr "新的指针映射集"

#: frmresumepointerscanunit.rsrpsentertheipaddressestonotifyexplicitly
msgctxt "frmresumepointerscanunit.rsrpsentertheipaddressestonotifyexplicitly"
msgid "Enter the IP addresses to notify explicitly"
msgstr "输入通知的IP地址"

#: frmresumepointerscanunit.rsrpsiplist
msgctxt "frmresumepointerscanunit.rsrpsiplist"
msgid "IP List"
msgstr "IP 列表"

#: frmresumepointerscanunit.rsrpswasnotfound
msgid " was not found"
msgstr " 未找到"

#: frmsavememoryregionunit.rsisnotavalidaddress
msgctxt "frmsavememoryregionunit.rsisnotavalidaddress"
msgid "%s is not a valid address"
msgstr "%s 不是一个有效的地址"

#: frmsavememoryregionunit.rsnostartaddress
msgid "If you don't include the header data you'll have to specify the startaddress yourself when loading the file(That means Cheat Engine wont fill in the startaddress text field when loaded for you)"
msgstr "如果没有包含头部数据(header data)你就要在载入文件时自己指定起始地址 (这意味着 Cheat Engine 载入时不再自动填入起始地址)"

#: frmsavememoryregionunit.rsnotallthememorywasreadablein
msgid "Not all the memory was readable in"
msgstr "不是所有内存都是可读的"

#: frmsavememoryregionunit.rspleaseaddatleastoneaddressregiontothelist
msgid "Please add at least one address region to the list"
msgstr "请在列表中添加至少一个地址"

#: frmsavesnapshotsunit.rsssareyousureyouwishtothrowawaythesesnapshots
msgid "Are you sure you wish to throw away these snapshots?"
msgstr "你确定想要扔掉这些快照吗?"

#: frmsnapshothandlerunit.rsshcompare
msgctxt "frmsnapshothandlerunit.rsshcompare"
msgid "Compare"
msgstr "比较"

#: frmsnapshothandlerunit.rsshdissectandcomparememoryofselectedsnapshots
msgid "Dissect and compare memory of selected snapshots"
msgstr "分析并比较选中的内存快照"

#: frmsnapshothandlerunit.rsshdissectmemoryofselectedsnapshot
msgid "Dissect memory of selected snapshot"
msgstr "分析选中的内存快照"

#: frmsnapshothandlerunit.rsshfunctions
msgid " Function(s): "
msgstr " 功能: "

#: frmsnapshothandlerunit.rsshlockandaddtostructuredissect
msgctxt "frmsnapshothandlerunit.rsshlockandaddtostructuredissect"
msgid "Lock and add to structure dissect"
msgstr "锁定并添加结构分析"

#: frmsnapshothandlerunit.rsshnewwindow
msgctxt "frmsnapshothandlerunit.rsshnewwindow"
msgid "<New window>"
msgstr "<新窗口>"

#: frmsnapshothandlerunit.rsshselectthestructuredissectwindowyouwishtoaddthisregionto
msgctxt "frmsnapshothandlerunit.rsshselectthestructuredissectwindowyouwishtoaddthisregionto"
msgid "Select the structure dissect window you wish to add this region to"
msgstr "请选择您想添加的此区域结构分析窗口"

#: frmsnapshothandlerunit.rsshthestructureslistisbroken
msgctxt "frmsnapshothandlerunit.rsshthestructureslistisbroken"
msgid "The structures list is broken"
msgstr "结构列表是 broken"

#: frmsnapshothandlerunit.rsshview
msgctxt "frmsnapshothandlerunit.rsshview"
msgid "View"
msgstr "视图"

#: frmsortpointerlistunit.rstimeleft
msgid "Estimated time left"
msgstr "预计剩余时间"

#: frmstackviewunit.rslockandaddtostructuredissect
msgctxt "frmstackviewunit.rslockandaddtostructuredissect"
msgid "Lock and add to structure dissect"
msgstr "锁定并添加到结构分析"

#: frmstackviewunit.rsnewwindow
msgctxt "frmstackviewunit.rsnewwindow"
msgid "<New window>"
msgstr "<新建窗口>"

#: frmstackviewunit.rsselectthestructuredissectwindowyouwishtoaddthisreg
msgctxt "frmstackviewunit.rsselectthestructuredissectwindowyouwishtoaddthisreg"
msgid "Select the structure dissect window you wish to add this region to"
msgstr "请选择你想添加的此区域结构分析窗口"

#: frmstackviewunit.rssvthisstackviewwindowhasallocatedstacksnapshotsetc
msgid "This stackview window has allocated stack snapshots in the target process. Do you wish to free them?"
msgstr "This stackview window has allocated stack snapshots in the target process. Do you wish to free them?"

#: frmstackviewunit.rsthestructureslistisbroken
msgctxt "frmstackviewunit.rsthestructureslistisbroken"
msgid "The structures list is broken"
msgstr "结构列表已损坏"

#: frmstringmapunit.rsbtnshowlist
msgctxt "frmstringmapunit.rsbtnshowlist"
msgid "<<Show list"
msgstr "<<显示列表"

#: frmstringmapunit.rserror
msgid "Error="
msgstr "Error="

#: frmstringmapunit.rsgenerateregexprenginefailed
msgid "GenerateRegExprEngine failed"
msgstr "GenerateRegExprEngine 失败"

#: frmstringmapunit.rsgeneratestringmap
msgctxt "frmstringmapunit.rsgeneratestringmap"
msgid "Generate string map"
msgstr "生成字符串映射"

#: frmstringmapunit.rsnoreadablememoryfound
msgctxt "frmstringmapunit.rsnoreadablememoryfound"
msgid "No readable memory found"
msgstr "未找到可读内存"

#: frmstringmapunit.rsstop
msgctxt "frmstringmapunit.rsstop"
msgid "Stop"
msgstr "停止"

#: frmstringmapunit.rsstringcount
msgid "Stringcount: %s"
msgstr "字符串总数: %s"

#: frmstringmapunit.rsunhandledtstringscancrash
msgid "Unhandled TStringScan crash"
msgstr "未知的 TStringScan 崩溃"

#: frmstringpointerscanunit.rsareyousureyo
msgid "Are you sure you wish to start a new scan?"
msgstr "你确实想要开始新的扫描吗?"

#: frmstringpointerscanunit.rsgeneratedscanning
msgid "Generated. Scanning..."
msgstr "生成扫描中..."

#: frmstringpointerscanunit.rsgeneratingstringmap
msgid "Generating stringmap"
msgstr "生成字符串映射"

#: frmstringpointerscanunit.rsscan
msgctxt "frmstringpointerscanunit.rsscan"
msgid "Scan"
msgstr "扫描"

#: frmstringpointerscanunit.rsscanningfoun
msgid "Scanning... Found %s"
msgstr "搜索中... 找到 %s"

#: frmstringpointerscanunit.rsspsuaddress
msgctxt "frmstringpointerscanunit.rsspsuaddress"
msgid "Address"
msgstr "地址"

#: frmstringpointerscanunit.rsspsuaddress2
msgid "Address 2"
msgstr "地址 2"

#: frmstringpointerscanunit.rsspsuerrorduringscannoscanresults
msgid "Error during scan. No scanresults available"
msgstr "扫描过程中出错. 无可用的扫描结果"

#: frmstringpointerscanunit.rsspsuexception
msgid "Exception:"
msgstr "异常:"

#: frmstringpointerscanunit.rsspsufound
msgctxt "frmstringpointerscanunit.rsspsufound"
msgid "Found:"
msgstr "找到:"

#: frmstringpointerscanunit.rsspsufuuuu
msgid "FUUUU"
msgstr "FUUUU"

#: frmstringpointerscanunit.rsspsufuuuuu
msgid "FUUUUU"
msgstr "FUUUUU"

#: frmstringpointerscanunit.rsspsunotyetimplemented
msgid "Not yet implemented"
msgstr "(功能)未实现"

#: frmstringpointerscanunit.rsspsuoffset
msgid "Offset "
msgstr "偏移 "

#: frmstringpointerscanunit.rsspsurescan
msgctxt "frmstringpointerscanunit.rsspsurescan"
msgid "Rescan"
msgstr "重新扫描"

#: frmstringpointerscanunit.rsspsuscandonefound
msgid "Scan done! Found "
msgstr "扫描完成! 找到 "

#: frmstringpointerscanunit.rsstop
msgctxt "frmstringpointerscanunit.rsstop"
msgid "Stop"
msgstr "停止"

#: frmstringpointerscanunit.rsterminating
msgid "Terminating..."
msgstr "正在中止..."

#: frmstringpointerscanunit.rsthisaddressisnotaccessible
msgid "This address is not accessible"
msgstr "该地址不可访问"

#: frmstructurelinkerunit.strisnotavalidaddress
msgctxt "frmstructurelinkerunit.strisnotavalidaddress"
msgid "%s is not a valid address"
msgstr "%s 不是一个有效的地址"

#: frmstructures2elementinfounit.rss2eiifyoucontinuetheoldlocallydefinedtype
msgid "If you continue the old locally defined type %s will be deleted. Continue? (Tip: You can make this type into a global type so it can be re-used over again)"
msgstr "If you continue the old locally defined type %s will be deleted. Continue? (Tip: You can make this type into a global type so it can be re-used over again)"

#: frmstructures2elementinfounit.rss2eilocalstruct
msgid "Local struct:"
msgstr "局部的结构:"

#: frmstructuresconfigunit.rshighlighted
msgctxt "frmstructuresconfigunit.rshighlighted"
msgid "Highlighted"
msgstr "高亮显示"

#: frmstructuresconfigunit.rsnormal
msgctxt "frmstructuresconfigunit.rsnormal"
msgid "Normal"
msgstr "标准"

#: frmthreadlistunit.rscouldntobtaincontext
msgid "Couldn't obtain context"
msgstr "无法获取上下文"

#: frmthreadlistunit.rscouldntopenhandle
msgid "Couldn't open handle"
msgstr "无法打开句柄"

#: frmthreadlistunit.rspleasefirstattachthedebuggertothisprocess
msgid "Please first attach the debugger to this process"
msgstr "请先将调试器附加到此进程"

#: frmthreadlistunit.rstl1bytes
msgid "1 字节"
msgstr "1 字节"

#: frmthreadlistunit.rstl2bytes
msgid "2 字节"
msgstr "2 字节"

#: frmthreadlistunit.rstl4bytes
msgid "4 字节"
msgstr "4 字节"

#: frmthreadlistunit.rstl8bytes
msgid "8 bytes"
msgstr "8 字节"

#: frmthreadlistunit.rstlaccess
msgid "Access"
msgstr "访问"

#: frmthreadlistunit.rstlchangevalue
msgctxt "frmthreadlistunit.rstlchangevalue"
msgid "Change value"
msgstr "改变数值"

#: frmthreadlistunit.rstlexecute
msgctxt "frmthreadlistunit.rstlexecute"
msgid "Execute"
msgstr "执行"

#: frmthreadlistunit.rstlfailederrorcode
msgid "failed. Errorcode="
msgstr "失败. 错误码="

#: frmthreadlistunit.rstlio
msgid "I/O"
msgstr "I/O"

#: frmthreadlistunit.rstlwhatshouldthenewvalueofthisregisterbe
msgid "What should the new value of this register be?"
msgstr "此寄存器的新值是什么?"

#: frmthreadlistunit.rstlwrite
msgid "Write"
msgstr "写入"

#: frmtracerunit.rssearch
msgctxt "frmtracerunit.rssearch"
msgid "Search"
msgstr "搜索"

#: frmtracerunit.rstypetheluaconditionyouwanttosearchforexampleeax0x1
msgctxt "frmtracerunit.rstypetheluaconditionyouwanttosearchforexampleeax0x1"
msgid "Type the (LUA) condition you want to search for (Example: EAX==0x1234)"
msgstr "键入(LUA)要搜索的条件(如: EAX==0x1234)"

#: frmultimap2unit.rscantbeparsed
msgid "\" can't be parsed"
msgstr "\" 无法解析"

#: frmultimap2unit.rsclosingwillfreeallcollecteddata
msgid "Closing will free all collected data. Continue? (Tip: You can minimize this window instead)"
msgstr "关闭此窗口将会释放所有收集到的数据. 要继续吗? 小贴士: 你可以最小化此窗口)"

#: frmultimap2unit.rscpu
msgid "CPU"
msgstr "CPU"

#: frmultimap2unit.rsdasherror
msgid " -error"
msgstr " -error"

#: frmultimap2unit.rsdoesntexistandcantbecreated
msgid " does not exist and can not be created"
msgstr " 不存在,无法创建"

#: frmultimap2unit.rsfailureloadinglibipt
msgid "Failure loading libipt"
msgstr "加载 libipt 失败"

#: frmultimap2unit.rsfirstopenaprocess
msgid "First open a process"
msgstr "首先打开一个进程"

#: frmultimap2unit.rsforsomeweirdreason
msgid "For some weird reason \""
msgstr "For some weird reason \""

#: frmultimap2unit.rsinstructionpointerlistsize
msgctxt "frmultimap2unit.rsinstructionpointerlistsize"
msgid "Instruction Pointer List Size:"
msgstr "指令/指针列表大小:"

#: frmultimap2unit.rsisaninvalidrange
msgid " is an invalid range"
msgstr " 是一个无效的范围"

#: frmultimap2unit.rsmaxamountofrangesreachedforyourcpu
msgid "Max amount of ranges reached for your CPU. Clear one first"
msgstr "Max amount of ranges reached for your CPU. Clear one first"

#: frmultimap2unit.rsmodulelist
msgid "Module list"
msgstr "模块列表"

#: frmultimap2unit.rsonlyforintelcpus
msgid "Sorry, but Ultimap2 only works on Intel CPU's"
msgstr "很抱歉,但 Ultimap2 仅在intel CPU上工作"

#: frmultimap2unit.rspaused
msgctxt "frmultimap2unit.rspaused"
msgid "Paused"
msgstr "已暂停"

#: frmultimap2unit.rsprocessingdata
msgid ""
"Processing\n"
"Data\n"
msgstr "已处理\n数据\n"

#: frmultimap2unit.rsputbetweentomarsasanautostoprange
msgid "(Put between *'s to mark as an auto stop range)"
msgstr "(Put between *'s to mark as an auto stop range)"

#: frmultimap2unit.rsrangesemptyforallmax
msgid "Ranges: (Empty for all) (Max %d)"
msgstr "范围: (全部清空) (最大 %d)"

#: frmultimap2unit.rsrecording2
msgid "Recording"
msgstr "记录中"

#: frmultimap2unit.rsselectamoduleorgiveyourownrange
msgid "Select a module or give your own range"
msgstr "选择一个模块或给出你的范围"

#: frmultimap2unit.rssorrybutyourcpudoesntseemtobeabletosetatargetprocess
msgid "Sorry, but your CPU doesn't seem to be able to set a target PROCESS"
msgstr "Sorry, but your CPU doesn't seem to be able to set a target PROCESS"

#: frmultimap2unit.rssorrybutyourcpuseemstobeleackingiptfeature
msgid "Sorry, but your CPU seems to be lacking the Intel Processor Trace feature which Ultimap2 makes use of"
msgstr "Sorry, but your CPU seems to be lacking the Intel Processor Trace feature which Ultimap2 makes use of"

#: frmultimap2unit.rssorrybutyourcpusimplementationoftheiptfeatureistooold
msgid "Sorry, but your CPU's implementation of the Intel Processor Trace feature is too old. Ultimap uses multiple ToPA entries"
msgstr "Sorry, but your CPU's implementation of the Intel Processor Trace feature is too old. Ultimap uses multiple ToPA entries"

#: frmultimap2unit.rstargetadifferentprocess
msgid "Target a different process. Ultimap2 will suspend the target when the buffer is full, and suspending the thing that empties the buffer is not a good idea"
msgstr "Target a different process. Ultimap2 will suspend the target when the buffer is full, and suspending the thing that empties the buffer is not a good idea"

#: frmultimap2unit.rstherangeyouhaveprovidedisanexitrangebeaware
msgid "The range you have provided is an 'Exit' range. Be aware that this doesn't mean it will always stop at that range, or that the result is what you expect. A context switch to another thread between the start and stop can add a lot of other data"
msgstr "The range you have provided is an 'Exit' range. Be aware that this doesn't mean it will always stop at that range, or that the result is what you expect. A context switch to another thread between the start and stop can add a lot of other data"

#: frmultimap2unit.rsthesizehastobe12kborhigher
msgid "The size has to be 12KB or higher"
msgstr "大小必须为12KB或更大"

#: frmultimapunit.rsremovehotkey
msgid "Remove hotkey (%s)"
msgstr "移除热键 (%s)"

#: frmultimapunit.rsuuerrorduringmap
msgid "Error during map"
msgstr "在映射时出错"

#: frmultimapunit.rsuulastfilterresults
msgid "Last filter results: filtered out %d left: %d"
msgstr "最终过滤结果: 被过滤的 %d 离开: %d"

#: frmultimapunit.rsuunew
msgid " new="
msgstr " 新="

#: frmultimapunit.rsuuold
msgid "old="
msgstr "旧="

#: frmultimapunit.rsuupause
msgctxt "frmultimapunit.rsuupause"
msgid "Pause"
msgstr "暂停"

#: frmultimapunit.rsuupleaserunthe64bitversionofcheatenginetomakeuseofthisfeature
msgid "Please run the 64-bit version of Cheat Engine to make use of this feature"
msgstr "要使用到此特性请运行64位版本的Cheat Engine"

#: frmultimapunit.rsuuresume
msgctxt "frmultimapunit.rsuuresume"
msgid "Resume"
msgstr "恢复"

#: frmultimapunit.rsuusorrybutthisfeatureisonlyavailableonintelcpus
msgid "Sorry, but this feature is only available on intel cpu's"
msgstr "很抱歉, 但此功能仅在intel的CPU中有效"

#: frmultimapunit.rsuuthemaximumnumberofworkersis64
msgid "The maximum number of workers is 64"
msgstr "线程的最大数量是64"

#: frmultimapunit.rsuuthisfunctionneedsatleast200bytesfortheheaderofthebuffer
msgid "This function needs at least 200 bytes for the header of the buffer"
msgstr "这个函数需要至少200字节的文件头缓冲区"

#: frmultimapunit.rsuuthiswillbringbackallfoundinstructions
msgid "This will bring back all found instructions. Continue?"
msgstr "将恢复所有找到的指令. 要继续吗?"

#: frmultimapunit.rsuuthiswillresetthecallcountoffunctionbackto0
msgid "This will reset the callcount of functions back to 0. This can not be undone. Continue?"
msgstr "将重置 callcount 的函数并返回 0. 此操作无法取消. 要继续吗?"

#: frmwatchlistunit.rsunparsable
msgid "<Unparsable>"
msgstr "<不可读>"

#: frmwatchlistunit.rsunreadable
msgid "<Unreadable>"
msgstr "<不可读>"

#: gnuassembler.rsbyteswhile
msgid " bytes while "
msgstr " 字节写入 "

#: gnuassembler.rsconfigureavalidbinutilssetupfirst
msgid "Configure a valid binutils setup first"
msgstr "请先配置一个有效的binutils"

#: gnuassembler.rscouldnotbefound
msgctxt "gnuassembler.rscouldnotbefound"
msgid " could not be found"
msgstr " 无法找到"

#: gnuassembler.rsdoesntseemtobeavalidelffile
msgid " doesn't seem to be a valid ELF file"
msgstr " 似乎不是一个有效的ELF文件"

#: gnuassembler.rsgnuassemblererror
msgid "GNU Assembler error"
msgstr "GNU 汇编器错误"

#: gnuassembler.rsinvalidelffile
msgid "Invalid ELF file"
msgstr "ELF 文件无效"

#: gnuassembler.rsnobinutilsinstalled
msgid "No binutils installed"
msgstr "未安装binutils"

#: gnuassembler.rstheaobfor
msgid "The AOB for "
msgstr "AOB为 "

#: gnuassembler.rstheproperformatofasectionis
msgid "The proper format of .asection is : .asection <name> <preferedaddress>"
msgstr ".asection 的格式是 : .asection <name> <preferedaddress>"

#: gnuassembler.rstheproperformatofmsectionis
msgid "The proper format of .msection is : .msection <name> <address> <optional expected size>"
msgstr ".msection 的格式是 : .msection <name> <address> <optional expected size>"

#: gnuassembler.rstook
msgid " took "
msgstr " 采用 "

#: gnuassembler.rswhereexpected
msgid " where expected"
msgstr " 预计"

#: groupscancommandparser.rsgscpcustomtypenotrecognized
msgid "Custom type not recognized: "
msgstr "自定义类型无法识别: "

#: groupscancommandparser.rsgscpinvalidgroupscancommand
msgid "Invalid groupscan command"
msgstr "无效的\"群组扫描\"参数"

#: groupscancommandparser.rsgscpwildcardsemptyarenotallowedforoutoforderscans
msgid "Wildcards/Empty are not allowed for Out of Order scans"
msgstr "\"次序颠倒\"方式不允许使用通配符/空白"

#: guisafecriticalsection.rscriticalsectionleavewithoutenter
msgid "Criticalsection leave without enter"
msgstr "移开无入口的关键字段"

#: hexviewunit.rsaddress
msgctxt "hexviewunit.rsaddress"
msgid "address"
msgstr "地址"

#: hexviewunit.rsbase
msgid "Base"
msgstr "基址"

#: hexviewunit.rsbigfuckingerror
msgid "Big fucking error"
msgstr "未知错误"

#: hexviewunit.rsbytes
msgctxt "hexviewunit.rsbytes"
msgid "字节"
msgstr "字节"

#: hexviewunit.rsexecute
msgctxt "hexviewunit.rsexecute"
msgid "Execute"
msgstr "可执行"

#: hexviewunit.rsexecutereadonly
msgid "Execute/Read only"
msgstr "可执行/只读"

#: hexviewunit.rsexecutereadwrite
msgid "Execute/Read/Write"
msgstr "可执行/读/写"

#: hexviewunit.rsexecutewritecopy
msgid "Execute/Write Copy"
msgstr "可执行/写时拷贝"

#: hexviewunit.rsguarded
msgid "Guarded"
msgstr "写保护"

#: hexviewunit.rsinvalidbytesperseperatorvalue
msgid "Invalid BytesPerSeperator value:%s"
msgstr "无效的字节拼接值:%s"

#: hexviewunit.rsmodule
msgid "Module"
msgstr "模块"

#: hexviewunit.rsnoaccess
msgctxt "hexviewunit.rsnoaccess"
msgid "No Access"
msgstr "不可访问"

#: hexviewunit.rsnotcached
msgid "Not Cached"
msgstr "无法缓存"

#: hexviewunit.rsphysicaladdress
msgid "Physical Address"
msgstr "物理地址"

#: hexviewunit.rsprotect
msgctxt "hexviewunit.rsprotect"
msgid "Protect"
msgstr "保护"

#: hexviewunit.rsreadonly
msgid "Read Only"
msgstr "只读"

#: hexviewunit.rsreadwrite
msgid "Read/Write"
msgstr "读/写"

#: hexviewunit.rssize
msgctxt "hexviewunit.rssize"
msgid "Size"
msgstr "长度"

#: hexviewunit.rsthislookslikeanarrayofbytedoyouwanttoinputitasahex
msgid "This looks like an array of byte. Do you want to input it as a hexadecimal string?"
msgstr "这看起来像一个字节数组. 你想视为16进制并输入吗?"

#: hexviewunit.rsthislookslikeanormalstringdoyouwanttoinputitasastr
msgid "This looks like a normal string. Do you want to input it as a string ?"
msgstr "这看起来像一个普通字串. 你想视为字串并输入吗?"

#: hexviewunit.rswritecopy
msgctxt "hexviewunit.rswritecopy"
msgid "Write Copy"
msgstr "写时拷贝"

#: hotkeys.rsdecreasevaluewith
msgid "Decrease value with:"
msgstr "(按下热键后)数值减少:"

#: hotkeys.rsdefaultactivated
msgid "%s Activated"
msgstr "%s 已激活"

#: hotkeys.rsdefaultdeactivated
msgid "%s Deactivated"
msgstr "%s 已禁用"

#: hotkeys.rsdisablescript
msgid "Disable script"
msgstr "禁用脚本"

#: hotkeys.rsenablescript
msgid "Enable script"
msgstr "启用脚本"

#: hotkeys.rsfreeze
msgid "Freeze"
msgstr "锁定"

#: hotkeys.rshotkeyid
msgid "Hotkey ID=%s"
msgstr "热键 ID=%s"

#: hotkeys.rsincreasevaluewith
msgid "Increase value with:"
msgstr "(按下热键后)数值增加:"

#: hotkeys.rssetvalueto
msgid "Set value to:"
msgstr "设置数值为:"

#: hotkeys.rsspeaktext
msgid "Speak Text"
msgstr "说明文本"

#: hotkeys.rstexttospeechhint
msgid ""
"The text to speak\n"
"%s = The description field of the memory record\n"
"%s = The description of the hotkey\n"
msgstr "说明文本\n%s = 记录内存段的描述\n\n%s = 热键的描述\n"

#: hotkeys.rstogglefreeze
msgctxt "hotkeys.rstogglefreeze"
msgid "Toggle freeze"
msgstr "切换锁定/解锁"

#: hotkeys.rstogglefreezeandallowdecrease
msgid "Toggle freeze and allow decrease"
msgstr "切换锁定/解锁但允许减少"

#: hotkeys.rstogglefreezeandallowincrease
msgid "Toggle freeze and allow increase"
msgstr "切换锁定/解锁但允许增加"

#: hotkeys.rstogglescript
msgid "Toggle script"
msgstr "开启/关闭脚本"

#: hotkeys.rsunfreeze
msgid "Unfreeze"
msgstr "解锁"

#: iconstuff.rsfailureopen
msgid "Failure opening "
msgstr "打开失败 "

#: iconstuff.rsiconpicker
msgid "Icon picker"
msgstr "图标选择器"

#: iconstuff.rsnoiconfound
msgid "No icon found in this file"
msgstr "这个文件没有找到图标"

#: iconstuff.rsunhandledext
msgid "Unhandled file extension"
msgstr "未知的文件扩展名"

#: kerneldebuggerinterface.rsdbkdebug_startdebuggingfailed
msgid "DBKDebug_StartDebugging failed"
msgstr "DBKDebug_StartDebugging 失败"

#: luacaller.rsautoassemblercallbackluafunctionerror
msgid "AutoAssemblerCallback: Lua Function error("
msgstr "AutoAssemblerCallback: Lua Function错误("

#: luacaller.rsisnotyetsupported
msgctxt "luacaller.rsisnotyetsupported"
msgid " is not yet supported"
msgstr " 暂不支持"

#: luacaller.rsstructuredissecteventluafunctionerror
msgid "StructureDissectEvent: Lua Function error("
msgstr "StructureDissectEvent: Lua 函数错误("

#: luacaller.rsthistypeofmethod
msgctxt "luacaller.rsthistypeofmethod"
msgid "This type of method:"
msgstr "此类型的方法:"

#: luaclass.rsinvalidclassobject
msgid "Invalid class object"
msgstr "无效的类对象"

#: luadissectcode.rscouldnotbefound
msgctxt "luadissectcode.rscouldnotbefound"
msgid " could not be found"
msgstr " 无法找到"

#: luadissectcode.rsinvalidparametersfordissect
msgid "Invalid parameters for dissect"
msgstr "无效的解析参数"

#: luadissectcode.rsthemodulenamed
msgid "The module named "
msgstr "给出的模块名 "

#: luaform.rsthegivenformisnotcompatible
msgid "The given form is not compatible. Formclass="
msgstr "是不受支持的. Formclass="

#: luafoundlist.rscreatefoundlistneedsamemscanobjectasparameter
msgid "createfoundlist needs a memscan object as parameter"
msgstr "createfoundlist 需要一个 memscan 对象作为参数"

#: luahandler.rscheatengineisbeingafag
msgid "Cheatengine is being a fag"
msgstr "Cheat Engine 是个吃力的工作"

#: luahandler.rsconditionalbreakpointerror
msgid "Conditional breakpoint error"
msgstr "条件断点错误"

#: luahandler.rscreatememscanneedsaprogressbarornil
msgid "createMemScan needs a progressbar or nil. "
msgstr "createMemScan 需要一个参数或nil "

#: luahandler.rsdeallocatesharedmemoryisnotimplemented
msgid "deallocateSharedMemory is not implemented (It's not even in the list of available functions)"
msgstr "deallocateSharedMemory 功能暂未实现 "

#: luahandler.rsdebugsetbreakpointneedsatleastanaddress
msgid "debug_setBreakpoint needs at least an address"
msgstr "debug_setBreakpoint 需要至少一个地址"

#: luahandler.rserror
msgid "Error:"
msgstr "错误:"

#: luahandler.rserror2
msgid " error:"
msgstr " 错误:"

#: luahandler.rserror3
msgid " error"
msgstr " 错误"

#: luahandler.rsgetprocesslisttheprovidedlistobjectisnotvalid
msgid "getProcessList: the provided List object is not valid"
msgstr "getProcessList: 提供的列表对象是无效的"

#: luahandler.rsgetthreadlisttheprovidedlistobjectisnotvalid
msgid "getThreadlist: the provided List object is not valid"
msgstr "getThreadlist: 提供的列表对象是无效的"

#: luahandler.rsincorrectnumberofparameters
msgid "Incorrect number of parameters"
msgstr "参数的数目不正确"

#: luahandler.rsinvalidfloat
msgid "Invalid floating point string:%s"
msgstr "无效的浮点字串:%s"

#: luahandler.rsinvalidint
msgid "Invalid integer:%s"
msgstr "无效的整数:%s"

#: luahandler.rsisnotaprogressbar
msgid " is not a progressbar"
msgstr " 不是一个进度条"

#: luahandler.rsluapanic
msgid "LUA panic!"
msgstr "LUA panic!"

#: luahandler.rslua_doscriptwasnotcalledromthemainthread
msgid "LUA_DoScript was not called from the main thread"
msgstr "LUA_DoScript 无法从主线程中调用"

#: luahandler.rsmainluaerror
msgid "main.lua error:"
msgstr "main.lua 错误:"

#: luahandler.rsmainluaerror2
msgid "main.lua error"
msgstr "main.lua 错误"

#: luahandler.rsnumberrequired
msgid "Number required"
msgstr "数目要求"

#: luahandler.rsplaysoundtheparametermustbeatablefileoramemorystream
msgid "playSound: The parameter must be a table file or a memory stream. Nothing else"
msgstr "playSound: 参数仅支持表单文件或内存流. "

#: luahandler.rspluginaddress
msgctxt "luahandler.rspluginaddress"
msgid "Plugin Address"
msgstr "插件地址"

#: luahandler.rsthistypeisnotsupportedhere
msgid "This type is not supported here"
msgstr "这里不支持此类型"

#: luahandler.rsundefinedluaerror
msgid "Undefined lua error"
msgstr "未定义的 lua 错误"

#: luamemscan.rsnotallparametershavebeenprovided
msgid "Not all parameters have been provided"
msgstr "未提供全部参数"

#: luaobject.rsisnotyetsupported
msgctxt "luaobject.rsisnotyetsupported"
msgid " is not yet supported"
msgstr " 暂不支持"

#: luaobject.rsthisisaninvalidclassormethodproperty
msgid "This is an invalid class or method property"
msgstr "这是一个无效的类或方法属性"

#: luaobject.rsthistypeofmethod
msgctxt "luaobject.rsthistypeofmethod"
msgid "This type of method:"
msgstr "此类型的方法:"

#: luastructurefrm.rsgroup1
msgctxt "luastructurefrm.rsgroup1"
msgid "Group 1"
msgstr "群组 1"

#: luastructurefrm.rsgroupd
msgid "Group %d"
msgstr "群组 %d"

#: luasyntax.syns_attrluamstring
msgid "LuaMString"
msgstr "LuaMString"

#: luasyntax.syns_attrnumber
msgid "Numbers"
msgstr "编号"

#: luasyntax.syns_filterlua
msgid "Lua Files (*.lua, *.lpr)|*.lua;*.lpr"
msgstr "Lua 文件 (*.lua, *.lpr)|*.lua;*.lpr"

#: luasyntax.syns_langlua
msgid "Lua"
msgstr "Lua"

#: luatablefile.rscreatetablefilerequiresatleastoneparameter
msgid "createTableFile requires at least one parameter"
msgstr "createTableFile 需要至少一个参数"

#: luatablefile.rserrorraisedwithmessage
msgid " error raised with message: "
msgstr " 错误信息: "

#: luatablefile.rstabledileentryalreadyexistsforfilename
msgid "Table file entry already exists for filename: "
msgstr "此文件名的表单文件已存在于: "

#: luathread.rserrorinnativethreadcalled
msgid "Error in native thread called "
msgstr "本地线程中的called错误 "

#: luathread.rsinnativecode
msgid " in native code:"
msgstr " 位于本机代码:"

#: luathread.rsinvalidfirstparameterforcreatenativethread
msgid "Invalid first parameter for createNativeThread"
msgstr "createNativeThread 的第一个参数无效"

#: lua_server.rsalreadyexists
msgid " already exists"
msgstr " 已存在"

#: lua_server.rsaluaserverwiththename
msgid "A luaserver with the name "
msgstr "A luaserver with the name "

#: mainunit.rsand
msgctxt "mainunit.rsand"
msgid "and"
msgstr "至"

#: mainunit.rsaprilfools
msgid "April fools!!!!"
msgstr "愚人节!!!!"

#: mainunit.rsareyousureyouwanttodelete
msgctxt "mainunit.rsareyousureyouwanttodelete"
msgid "Are you sure you want to delete %s?"
msgstr "你确定要删除 %s?"

#: mainunit.rsareyousureyouwanttodeletethisform
msgid "Are you sure you want to delete this form?"
msgstr "你确定要删除这个窗体吗?"

#: mainunit.rsareyousureyouwanttoerasethedatainthecurrenttable
msgid "Are you sure you want to erase the data in the current table?"
msgstr "你确定要清除当前列表中的数据吗?"

#: mainunit.rsatleastxx
msgid "at least xx%"
msgstr "至少 xx%"

#: mainunit.rsautoassembleedit
msgid "Auto Assemble edit: %s"
msgstr "编辑自动汇编: %s"

#: mainunit.rsbetween
msgid "between %"
msgstr "介于 % 之间"

#: mainunit.rsbringscheatenginetofront
msgid "brings Cheat Engine to front"
msgstr "把Cheat Engine带到前面"

#: mainunit.rsbusy
msgid "<busy>"
msgstr "<繁忙>"

#: mainunit.rscancel
msgctxt "mainunit.rscancel"
msgid "Cancel"
msgstr "取消"

#: mainunit.rsceerror
msgid "CE Error:"
msgstr "CE 错误:"

#: mainunit.rschangevalue
msgctxt "mainunit.rschangevalue"
msgid "Change value"
msgstr "更改数值"

#: mainunit.rscheatengine
msgid "Cheat Engine"
msgstr "Cheat Engine"

#: mainunit.rschooselanguage
msgid "Which language do you wish to use?"
msgstr "你想要使用哪种语言?"

#: mainunit.rscomparingto
msgid "Comparing to %s"
msgstr "和 %s 比较"

#: mainunit.rscomparingtof
msgid "Comparing to first scan results"
msgstr "与首次扫描结果对比"

#: mainunit.rscurrentprocess
msgid "Current process"
msgstr "当前进程"

#: mainunit.rscustomluatype
msgid "Custom LUA type"
msgstr "自定义 LUA 类型"

#: mainunit.rscustomtypename
msgid "Custom Type Name"
msgstr "自定义类型的名称"

#: mainunit.rsdecimal
msgctxt "mainunit.rsdecimal"
msgid "Decimal"
msgstr "十进制"

#: mainunit.rsdelete
msgctxt "mainunit.rsdelete"
msgid "Delete"
msgstr "删除"

#: mainunit.rsdoyouwanttogotothecheatenginewebsite
msgid "Do you want to go to the Cheat Engine website?"
msgstr "你想要访问 Cheat Engine 的网站吗?官方网站在国外,部分用户可能打不开！"

#: mainunit.rsdoyouwanttoprotectthistrainerfilefromediting
msgid "Do you want to protect this trainer file from editing?"
msgstr "你是否要锁定这个修改器窗口?"

#: mainunit.rsdoyouwishtomergethecurrenttablewiththistable
msgid "Do you wish to merge the current table with this table?"
msgstr "你想合并到现有表单中吗?"

#: mainunit.rsedit
msgctxt "mainunit.rsedit"
msgid "Edit"
msgstr "编辑"

#: mainunit.rseditaddresses
msgid "Edit addresses"
msgstr "编辑地址"

#: mainunit.rsexpired
msgid "EXPIRED"
msgstr "→今天是每年的4月1日愚人节！没想到今天还有人在用CE！"

#: mainunit.rsfailedtoload
msgid "failed to load"
msgstr "无法加载"

#: mainunit.rsfailuresettingthecreateglobalprivilege
msgid "Failure setting the CreateGlobal privilege."
msgstr "设置 CreateGlobal 的权限失败."

#: mainunit.rsfailuresettingthedebugprivilege
msgid "Failure setting the debug privilege. Debugging may be limited."
msgstr "设置调试器权限失败. 调试器的性能受到限制."

#: mainunit.rsfailuresettingtheloaddriverprivilege
msgid "Failure setting the load driver privilege. Debugging may be limited."
msgstr "设置加载驱动的权限失败. 调试器的性能受到限制."

#: mainunit.rsfailuresettingthesetcbprivilegeprivilege
msgid "Failure setting the SeTcbPrivilege privilege. Debugging may be limited."
msgstr "设置任务控制特权失败，调试权限受限."

#: mainunit.rsfileinuse
msgid "<File in use>"
msgstr "<文件正在使用>"

#: mainunit.rsfindoutwhataccessesthispointer
msgid "Find out what accesses this pointer"
msgstr "找出是什么访问了该指针"

#: mainunit.rsfindoutwhatwritesthispointer
msgid "Find out what writes this pointer"
msgstr "找出是什么改写了该指针"

#: mainunit.rsfindwhataccessestheaddresspointedatbythispointer
msgid "Find what accesses the address pointed at by this pointer"
msgstr "找出是什么访问了该指针指向的地址"

#: mainunit.rsfindwhatwritestheaddresspointedatbythispointer
msgid "Find what writes the address pointed at by this pointer"
msgstr "找出是什么改写了该指针指向的地址"

#: mainunit.rsforcetermination
msgid "Force termination"
msgstr "强制终止"

#: mainunit.rsgivethenewfilename
msgid "Give the new filename"
msgstr "获取新的文件名"

#: mainunit.rsgivethenewvaluefortheselectedaddresses
msgid "Give the new value for the selected address(es)"
msgstr "请给所选中的地址提供一个新的数值"

#: mainunit.rsgroup
msgid "Group %s"
msgstr "群组 %s"

#: mainunit.rsgroups
msgid "Groups"
msgstr "群组"

#: mainunit.rsgroupscandatainvalid
msgid "groupscan data invalid"
msgstr "\"群组扫描\"数据无效"

#: mainunit.rsgroupscanresultwithnogroupscanparser
msgid "Groupscan result with no groupscanparser"
msgstr "Groupscan result with no groupscanparser"

#: mainunit.rsheight
msgid " , height="
msgstr " , height="

#: mainunit.rshex
msgctxt "mainunit.rshex"
msgid "Hex"
msgstr "十六进制"

#: mainunit.rshexadecimal
msgctxt "mainunit.rshexadecimal"
msgid "Hexadecimal"
msgstr "十六进制"

#: mainunit.rshrmpfbecauseiminagoodmoodillletyougothistimebutdon
msgid "Hrmpf... Because I'm in a good mood i'll let you go this time. But don't do it again you filthy pirate"
msgstr "嗯... 今儿我心情好就饶你一次, 下不为例, 无耻的盗版者"

#: mainunit.rsinvalidscanfolder
msgid "%s is not accessible like it should.  Please configure a proper location in the settings"
msgstr "%s 是不可访问的.  请在设置中配置合适的位置"

#: mainunit.rsinvalidstartaddress
msgid "Invalid start address: %s"
msgstr "无效的起始地址: %s"

#: mainunit.rsinvalidstopaddress
msgid "Invalid stop address: %s"
msgstr "无效的终止地址: %s"

#: mainunit.rsisnotavalidspeed
msgid "%s is not a valid speed"
msgstr "%s 速度无效"

#: mainunit.rsisnotavalidx
msgid "%s is not a valid xml name"
msgstr "%s 无效的xml名称"

#: mainunit.rslanguage
msgid "Language"
msgstr "语言"

#: mainunit.rslicenseexpired
msgid "Your license to use Cheat Engine has expired. You can buy a license to use cheat engine for 1 month for $200, 6 months for only $1000 and for 1 year for only $1800. If you don't renew your license Cheat Engine will be severely limited in it's abilities. (e.g: Next scan has been disabled)"
msgstr "CE7.0.1红尘汉化版提示：今天是每年的4月1号愚人节哦！哈哈,我只是没想到今天愚人节还有人在使用CE！"

#: mainunit.rsloadtheassociatedtable
msgid "Load the associated table? (%s)"
msgstr "是否载入关联的表单? (%s)"

#: mainunit.rsluascriptcheattable
msgid "Lua script: Cheat Table"
msgstr "Lua 脚本: CT表"

#: mainunit.rsmugenerategroupscancommand
msgctxt "mainunit.rsmugenerategroupscancommand"
msgid "Generate groupscan command"
msgstr "生成\"群组扫描\"参数"

#: mainunit.rsnone
msgid "<none>"
msgstr "<无>"

#: mainunit.rsoverlay
msgid "overlay "
msgstr "覆盖 "

#: mainunit.rspart
msgid " part "
msgstr " part "

#: mainunit.rsprevious
msgctxt "mainunit.rsprevious"
msgid "Previous"
msgstr "先前值"

#: mainunit.rsprocessing
msgid "<Processing>"
msgstr "<处理中>"

#: mainunit.rsremoveselectedaddress
msgid "Remove selected address"
msgstr "删除已选中的地址"

#: mainunit.rsremoveselectedaddresses
msgctxt "mainunit.rsremoveselectedaddresses"
msgid "Remove selected addresses"
msgstr "删除已选中的地址"

#: mainunit.rsrename
msgctxt "mainunit.rsrename"
msgid "Rename"
msgstr "重命名"

#: mainunit.rsrenamefile
msgid "Rename file"
msgstr "重命名文件"

#: mainunit.rsrestoreandshow
msgid "Restore and show"
msgstr "恢复显示"

#: mainunit.rssaved
msgid "Saved"
msgstr "已保存"

#: mainunit.rssavedscanresults
msgid "Saved scan results"
msgstr "已保存的扫描结果"

#: mainunit.rssavescanresults
msgid "Save scan results"
msgstr "保存扫描结果"

#: mainunit.rssavetodisk
msgctxt "mainunit.rssavetodisk"
msgid "Save to disk"
msgstr "保存到磁盘"

#: mainunit.rsscan
msgctxt "mainunit.rsscan"
msgid "Scan"
msgstr "扫描"

#: mainunit.rsscanerror
msgid "Scan error:%s"
msgstr "扫描出错:%s"

#: mainunit.rsscanresult
msgid "Scanresult"
msgstr "扫描结果"

#: mainunit.rsselectthesavedscanresultfromthelistbelow
msgid "Select the saved scan result from the list below"
msgstr "从下面的列表中选择已保存的扫描结果"

#: mainunit.rssetchangehotkeys
msgid "Set/Change hotkeys"
msgstr "设定/更改热键"

#: mainunit.rssethotkeys
msgctxt "mainunit.rssethotkeys"
msgid "Set hotkeys"
msgstr "设定热键"

#: mainunit.rsshowasbinary
msgctxt "mainunit.rsshowasbinary"
msgid "Show as binary"
msgstr "以二进制显示"

#: mainunit.rsshowasdecimal
msgid "Show as decimal"
msgstr "以十进制显示"

#: mainunit.rsshowashexadecimal
msgctxt "mainunit.rsshowashexadecimal"
msgid "Show as hexadecimal"
msgstr "以十六进制显示"

#: mainunit.rsshown
msgid "shown"
msgstr "显示"

#: mainunit.rsterminatingscan
msgid "Terminating scan..."
msgstr "正在中止扫描..."

#: mainunit.rsthankyoufortryingoutcheatenginebecauseithasexpired
msgid "Thank you for trying out Cheat Engine. Because it has expired Cheat Engine will now close. Is that ok with you?"
msgstr "感谢你试用 Cheat Engine, 由于 Cheat Engine 已过期即将关闭. 你没问题吧?"

#: mainunit.rstheprocessisntfullyopenedindicatingainvalidprocess
msgid "The process isn't fully opened. Indicating a invalid ProcessID. You still want to find out the EPROCESS? (BSOD is possible)"
msgstr "进程没有完全打开. 指定的 ProcessID 无效. 你仍想找出 EPROCESS 吗? (可能蓝屏死机)"

#: mainunit.rsthereareoneormoreautoassemblerentriesorcodechanges
msgid "There are one or more auto assembler entries or code changes enabled in this table. Do you want them disabled? (without executing the disable part)"
msgstr "在表单中已启用或更改了一个/多个自动汇编代码. 要禁用吗? (不包括运行中的禁用部分)"

#: mainunit.rstherecordwithdescriptionhasasinterpretableaddresst
msgid "The record with description '%s' has as interpretable address '%s'. The recalculation will change it to %s. Do you want to edit it to the new address?"
msgstr "带描述的记录 '%s' 拥有着可判断的地址 '%s'. 重新计算将使其更改为 %s. 你想编辑这个新地址吗?"

#: mainunit.rsthisbuttonwillforcecancelascanexpectmemoryleaks
msgid "This button will force cancel a scan. Expect memory leaks"
msgstr "将强制取消扫描.预计可能内存泄漏"

#: mainunit.rsthisbuttonwilltrytocancelthecurrentscanclicktwicet
msgid "This button will try to cancel the current scan. Click twice to force an exit"
msgstr "将取消当前的扫描. 点击两次强制退出"

#: mainunit.rsthislistishuge
msgid "This list is huge and deleting multiple items will require CE to traverse the whole list and can take a while. Are you sure?"
msgstr "列表数据较多, 删除多项列表数据需要 CE 遍历整个列表并花费一些时间. 你确定吗?"

#: mainunit.rstrytutorial
msgid "Do you want to try out the tutorial?"
msgstr "你想尝试教程吗?"

#: mainunit.rsunabletoscanfixyourscansettings
msgid "Unable to scan. Fix your scan settings and restart cheat engine"
msgstr "无法扫描. 请修复你的扫描设置并重新启动cheat engine"

#: mainunit.rsunrandomizerinfo
msgid "This will scan for and change some routines that are commonly used to generate a random value so they always return the same. Please be aware that there is a chance it overwrites the wrong routines causing the program to crash, or that the program uses an unknown random generator. Continue?"
msgstr "扫描生成随机数的程序并将随机数更改为同样的数值. 请注意有一定机率会导致程序崩溃, 如果程序使用了未知的随机数发生器则无法变更. 是否继续?"

#: mainunit.rsunspecifiederror
msgid "Unspecified error"
msgstr "未知的错误"

#: mainunit.rsvalue
msgid "Value %"
msgstr "数值 %"

#: mainunit.rswasclickedatpositon
msgid " was clicked at positon "
msgstr " 被点击的位置 "

#: mainunit.rswhatareyousayingyouregoingtocontinueusingceillegal
msgid "WHAT!!! Are you saying you're going to continue using CE ILLEGALLY??? If you say yes, i'm going to mail the cops to get you and send you to jail!!!"
msgstr "什么!!! 你说你打算继续非法使用 CE ??? 我会给警察发封邮件让他们把你送去坐牢!!!"

#: mainunit.rswhatdoyouwantthegroupnametobe
msgid "What do you want the groupname to be?"
msgstr "你想要的新的群组名是什么?"

#: mainunit.rswhatnamedoyouwanttogivetothesescanresults
msgid "What name do you want to give to these scanresults?"
msgstr "你想要给扫描结果输入什么名字?"

#: mainunit.rswhatwillbethenewnameforthistab
msgid "What will be the new name for this tab?"
msgstr "请输入新的标签名?"

#: mainunit.rswidth
msgid "   -   width="
msgstr "   -   width="

#: mainunit.rsyouarelowondiskspaceonthefolderwherethescanresults
msgid "You are low on diskspace on the folder where the scanresults are stored. Scanning might fail. Are you sure you want to continue?"
msgstr "扫描结果存储文件夹空间不足. 扫描可能会失败，你确定要继续吗?"

#: mainunit.straccessed
msgid "The following opcodes accessed the selected address"
msgstr "下列操作码访问了选中的地址"

#: mainunit.stradd0
msgid "Do you want to add a '0'-terminator at the end?"
msgstr "你要在尾部加入 '0' (零终止符)吗?"

#: mainunit.stralreadyin
msgctxt "mainunit.stralreadyin"
msgid "This address is already in the list"
msgstr "列表中已有相同的地址"

#: mainunit.stralreadyinlistmultiple
msgid "One or more addresses where already in the list"
msgstr "列表中已有一个或多个相同的地址"

#: mainunit.strasktosave
msgid "You haven't saved your last changes yet. Save Now?"
msgstr "你还没有保存最后的更改. 现在要保存吗?"

#: mainunit.strchange1value
msgid "Change this value to:"
msgstr "更改数值为:"

#: mainunit.strchangedescription1
msgctxt "mainunit.strchangedescription1"
msgid "Description"
msgstr "描述"

#: mainunit.strchangedescription2
msgid "Change the description to:"
msgstr "更改描述:"

#: mainunit.strchangemorevalues
msgid "Change these values to:"
msgstr "更改这些数值为:"

#: mainunit.strchangescript
msgctxt "mainunit.strchangescript"
msgid "Change script"
msgstr "更改脚本"

#: mainunit.strclicktogohome
msgid "Click here to go to the Cheat Engine homepage"
msgstr "点这里进入 Cheat Engine 主页"

#: mainunit.strconfirmprocesstermination
msgid "This will close the current process. Are you sure you want to do this?"
msgstr "将关闭当前进程, 你确定要这么做吗?"

#: mainunit.strconfirmundo
msgid "Do you really want to go back to the results of the previous scan?"
msgstr "你确定要返回之前的扫描结果吗?"

#: mainunit.strdeleteaddress
msgid "Delete this address"
msgstr "删除当前地址"

#: mainunit.strdeleteall
msgid "Are you sure you want to delete all addresses?"
msgstr "你确定要删除所有的地址吗?"

#: mainunit.strdeletetheseaddresses
msgid "Delete these addresses"
msgstr "删除这些地址"

#: mainunit.strdisablecheat
msgid "Disable cheat"
msgstr "停用金手指"

#: mainunit.strdontbother
msgid "Don't even bother. Cheat Engine uses the main thread to receive messages when the scan is done, freeze it and CE will crash!"
msgstr "不要动它. Cheat Engine 扫描完毕时使用主线程接收消息, 锁定它会使 CE 崩溃!"

#: mainunit.strenablecheat
msgid "Enable cheat"
msgstr "开启金手指"

#: mainunit.strerror
msgctxt "mainunit.strerror"
msgid "Error"
msgstr "错误"

#: mainunit.strerrorwhileopeningprocess
msgid "Error while opening this process"
msgstr "打开进程错误"

#: mainunit.strforcerecheck
msgctxt "mainunit.strforcerecheck"
msgid "Force recheck symbols"
msgstr "强制复查符号表"

#: mainunit.strfreezeaddressinlist
msgid "Freeze the address in this list"
msgstr "锁定列表中的地址"

#: mainunit.strfreezealladdresses
msgid "Freeze all addresses in this list"
msgstr "锁定列表中所有地址"

#: mainunit.strfuture
msgid "Wow,I never imagined people would use Cheat Engine up to today"
msgstr "呵呵,我没想到今天大家仍在使用 Cheat Engine"

#: mainunit.strhappybirthday
msgid "Let's sing Happy Birthday for Dark Byte today!"
msgstr "今天让我们为 Dark Byte 唱首生日歌!"

#: mainunit.strhideall
msgid "will hide all windows"
msgstr "将隐藏所有窗口"

#: mainunit.strhideforeground
msgid "will hide the foreground window"
msgstr "将隐藏前台窗口"

#: mainunit.strinfoabouttable
msgid "Info about this table:"
msgstr "关于表格信息:"

#: mainunit.strkeeplist
msgid "Keep the current address list/code list?"
msgstr "保留当前地址列表/代码列表吗?"

#: mainunit.strmorepointers
msgid "There are more pointers selected. Do you want to change them as well?"
msgstr "还有更多指针被选中. 你需要更改吗?"

#: mainunit.strmorepointers2
msgid "You have selected one or more pointers. Do you want to change them as well?"
msgstr "你已经选择了一个或多个的指针, 你需要更改吗?"

#: mainunit.strnewyear
msgid "And what are your good intentions for this year? ;-)"
msgstr "今年你有什么好的打算? ;-)"

#: mainunit.strnotavalidbinarynotation
msgid " is not a valid binary notation!"
msgstr " 不是一个有效的二进制记数法!"

#: mainunit.strnotavalidnotation
msgid "This is not a valid notation"
msgstr "这不是一个有效的注释"

#: mainunit.strnotavalidvalue
msgctxt "mainunit.strnotavalidvalue"
msgid "This is not an valid value"
msgstr "这不是一个有效的数值"

#: mainunit.strnotsameammountofbytes
msgid "The number of bytes you typed is not the same as the previous ammount. Continue?"
msgstr "你输入的数值和原来的不同, 继续吗?"

#: mainunit.strnotthesamesize1
msgid "The text you entered isn't the same size as the original. Continue?"
msgstr "你输入的文本和原来的不同, 继续吗?"

#: mainunit.strnotthesamesize2
msgid "Not the same size!"
msgstr "大小不一致!"

#: mainunit.stropcodechanged
msgctxt "mainunit.stropcodechanged"
msgid "The following opcodes changed the selected address"
msgstr "下列操作码改变了选中的地址"

#: mainunit.stropcoderead
msgid "The following opcodes read from the selected address"
msgstr "选中的地址读取了下列操作码"

#: mainunit.strphysicalmemory
msgctxt "mainunit.strphysicalmemory"
msgid "Physical Memory"
msgstr "物理内存"

#: mainunit.strrecalculateaddress
msgctxt "mainunit.strrecalculateaddress"
msgid "Recalculate address"
msgstr "重新计算地址"

#: mainunit.strrecalculatealladdresses
msgid "Recalculate all addresses"
msgstr "重新计算所有地址"

#: mainunit.strrecalculateselectedaddresses
msgid "Recalculate selected addresses"
msgstr "重新计算选择的地址"

#: mainunit.strremovefromgroup
msgid "Remove from group "
msgstr "从群组移除 "

#: mainunit.strsaferphysicalmemory
msgid "Safer memory access"
msgstr "更安全的内存访问"

#: mainunit.strscantextcaptiontotext
msgid "Text:"
msgstr "文本:"

#: mainunit.strscantextcaptiontovalue
msgid "Value:"
msgstr "数值:"

#: mainunit.strsearchforarray
msgid "Search for this array"
msgstr "搜索数组"

#: mainunit.strsearchfortext
msgid "Search for text"
msgstr "搜索字符串"

#: mainunit.strselectedaddressisapointer
msgid "The selected address is a pointer. Are you sure? (the base pointer will get the address)"
msgstr "已选中的地址是一个指针,你确定吗?(基址指针将获取地址)"

#: mainunit.strsethotkey
msgid "Set a hotkey"
msgstr "设定热键"

#: mainunit.strshowasdecimal
msgid "Show as decimal value"
msgstr "以十进制显示"

#: mainunit.strshowashex
msgid "Show as hexadecimal value"
msgstr "以十六进制显示"

#: mainunit.strunfreezeaddressinlist
msgid "Unfreeze the address in this list"
msgstr "解锁列表中的地址"

#: mainunit.strunfreezealladdresses
msgid "Unfreeze all addresses in this list"
msgstr "解锁列表中所有地址"

#: mainunit.strunhideall
msgid "will bring all windows back"
msgstr "将所有窗口置后"

#: mainunit.strunhideforeground
msgid "will bring the foreground window back"
msgstr "将前台窗口置后"

#: mainunit.strunknownextension
msgctxt "mainunit.strunknownextension"
msgid "Unknown extension"
msgstr "未知的扩展名"

#: mainunit.strvalue
msgctxt "mainunit.strvalue"
msgid "Value"
msgstr "数值"

#: mainunit.strwindowfailedtohide
msgid "A window failed to hide"
msgstr "窗口隐藏失败"

#: mainunit.strxmess
msgid "Merry christmas and happy new year"
msgstr "圣诞快乐, 新年快乐"

#: mainunit2.cename
msgctxt "mainunit2.cename"
msgid "Cheat Engine 7.0"
msgstr "Cheat Engine 7.0"

#: mainunit2.rsenabledisablespeedhack
msgid "Enable/Disable speedhack."
msgstr "启用/禁用变速精灵."

#: mainunit2.rsm2nohotkey
msgid " (No hotkey)"
msgstr " (无热键)"

#: mainunit2.rsnohotkey
msgid "No hotkey"
msgstr "无热键"

#: mainunit2.rspleasewait
msgid "Please Wait!"
msgstr "请稍候!"

#: mainunit2.rsselectanitemfromthelistforasmalldescription
msgid "Select an item from the list for a small description"
msgstr "从列表中选择一个项目,有简单的描述"

#: mainunit2.rsusethegameapplicationforawhile
msgid "Use the game/application for a while and make the address you're watching change. The list will be filled with addresses that contain code that change the watched address."
msgstr "运行一段时间的游戏/应用程序后使正被监视的地址发生变化. 列表中将填充包含了改变被监视地址的代码的地址."

#: mainunit2.straddresshastobereadable
msgid "The address has to be readable if you want to use this function"
msgstr "使用此项功能可将地址设置为可读"

#: mainunit2.strautoassemble
msgid "Assembler"
msgstr "汇编器"

#: mainunit2.strbiggerthan
msgid "Bigger than..."
msgstr "值大于..."

#: mainunit2.strbug
msgid "BUG!"
msgstr "BUG!"

#: mainunit2.strchangedvalue
msgid "Changed value"
msgstr "变动的数值"

#: mainunit2.strcomparetofirstscan
msgid "Compare to first scan"
msgstr "对比首次扫描"

#: mainunit2.strcomparetolastscan
msgid "Compare to last scan"
msgstr "对比上次扫描"

#: mainunit2.strcomparetosavedscan
msgid "Compare to saved scan"
msgstr "对比已保存的扫描"

#: mainunit2.strdecreasedvalue
msgid "Decreased value"
msgstr "减少的数值"

#: mainunit2.strdecreasedvalueby
msgid "Decreased value by ..."
msgstr "数值减少了 ..."

#: mainunit2.strexact
msgid "Exact"
msgstr "精确"

#: mainunit2.strexactvalue
msgid "Exact Value"
msgstr "精确数值"

#: mainunit2.strfailedtoinitialize
msgid "Failed to initialize the debugger"
msgstr "无法初始化调试器"

#: mainunit2.strfirstscan
msgctxt "mainunit2.strfirstscan"
msgid "First Scan"
msgstr "首次扫描"

#: mainunit2.strincreasedvalue
msgid "Increased value"
msgstr "增加的数值"

#: mainunit2.strincreasedvalueby
msgid "Increased value by ..."
msgstr "数值增加了 ..."

#: mainunit2.strneednewerwindowsversion
msgid "This function only works in Windows 2000+ (perhaps also NT but not tested)"
msgstr "该功能用于 Windows 2000 以上系统 (或许NT也行, 但没经测试)"

#: mainunit2.strnewscan
msgctxt "mainunit2.strnewscan"
msgid "New Scan"
msgstr "新的扫描"

#: mainunit2.strnodescription
msgctxt "mainunit2.strnodescription"
msgid "No description"
msgstr "无描述"

#: mainunit2.strok
msgctxt "mainunit2.strok"
msgid "OK"
msgstr "确定"

#: mainunit2.strsmallerthan
msgid "Smaller than..."
msgstr "值小于..."

#: mainunit2.strstart
msgctxt "mainunit2.strstart"
msgid "Start"
msgstr "起始"

#: mainunit2.strstop
msgctxt "mainunit2.strstop"
msgid "Stop"
msgstr "停止"

#: mainunit2.strtoolong
msgid "Too long"
msgstr "过长"

#: mainunit2.strunchangedvalue
msgid "Unchanged value"
msgstr "未变动的数值"

#: mainunit2.strunknowninitialvalue
msgid "Unknown initial value"
msgstr "未知的初始值"

#: mainunit2.strvaluebetween
msgid "Value between..."
msgstr "值介于...两者之间"

#: manualmoduleloader.rsmmlallocationerror
msgid "Allocation error"
msgstr "分配错误"

#: manualmoduleloader.rsmmlfailedfindingaddressof
msgid "failed finding address of "
msgstr "没有查找到地址 "

#: manualmoduleloader.rsmmlnotavalidfile
msgid "not a valid file"
msgstr "不是一个有效的文件"

#: manualmoduleloader.rsmmlnotavalidheader
msgid "Not a valid header"
msgstr "不是一个有效的文件头"

#: manualmoduleloader.rsmmltriedloadinga64bitmoduleona32bitsystem
msgid "Tried loading a 64-bit module on a 32-bit system"
msgstr "尝试在32位系统中加载64位模块"

#: memdisplay.rsfailurecreatingopenglwindow
msgid "failure creating opengl window"
msgstr "创建opengl窗口失败"

#: memdisplay.rsondatareturnedatoosmallmemoryregion
msgid "OnData returned a too small memory region. It should have returned false instead"
msgstr "OnData 返回一个很小的内存区域. 但是它应该返回 false"

#: memorybrowserformunit.rs0or1
msgid "(0 or 1)"
msgstr "(0 或 1)"

#: memorybrowserformunit.rsaddress
msgctxt "memorybrowserformunit.rsaddress"
msgid "Address"
msgstr "地址"

#: memorybrowserformunit.rsallocatememory
msgid "Allocate memory"
msgstr "分配内存"

#: memorybrowserformunit.rsassemblyscan
msgctxt "memorybrowserformunit.rsassemblyscan"
msgid "Assembly scan"
msgstr "汇编扫描"

#: memorybrowserformunit.rsatleastbyteshavebeenallocatedatdoyouwanttogotheren
msgid "At least %s bytes have been allocated at %s%sDo you want to go there now?"
msgstr "至少 %s 字节已分配 %s%s 你想跳转过去吗?"

#: memorybrowserformunit.rsatleastbyteshavebeenallocatedatgotherenow
msgid "At least %s bytes have been allocated at %s. Go there now?"
msgstr "至少 %s 字节已分配 %s. 跳转过去吗?"

#: memorybrowserformunit.rsbetween
msgid "Between %s"
msgstr "介于 %s 之间"

#: memorybrowserformunit.rschangedisplayfader
msgid "Change display fader"
msgstr "请输入刷新时间(毫秒)"

#: memorybrowserformunit.rschangeregister
msgid "Change register"
msgstr "更改寄存器"

#: memorybrowserformunit.rscheatenginesinglelingeassembler
msgid "Cheat Engine single-line assembler"
msgstr "Cheat Engine 逐行汇编"

#: memorybrowserformunit.rscomment
msgctxt "memorybrowserformunit.rscomment"
msgid "Comment"
msgstr "注释"

#: memorybrowserformunit.rscommentfor
msgctxt "memorybrowserformunit.rscommentfor"
msgid "Comment for %s"
msgstr "注释: %s"

#: memorybrowserformunit.rscreateremotethread
msgid "Create remote thread"
msgstr "创建远程线程"

#: memorybrowserformunit.rsdllinjected
msgid "DLL Injected"
msgstr "DLL 注入"

#: memorybrowserformunit.rsdoyouwanttoexecuteafunctionofthedll
msgid "Do you want to execute a function of the dll?"
msgstr "你想执行 dll 中的函数吗?"

#: memorybrowserformunit.rserrorallocatingmemory
msgid "Error allocating memory!"
msgstr "分配内存时出错!"

#: memorybrowserformunit.rsfillintheaddressyouwanttogoto
msgctxt "memorybrowserformunit.rsfillintheaddressyouwanttogoto"
msgid "Fill in the address you want to go to"
msgstr "输入你想跳转的地址"

#: memorybrowserformunit.rsgetkerneladdress
msgid "Get kernel address"
msgstr "获取内核地址"

#: memorybrowserformunit.rsgivethenameofthefunctionyouwanttofindcasesensitive
msgid "Give the name of the function you want to find (Case sensitive,certain words can cause blue screens)"
msgstr "给出要查找的函数名称 (区分大小写,某些词可能会导致蓝屏)"

#: memorybrowserformunit.rsgotoaddress
msgctxt "memorybrowserformunit.rsgotoaddress"
msgid "Goto Address"
msgstr "转到地址"

#: memorybrowserformunit.rsheaderfor
msgctxt "memorybrowserformunit.rsheaderfor"
msgid "Header for %s"
msgstr "标题为 %s"

#: memorybrowserformunit.rshowlongshouldachangebeshown
msgid "How long should a change be shown?"
msgstr "多久时间刷新一次?"

#: memorybrowserformunit.rshowmuchis
msgid "How much is %s?"
msgstr "是多少 %s?"

#: memorybrowserformunit.rshowmuchmemorydoyouwanttoaddtothisprocess
msgid "How much memory do you want to add to this process?"
msgstr "你需要分配给此进程多少内存？"

#: memorybrowserformunit.rshowmuchmemorydoyouwishtoallocate
msgid "How much memory do you wish to allocate?"
msgstr "你需要分配多少内存?"

#: memorybrowserformunit.rsidontunderstandwhatyoumeanwith
msgid "I don't understand what you mean with %s"
msgstr "我不明白你是什么意思 %s"

#: memorybrowserformunit.rsinjectdll
msgid "Inject dll"
msgstr "注入 dll"

#: memorybrowserformunit.rsinputtheassemblycodetofindwilcardssupported
msgctxt "memorybrowserformunit.rsinputtheassemblycodetofindwilcardssupported"
msgid "Input the assembly code to find. Wildcards( * ) supported."
msgstr "输入要查找的汇编码. 支持通配符( * )."

#: memorybrowserformunit.rsisnotavalidvalue
msgctxt "memorybrowserformunit.rsisnotavalidvalue"
msgid "%s is not a valid value"
msgstr "%s 不是一个有效的数值"

#: memorybrowserformunit.rslinkwithotherhexview
msgctxt "memorybrowserformunit.rslinkwithotherhexview"
msgid "Link with other hexview"
msgstr "与其它的十六进制视图链接"

#: memorybrowserformunit.rsmaxstacktracesize
msgid "Max stacktrace size"
msgstr "最大栈跟踪大小"

#: memorybrowserformunit.rsmbbookmark
msgid "Bookmark %d"
msgstr "书签 %d"

#: memorybrowserformunit.rsmbbookmark2
msgid "Bookmark %d: %s"
msgstr "书签 %d: %s"

#: memorybrowserformunit.rsmbcreationoftheremotethreadfailed
msgid "Creation of the remote thread failed"
msgstr "创建远程线程失败"

#: memorybrowserformunit.rsmbthreadcreated
msgid "Thread Created"
msgstr "创建的线程"

#: memorybrowserformunit.rsmemorybrowser
msgid "MemoryBrowser"
msgstr "MemoryBrowser"

#: memorybrowserformunit.rsmemoryviewercurrentlydebuggingthread
msgid "Memory Viewer - Currently debugging thread %s"
msgstr "内存查看器 - 当前正在调试的线程 %s"

#: memorybrowserformunit.rsmemoryviewerrunning
msgid "Memory Viewer - Running"
msgstr "内存查看器 - 已运行"

#: memorybrowserformunit.rsneedtorundissectcode
msgid "You will need to run the dissect code routine first before this window is usable. Run it now?"
msgstr "使用此功能之前应先运行分析代码. 现在运行吗?"

#: memorybrowserformunit.rsnewsize
msgid "New size"
msgstr "新的大小"

#: memorybrowserformunit.rsparameters
msgctxt "memorybrowserformunit.rsparameters"
msgid "Parameters"
msgstr "参数"

#: memorybrowserformunit.rspleaseenteravalidhexadecimaladdres
msgid "Please enter a valid hexadecimal addres"
msgstr "请输入有效的十六进制地址"

#: memorybrowserformunit.rspleaseenteravalidhexadecimalvalue
msgid "Please enter a valid hexadecimal value"
msgstr "请输入有效的十六进制值"

#: memorybrowserformunit.rspleasespecifythenewsymbolsearchpathseperatespaths
msgid "Please specify the new symbol searchpath (; separates paths)"
msgstr "请指定新的符号搜索路径 (使用 ; 分隔路径)"

#: memorybrowserformunit.rspleasetargetanotherprocess
msgid "Start another version of Cheat Engine and attach to that instead"
msgstr "启动另一个版本的CE,并用其替代附加"

#: memorybrowserformunit.rspleasetargetaprocessfirst
msgid "Please target a process first"
msgstr "请打开一个进程"

#: memorybrowserformunit.rsreplacewithcodethatdoesnothing
msgctxt "memorybrowserformunit.rsreplacewithcodethatdoesnothing"
msgid "Replace with code that does nothing"
msgstr "使用空指令替换"

#: memorybrowserformunit.rsrestorewithorginalcode
msgctxt "memorybrowserformunit.rsrestorewithorginalcode"
msgid "Restore with original code"
msgstr "用原代码还原"

#: memorybrowserformunit.rsreturnaddress
msgid "Return Address"
msgstr "返回地址"

#: memorybrowserformunit.rsselectthefunctionyouwanttoexecute
msgid "Select the function you want to execute"
msgstr "选择你要执行的函数"

#: memorybrowserformunit.rsshowdifference
msgctxt "memorybrowserformunit.rsshowdifference"
msgid "Show difference"
msgstr "显示差异"

#: memorybrowserformunit.rssshowstheautoguessvalue
msgid "(%s shows the autoguess value)"
msgstr "(%s 显示预测值)"

#: memorybrowserformunit.rsstacktrace
msgctxt "memorybrowserformunit.rsstacktrace"
msgid "Stacktrace"
msgstr "堆栈跟踪"

#: memorybrowserformunit.rsstopshowingthedifference
msgid "Stop showing the difference"
msgstr "停止显示差异"

#: memorybrowserformunit.rssymbolhandler
msgid "Symbol handler"
msgstr "符号处理器"

#: memorybrowserformunit.rsthegeneratedcodeisbyteslongbuttheselectedopcodeisb
msgid "The generated code is %s byte(s) long, but the selected opcode is %s byte(s) long! Do you want to replace the incomplete opcode(s) with NOP's?"
msgstr "生成的代码为 %s 字节长度, 但所选操作码为 %s 字节长度! 要使用空指令(NOP)填充多余的操作码吗?"

#: memorybrowserformunit.rstypeyourassemblercodehereaddress
msgid "Type your assembler code here: (address=%s)"
msgstr "输入你的汇编码:(地址=%s)"

#: memorybrowserformunit.rsunlinkfromotherhexview
msgid "Unlink from other hexview"
msgstr "取消链接其他十六进制视图"

#: memorybrowserformunit.rsvalue
msgctxt "memorybrowserformunit.rsvalue"
msgid "Value"
msgstr "数值"

#: memorybrowserformunit.rswhatisthenewvalueof
msgid "What is the new value of %s?"
msgstr "新数值是什么 %s?"

#: memorybrowserformunit.rswhatwillbethestartaddressofthisthread
msgid "What will be the startaddress of this thread?"
msgstr "当前线程起始地址是什么?"

#: memorybrowserformunit.rsyouwanttogiveanadditional32bitparameterwillshowupi
msgid "You want to give an additional 32-bit parameter? (Will show up in (R)/(E)BX)"
msgstr "你想给出额外的32位的参数吗? (将显示在 (R)/(E)BX)"

#: memoryrecordunit.rsactivate
msgctxt "memoryrecordunit.rsactivate"
msgid "Activate"
msgstr "激活"

#: memoryrecordunit.rsdeactivate
msgctxt "memoryrecordunit.rsdeactivate"
msgid "Deactivate"
msgstr "禁用"

#: memoryrecordunit.rsdecreasevalue
msgid "Decrease Value"
msgstr "减少的数值"

#: memoryrecordunit.rserror
msgctxt "memoryrecordunit.rserror"
msgid "error"
msgstr "错误"

#: memoryrecordunit.rsincreasevalue
msgid "Increase Value"
msgstr "增加的数值"

#: memoryrecordunit.rsmrnibblesupportisonlyforhexadecimaldisplay
msgid "Nibble support is only for hexadecimal display"
msgstr "半字节仅支持以十六进制显示"

#: memoryrecordunit.rsp
msgctxt "memoryrecordunit.rsp"
msgid "P->"
msgstr "P->"

#: memoryrecordunit.rspqqqqqqqq
msgid "P->????????"
msgstr "P->????????"

#: memoryrecordunit.rssetvalue
msgid "Set Value"
msgstr "设置数值"

#: memoryrecordunit.rstoggleactivation
msgid "Toggle Activation"
msgstr "切换到激活"

#: memoryrecordunit.rstoggleactivationallowdecrease
msgid "Toggle Activation Allow Decrease"
msgstr "切换到激活但允许减少"

#: memoryrecordunit.rstoggleactivationallowincrease
msgid "Toggle Activation Allow Increase"
msgstr "切换到激活但允许增加"

#: memscan.rsaresultsetwiththisnamealreadyexists
msgid "A result set with this name(%s) already exists"
msgstr "这个名字(%s)在结果中已经存在"

#: memscan.rsdiskwriteerror
msgid "Disk write error:%s"
msgstr "磁盘写入错误:%s"

#: memscan.rsdiskwriteerror2
msgid "Disk Write Error:%s"
msgstr "磁盘写入错误:%s"

#: memscan.rserrorallocatingbytesfortheoldresults
msgid "Error allocating %s bytes for the old results. buffersize=%s variablesize=%s"
msgstr "结果中错误分配了 %s 字节. 缓存大小=%s 变量大小=%s"

#: memscan.rserrorwhenwhileloadingresult
msgid "Error while loading result"
msgstr "载入结果时失败"

#: memscan.rsfailedspawningthesavefirstscanthread
msgid "Failed spawning the Save First Scan thread"
msgstr "首次扫描结果保存失败"

#: memscan.rsfailureallocatingmemoryforcopytriedallocatingkb
msgid "Failure allocating memory for copy. Tried allocating %s KB"
msgstr "(可用物理内存不足)分配并复制内存失败. 尝试分配 %s KB"

#: memscan.rsfailurecreatingthescandirectory
msgid "Failure creating the scan directory"
msgstr "为扫描结果创建目录失败"

#: memscan.rsforsomereasonthescancontrollerdoesnotexist
msgid "For some reason the scanController does not exist"
msgstr "由于某种原因scanController并不存在"

#: memscan.rsinvalidbinarynotation
msgid "Invalid binary notation"
msgstr "无效的二进制记数法"

#: memscan.rsisaninvalidvalue
msgid "%s is an invalid value"
msgstr "%s 数值无效"

#: memscan.rsisnotavalidcharacterinsideabinarystring
msgid "%s is not a valid character inside a binary string"
msgstr "%s 字符不是二进制字串"

#: memscan.rsisnotavalidnotation
msgid "%s is an invalid notation"
msgstr "%s 是无效的记数法"

#: memscan.rsisnotavalidvalue
msgctxt "memscan.rsisnotavalidvalue"
msgid "%s is not a valid value"
msgstr "%s 是无效的数值"

#: memscan.rsmscustomtypeisnil
msgid "Custom type is nil"
msgstr "自定义类型是 nil"

#: memscan.rsmsnothingtoscanfor
msgid "Nothing to scan for"
msgstr "没有扫描"

#: memscan.rsmsthescanwasforcedtoterminatesubsequentscansmaynotfunctionproperlyetc
msgid "The scan was forced to terminate. Subsequent scans may not function properly. It's recommended to restart Cheat Engine"
msgstr "扫描被迫终止. 继续扫描有可能不会正常工作. 建议重新启动 Cheat Engine"

#: memscan.rsmstupidalignsize
msgid "Stupid alignsize"
msgstr "愚蠢的对齐大小"

#: memscan.rsnoreadablememoryfound
msgctxt "memscan.rsnoreadablememoryfound"
msgid "No readable memory found"
msgstr "可读内存未找到"

#: memscan.rsnotenoughdiskspacefortheaddressfile
msgid "Not enough diskspace for the address file"
msgstr "无法保存地址文件 (没有足够的磁盘空间)"

#: memscan.rsnotenoughdiskspaceforthememoryfile
msgid "Not enough diskspace for the memory file"
msgstr "无法保存内存文件 (没有足够的磁盘空间)"

#: memscan.rspleasefillsomethingin
msgid "Please fill something in"
msgstr "不能为空"

#: memscan.rsthetemporaryscandirectorydoesnotexistcheckyourscan
msgid "The temporary scan directory %s does not exist. Check your scan settings"
msgstr "临时存放扫描结果目录 %s 不存在. 请检查你的扫描目录设置"

#: memscan.rsthread
msgid "thread "
msgstr "线程 "

#: memscan.rsthreadsynchronizer
msgid "Thread synchronizer"
msgstr "线程同步"

#: memscan.rstmpandundoarenamesthatmaynotbeusedtryanothername
msgid "TMP and UNDO are names that may not be used. Try another name"
msgstr "不能使用 TMP 和 UNDO 的名称. 请使用其它名称"

#: memscan.rsunknown
msgctxt "memscan.rsunknown"
msgid "Unknown"
msgstr "未知"

#: mikmod.rsfailuretoinitializemikmod
msgid "Failure to initialize MikMod"
msgstr "初始化 MikMod 失败"

#: networkconfig.rscouldnotberesolved
msgctxt "networkconfig.rscouldnotberesolved"
msgid " could not be resolved"
msgstr " 无法解析"

#: networkconfig.rsfailedconnectingtotheserver
msgid "Failed connecting to the server"
msgstr "无法连接到服务器"

#: networkconfig.rsfailurecreatingsocket
msgctxt "networkconfig.rsfailurecreatingsocket"
msgid "Failure creating socket"
msgstr "创建socket失败"

#: networkconfig.rshost
msgctxt "networkconfig.rshost"
msgid "host:"
msgstr "主机:"

#: networkinterfaceapi.rsnoconnection
msgid "No connection"
msgstr "无连接"

#: newkernelhandler.rscouldntbeopened
msgid "%s couldn't be opened"
msgstr "%s 无法打开"

#: newkernelhandler.rsdbvmisnotloadedthisfeatureisnotusable
msgid "DBVM is not loaded. This feature is not usable"
msgstr "DBVM 未加载. 所以此功能不可用"

#: newkernelhandler.rsdidnotloaddbvm
msgid "I don't know what you did, you didn't crash, but you also didn't load DBVM"
msgstr "我不知道你做了什么, 你的操作系统没有崩溃, 但你也没有加载 DBVM"

#: newkernelhandler.rsfucked
msgid "Something is really messed up on your computer! You don't seem to have a kernel!!!!"
msgstr "你的计算机中的东西真的是乱七八糟! 而且你没有一个kernel!!!!"

#: newkernelhandler.rspleaserebootandpressf8beforewindowsboots
msgid "Please reboot and press f8 before windows boots. Then enable unsigned drivers. Alternatively, you could buy yourself a business class certificate and sign the driver yourself (or try debug signing)"
msgstr "请重新启动在 Windows 启动前按 F8键. 然后启用未签名驱动程序. 或者你可以给自己买一个证书和自己为驱动程序签名 (或者尝试调试签名)"

#: newkernelhandler.rsthedriverneedstobeloadedtobeabletousethisfunction
msgid "The driver needs to be loaded to be able to use this function."
msgstr "需要加载驱动程序才能使用这个功能."

#: newkernelhandler.rstousethisfunctionyouwillneedtorundbvm
msgid "To use this function you will need to run DBVM. There is a high chance running DBVM can crash your system and make you lose your data(So don't forget to save first). Do you want to run DBVM?"
msgstr "要使用这个功能, 你就需要运行 DBVM. 运行 DBVM 时有一定机率会使你的系统崩溃, 造成你丢失没有保存的数据(所以不要忘记保存). 你确定要运行 DBVM 吗?"

#: newkernelhandler.rsyourcpumustbeabletorundbvmtousethisfunction
msgid "Your cpu must be able to run dbvm to use this function"
msgstr "你的 CPU 必须能够运行 DBVM 才能使用这个功能"

#: opensave.rsaskifstupid
msgid "Generating a trainer with the current state of the cheat table will likely result in a completely useless trainer that does nothing. Are you sure?"
msgstr "当前的作弊码表状态可能会生成完全没有作用的修改器. 你确定吗?"

#: opensave.rsdoesntcontainneededinformationwheretoplacethememor
msgid "%s doesn't contain needed information where to place the memory"
msgstr "%s 内存中并不包含所需要的信息"

#: opensave.rserrorexecutingthistablesluascript
msgid "Error executing this table's lua script: %s"
msgstr "执行表单中的 Lua 脚本出错: %s"

#: opensave.rserrorsaving
msgid "Error saving..."
msgstr "保存时出错..."

#: opensave.rsosthereisanewerversionifcheatengineoutetc
msgid "There is a newer version of Cheat Engine out. It's recommended to use that version instead"
msgstr "由新版本的 Cheat Engine 生成. 推荐更新到最新版本"

#: opensave.rsosthischeattableiscorrupt
msgid "This cheat table is corrupt"
msgstr "这个CT表已损坏"

#: opensave.rstheregionatwaspartiallyorcompletlyunreadable
msgid "The region at %s was partially or completely unreadable"
msgstr "%s 区域部分或完全无法读取"

#: opensave.rstheversionofisincompatiblewiththisceversion
msgid "The version of %s is incompatible with this CE version"
msgstr "%s 版本与当前 CE 版本不兼容"

#: opensave.rsthisisnotavalidcheattable
msgid "This is not a valid cheat table"
msgstr "CT 表无法识别"

#: opensave.rsthisisnotavalidxmlfile
msgid "This is not a valid xml file"
msgstr "XML 文件无法识别"

#: opensave.rsthistablecontainsaluascriptdoyouwanttorunit
msgid "This table contains a lua script. Do you want to run it?"
msgstr "表单中包含 Lua 脚本. 你想要运行它吗?"

#: opensave.rsunknownextention
msgctxt "opensave.rsunknownextention"
msgid "Unknown extension"
msgstr "未知的扩展名"

#: opensave.rsyoucanonlyprotectafileifithasancetrainerextension
msgid "You can only protect a file if it has an .CETRAINER extension"
msgstr "你只能保护以 .CETRAINER 为扩展名的文件"

#: opensave.strcantloadfilepatcher
msgid "The file patcher can't be loaded by Cheat Engine!"
msgstr "Cheat Engine 无法载入补丁文件!"

#: opensave.strcantloadprotectedfile
msgid "This trainer is protected from being opened by CE. Now go away!!!"
msgstr "正在载入的修改器已经 CE 保护, 没必要费劲了!!!"

#: opensave.strcorrupticon
msgid "The icon has been corrupted"
msgstr "已损坏的图标"

#: opensave.strnotacetrainer
msgid "This is not a trainer made by Cheat Engine (If it is a trainer at all!)"
msgstr "这不是 Cheat Engine 制作的修改器 (如果你能确认它是修改器的话)"

#: opensave.strunknowncomponent
msgid "There is a unknown component in the trainer! compnr="
msgstr "修改器存在未知成分! compnr="

#: opensave.strunknowntrainerversion
msgid "This version of Cheat Engine doesn't know how to read this trainer! Trainerversion="
msgstr "此版本的 Cheat Engine 无法载入这个修改器. 修改器版本="

#: parsers.rsinvalidinteger
msgid "Invalid integer"
msgstr "无效的整数"

#: peinfofunctions.rspeifnoexports
msgid "No exports"
msgstr "未导出"

#: peinfofunctions.strinvalidfile
msgid "Invalid file"
msgstr "无效的文件"

#: peinfounit.rspe32bit
msgid "32-bit, "
msgstr "32位, "

#: peinfounit.rspeaddressoffunctions
msgid "AddressOfFunctions = %x"
msgstr "AddressOfFunctions = %x"

#: peinfounit.rspeaddressofnameordinals
msgid "AddressOfNameOrdinals = %x"
msgstr "AddressOfNameOrdinals = %x"

#: peinfounit.rspeaddressofnames
msgid "AddressOfNames = %x"
msgstr "AddressOfNames = %x"

#: peinfounit.rspeaddressofrawdata
msgid "Address of raw data = %x"
msgstr "原始数据的地址 = %x"

#: peinfounit.rspeagressivetrim
msgid "Agressive trim, "
msgstr "Agressive trim, "

#: peinfounit.rspearchitecturespecifictable
msgid "Architecture-Specific table"
msgstr "具体架构表"

#: peinfounit.rspebase
msgid "Base = %x"
msgstr "基址 = %x"

#: peinfounit.rspebaseofcode
msgid "Base of code = %.8x "
msgstr "基代码 = %.8x "

#: peinfounit.rspebaseofdata
msgid "Base of data = %.8x "
msgstr "基数据 = %.8x "

#: peinfounit.rspebaserelocationtable
msgid "Base-Relocation table"
msgstr "基址重定向表"

#: peinfounit.rspeboundimporttable
msgid "Bound import table"
msgstr "Bound 导入表"

#: peinfounit.rspecertificatetabel
msgid "Certificate table"
msgstr "Certificate 表"

#: peinfounit.rspecharacterisitics
msgid "characterisitics=%x (%s)"
msgstr "特征=%x (%s)"

#: peinfounit.rspecharacteristics
msgid "Characteristics = %x (%s)"
msgstr "特征 = %x (%s)"

#: peinfounit.rspecharacteristics2
msgid "Characteristics=%x (should be 0)"
msgstr "特征=%x (应该是 0)"

#: peinfounit.rspecharacteristicsoriginalfirstthunk
msgid "Characteristics/OriginalFirstThunk=%x"
msgstr "特征/OriginalFirstThunk=%x"

#: peinfounit.rspechecksum
msgid "CheckSum = %x "
msgstr "校验和 = %x "

#: peinfounit.rspecodeview
msgid "Codeview"
msgstr "代码视图"

#: peinfounit.rspedebugfile
msgid "Debugfile =%s"
msgstr "调试文件 =%s"

#: peinfounit.rspedebugginginfotable
msgid "Debugging info table"
msgstr "调试信息表"

#: peinfounit.rspedelayimportdescriptiontable
msgid "Delay import descriptor table"
msgstr "延迟导入描述符表"

#: peinfounit.rspediscardable
msgid "discardable, "
msgstr "被丢弃的, "

#: peinfounit.rspedll
msgid "DLL, "
msgstr "DLL, "

#: peinfounit.rspedllcharacteristics
msgid "Dll Characteristics = %x "
msgstr "Dll 特征 = %x "

#: peinfounit.rspedosentrypoint
msgid "dos entrypoint = %.4x:%.4x"
msgstr "dos 入口点 = %.4x:%.4x"

#: peinfounit.rspedosstack
msgid "dos stack = %.4x:%.4x"
msgstr "dos 堆栈 = %.4x:%.4x"

#: peinfounit.rspeentrypoint
msgid "Entry point = %.8x "
msgstr "入口点 = %.8x "

#: peinfounit.rspeexceptiontable
msgid "Exception table"
msgstr "异常表"

#: peinfounit.rspeexecutable
msgid "Executable, "
msgstr "可执行, "

#: peinfounit.rspeexecutablecode
msgid "Executable code, "
msgstr "可执行代码, "

#: peinfounit.rspeexecutablememory
msgid "executable memory, "
msgstr "可执行内存, "

#: peinfounit.rspeexporttable
msgid "Export table"
msgstr "导出表"

#: peinfounit.rspefailureatallocationmemory
msgid "Failure at allocating memory"
msgstr "分配内存失败"

#: peinfounit.rspefailurereadingmemory
msgid "Failure reading memory"
msgstr "读取内存失败"

#: peinfounit.rspefilealignment
msgid "File Alignment = %x "
msgstr "文件对齐 = %x "

#: peinfounit.rspefirstthunk
msgid "FirstThunk=%x"
msgstr "FirstThunk=%x"

#: peinfounit.rspeforwarderchain
msgid "Forwarder Chain=%x (-1 if no forwarders)"
msgstr "传递器链表=%x (如果无传递器则为 -1)"

#: peinfounit.rspeglobalpointertable
msgid "Global pointer table"
msgstr "全局指针表"

#: peinfounit.rspeimport
msgid "Import %d : %s"
msgstr "导入 %d : %s"

#: peinfounit.rspeimportaddresstable
msgid "import address table"
msgstr "导入地址表"

#: peinfounit.rspeimports
msgid "imports:"
msgstr "导入:"

#: peinfounit.rspeimporttable
msgid "Import table"
msgstr "导入表"

#: peinfounit.rspeinconsistent
msgid " (inconsistent)"
msgstr " (不一致)"

#: peinfounit.rspeinitializeddata
msgid "Initialized data, "
msgstr "初始化数据, "

#: peinfounit.rspelfanew
msgid "lfanew="
msgstr "lfanew="

#: peinfounit.rspeloadconfigtable
msgid "Load config table"
msgstr "载入配置表"

#: peinfounit.rspeloaderflags
msgid "Loader Flags = %x "
msgstr "载入标志位 = %x "

#: peinfounit.rspemachine
msgid "Machine=%.2x"
msgstr "匹配=%.2x"

#: peinfounit.rspemachine64
msgid "Machine=%.2x (64 bit)"
msgstr "匹配=%.2x (64 位)"

#: peinfounit.rspemajorimageversion
msgid "Major Image Version = %d "
msgstr "主映像版本 = %d "

#: peinfounit.rspemajorlinkerversion
msgid "Major linker version = %d "
msgstr "主链接器版本 = %d "

#: peinfounit.rspemajoroperatingsystemversion
msgid "Major Operating System Version = %d "
msgstr "主操作系统版本 = %d "

#: peinfounit.rspemajorsubsystemversion
msgid "Major Subsystem Version = %d "
msgstr "主子系统的版本 = %d "

#: peinfounit.rspemajorversion
msgid "Major version=%d"
msgstr "主版本=%d"

#: peinfounit.rspeminorimageversion
msgid "Minor Image Version = %d "
msgstr "次要映像版本 = %d "

#: peinfounit.rspeminorlinlerversion
msgid "Minor linker version = %d "
msgstr "次要链接器版本 = %d "

#: peinfounit.rspeminorsubsystemversion
msgid "Minor Subsystem Version = %d "
msgstr "次要子系统版本 = %d "

#: peinfounit.rspeminorversion
msgid "Minor version=%d"
msgstr "次要版本=%d"

#: peinfounit.rspemzheader
msgid "MZ header"
msgstr "MZ header"

#: peinfounit.rspename
msgid "Name = %x (%s)"
msgstr "名称 = %x (%s)"

#: peinfounit.rspename2
msgid "Name=%x : %s"
msgstr "名称=%x : %s"

#: peinfounit.rspenetrunfromswap
msgid "Net: Run from swap, "
msgstr "Net: Run from swap, "

#: peinfounit.rspenodbginfo
msgid "No DBG info, "
msgstr "无 DBG 信息, "

#: peinfounit.rspenolinenumbers
msgid "No line numbers, "
msgstr "无行号, "

#: peinfounit.rspenolocalsymbols
msgid "No local symbols, "
msgstr "无本地符号表, "

#: peinfounit.rspenorelocations
msgid "No relocations, "
msgstr "不重定位, "

#: peinfounit.rspenotallmemorycouldbereadworkingwithapartialcopyhere
msgid "Not all memory could be read, working with a partial copy here"
msgstr "不是所有的内存都可以读取, 处理了的部分复制在这里"

#: peinfounit.rspenotcached
msgid "not cached, "
msgstr "不缓存, "

#: peinfounit.rspenotpaged
msgid "not paged, "
msgstr "不分页, "

#: peinfounit.rspenumberoffunctions
msgid "NumberOfFunctions = %d"
msgstr "NumberOfFunctions = %d"

#: peinfounit.rspenumberoflinenumbers
msgid "number of line numbers=%x"
msgstr "行数=%x"

#: peinfounit.rspenumberofnames
msgid "NumberOfNames = %d"
msgstr "名称的数量 = %d"

#: peinfounit.rspenumberofrelocations
msgid "number of relocations=%x"
msgstr "重定位数量=%x"

#: peinfounit.rspenumberofrvaandsize
msgid "Number Of Rva And Sizes = %d "
msgstr "Rva 的数量和大小 = %d "

#: peinfounit.rspenumberofsections
msgid "Number of sections=%d"
msgstr "段的数量=%d"

#: peinfounit.rspeoptional
msgid "-----optional-----"
msgstr "-----可选项-----"

#: peinfounit.rspeoptionalheader
msgid "OptionalHeader size = %x"
msgstr "OptionalHeader 大小 = %x"

#: peinfounit.rspeoptionalmagicnumber
msgid "Optional magic number = %x "
msgstr "可选的魔法数 = %x "

#: peinfounit.rspepeheader
msgid "PE header"
msgstr "PE 头部"

#: peinfounit.rspepeinfoimagesize
msgid "PEInfo: Image size"
msgstr "PE信息: 映像大小"

#: peinfounit.rspepointertolinenumbers
msgid "Pointer to line numbers=%x"
msgstr "指针行数=%x"

#: peinfounit.rspepointertorawdata
msgid "Pointer to raw data=%x"
msgstr "指针指向原始数据=%x"

#: peinfounit.rspepointertorelocations
msgid "Pointer to relocations=%x"
msgstr "指针重定位=%x"

#: tformsettings.cbskip_page_nocache.caption
msgid "Don't scan memory that is protected with the No Cache option"
msgstr "不要扫描没有缓存选项保护的内存"

#: peinfounit.rspepreferedimagebase
msgid "Prefered imagebase = %.8x "
msgstr "首选基地址 = %.8x "

#: peinfounit.rspepreferedimagebase2
msgid "Prefered imagebase = %.16x "
msgstr "首选基地址 = %.16x "

#: peinfounit.rspereadablememory
msgid "readable memory, "
msgstr "可读的内存, "

#: peinfounit.rsperemovablerunfromswap
msgid "Removable: Run from swap, "
msgstr "Removable: Run from swap, "

#: peinfounit.rsperemoved
msgid "removed, "
msgstr "已移除, "

#: peinfounit.rspereserved
msgid "reserved"
msgstr "保留"

#: peinfounit.rsperesourcetable
msgid "Resource table"
msgstr "资源表"

#: peinfounit.rspereversedbyteshi
msgid "Reversed bytes HI, "
msgstr "字节颠倒 HI, "

#: peinfounit.rspereversedbyteslo
msgid "Reversed bytes LO, "
msgstr "字节颠倒 LO, "

#: peinfounit.rspesectionallignment
msgid "Section allignment = %x "
msgstr "段对齐 = %x "

#: peinfounit.rspesections
msgid "-----sections-----"
msgstr "-----段-----"

#: peinfounit.rspesharedmemory
msgid "shared memory, "
msgstr "共享内存, "

#: peinfounit.rspesignature
msgid "Signature=%x (%s)"
msgstr "有符号=%x (%s)"

#: peinfounit.rspesizeofcode
msgid "Size of code = %x (%d) "
msgstr "代码的大小 = %x (%d) "

#: peinfounit.rspesizeofheader
msgid "Size Of Headers = %x "
msgstr "Headers的大小 = %x "

#: peinfounit.rspesizeofheapcommit
msgid "Size Of Heap Commit = %x "
msgstr "堆提交大小 = %x "

#: peinfounit.rspesizeofheapreserve
msgid "Size Of Heap Reserve = %x "
msgstr "堆存储大小 = %x "

#: peinfounit.rspesizeofimage
msgid "Size Of Image = %x "
msgstr "映像大小 = %x "

#: peinfounit.rspesizeofinitializeddata
msgid "Size of initialized data = %x (%d)"
msgstr "初始化数据的大小 = %x (%d)"

#: peinfounit.rspesizeofrawdata
msgid "size of raw data=%x"
msgstr "RAW数据大小=%x"

#: peinfounit.rspesizeofstackcommit
msgid "Size Of Stack Commit = %x "
msgstr "堆栈提交大小 = %x "

#: peinfounit.rspesizeofstackreserve
msgid "Size Of Stack Reserve = %x "
msgstr "堆栈存储大小 = %x "

#: peinfounit.rspesizeofuninitializeddata
msgid "Size of uninitialized data = %x (%d) "
msgstr "未初始化数据的大小 = %x (%d) "

#: peinfounit.rspestaticaddresses
msgid "Static addresses"
msgstr "静态地址"

#: peinfounit.rspesubsystem
msgid "Subsystem = %x "
msgstr "子系统 = %x "

#: peinfounit.rspesymbolcount
msgid "Symbolcount = %x"
msgstr "符号数 = %x"

#: peinfounit.rspesymboltableat
msgid "SymbolTable at %x"
msgstr "符号表 at %x"

#: peinfounit.rspesystemfile
msgid "System file, "
msgstr "系统文件, "

#: peinfounit.rspetheheaderofthemodelecouldnobberead
msgid "The header of the module could not be read"
msgstr "无法读取模块的标题"

#: peinfounit.rspetheimagesizeismorethan256
msgid "The imagesize is more than 256 MB, is this the correct ammount? If not, edit here"
msgstr "映像大小超过 256 MB, 总数是否正确? 如果不正确, 请编辑它"

#: peinfounit.rspethisisnotavakidpefile
msgid "This is not a valid PE file"
msgstr "这不是一个有效的PE文件"

#: peinfounit.rspetimedatastamp
msgid "Time datastamp=%x"
msgstr "日期时间戳=%x"

#: peinfounit.rspetimedate
msgid "Time/Date =%d"
msgstr "时间/数据 =%d"

#: peinfounit.rspetimedatestamp
msgid "TimeDateStamp=%x (0=not bound -1=bound, and timestamp)"
msgstr "日期时间戳=%x (0=不绑定 -1=绑定时间戳"

#: peinfounit.rspetlstable
msgid "TLS table"
msgstr "TLS 表"

#: peinfounit.rspetype
msgid "Type = %d"
msgstr "类型 = %d"

#: peinfounit.rspeuninitializeddata
msgid "Uninitialized data, "
msgstr "未初始化数据, "

#: peinfounit.rspeunknown
msgctxt "peinfounit.rspeunknown"
msgid "Unknown"
msgstr "未知"

#: peinfounit.rspeupsystemonly
msgid "UP system only, "
msgstr "仅限更高版本系统, "

#: peinfounit.rspevirtualaddress
msgid "Virtual Address=%x"
msgstr "虚拟地址=%x"

#: peinfounit.rspevirtualaddressbase
msgid "Virtual address base: %.8x (size=%x (%d))"
msgstr "虚拟基地址: %.8x (大小=%x (%d))"

#: peinfounit.rspevirtualsize
msgid "Virtual Size=%x"
msgstr "虚拟大小=%x"

#: peinfounit.rspewin32versionvalue
msgid "Win32 Version Value = %x "
msgstr "Win32 版本号 = %x "

#: peinfounit.rspewritablememory
msgid "writable memory, "
msgstr "可写存储器, "

#: peinfounit.rsthisisnotavalidimage
msgid "This is not a valid image"
msgstr "PE 映像无法识别"

#: plugin.rserrordisabling
msgid "Error disabling %s"
msgstr "禁用插件错误 %s"

#: plugin.rserrorenabling
msgid "Error enabling %s"
msgstr "启用插件错误 %s"

#: plugin.rserrorloadingonlydllfilesareallowed
msgid "Error loading %s. Only DLL files are allowed"
msgstr "载入插件错误 %s. 只允许 DLL 文件"

#: plugin.rserrorloadingthedllismissingtheceplugin_getversione
msgid "Error loading %s. The dll is missing the CEPlugin_GetVersion export"
msgstr "载入插件错误 %s. DLL 中的 CEPlugin_GetVersion 缺少输出控制函数"

#: plugin.rserrorloadingthedllismissingtheceplugin_getversionf
msgid "Error loading %s. The dll is missing the CEPlugin_GetVersion function"
msgstr "载入插件错误 %s. DLL 中缺少 CEPlugin_GetVersion 函数"

#: plugin.rserrorloadingthegetversionfunctionreturnedfalse
msgid "Error loading %s. The GetVersion function returned FALSE"
msgstr "载入插件错误 %s. GetVersion 函数返回 FALSE"

#: plugin.rserrorloadingthisdllrequiresanewerversionofcetofunc
msgid "Error loading %s. This dll requires a newer version of ce to function properly"
msgstr "载入插件错误 %s. 这个 DLL 在当前 CE 中无法正常运行"

#: plugin.rsismissingtheceplugin_disablepluginexport
msgid "%s is missing the CEPlugin_DisablePlugin export"
msgstr "%s 中的 CEPlugin_DisablePlugin 缺少输出控制函数"

#: plugin.rsismissingtheceplugin_initializepluginexport
msgid "%s is missing the CEPlugin_InitializePlugin export"
msgstr "%s 中的 CEPlugin_InitializePlugin 缺少输出控制函数"

#: plugin.rsplugtheplugindllcouldnotbeloaded
msgid "The plugin dll could not be loaded:"
msgstr "DLL 插件无法加载:"

#: pluginexports.rsloadmodulefailed
msgid "LoadModule failed"
msgstr "LoadModule 失败"

#: pluginexports.rspluginaddress
msgctxt "pluginexports.rspluginaddress"
msgid "Plugin Address"
msgstr "插件地址"

#: pointeraddresslist.rspalinvalidscandatafile
msgctxt "pointeraddresslist.rspalinvalidscandatafile"
msgid "Invalid scandata file"
msgstr "无效的 scandata 文件"

#: pointeraddresslist.rspalinvalidscandataversion
msgctxt "pointeraddresslist.rspalinvalidscandataversion"
msgid "Invalid scandata version"
msgstr "无效的 scandata 版本"

#: pointerscanconnector.rscouldnotberesolved
msgctxt "pointerscanconnector.rscouldnotberesolved"
msgid " could not be resolved"
msgstr " could not be resolved"

#: pointerscanconnector.rserrorwhileconnecting
msgid "Error while connecting: "
msgstr "连接时发生错误"

#: pointerscanconnector.rsfailurecreatingsocket
msgctxt "pointerscanconnector.rsfailurecreatingsocket"
msgid "Failure creating socket"
msgstr "创建socket失败"

#: pointerscanconnector.rshost
msgctxt "pointerscanconnector.rshost"
msgid "host:"
msgstr "主机:"

#: pointerscanconnector.rsinvalidresponsefrom
msgid "invalid response from "
msgstr "无效的响应来自 "

#: pointerscanconnector.rssomeoneforgottogivethisconnector
msgid "Someone forgot to give this connector an OnConnected event..."
msgstr "有台机器忘了给连接返回一个 OnConnected 事件..."

#: pointerscancontroller.rsallpathsreceived
msgid "All paths received"
msgstr "接收到所有路径"

#: pointerscancontroller.rsfailurecopyingtargetprocessmemory
msgctxt "pointerscancontroller.rsfailurecopyingtargetprocessmemory"
msgid "Failure copying target process memory"
msgstr "复制目标进程内存失败"

#: pointerscancontroller.rsinvaliddata
msgid "invalid data:"
msgstr "无效的数据:"

#: pointerscancontroller.rsnoupdatefromtheclientforover120sec
msgid "No update from the client for over 120 seconds"
msgstr "客户端更新(数据)已超时120秒"

#: pointerscancontroller.rspscachildtriedtoconnect
msgid "A child tried to connect"
msgstr "尝试连接子节点"

#: pointerscancontroller.rspscalreadystillconnectedtothischild
msgid "Already/still connected to this child"
msgstr "仍然连接到子节点"

#: pointerscancontroller.rspscaparenttriedtoconnect
msgid "A parent tried to connect"
msgstr "尝试连接父节点"

#: pointerscancontroller.rspscchildisntidlewhilepreviouslyitwas
msgid "child isn't idle while previously it was..."
msgstr "在子节点空闲之前..."

#: pointerscancontroller.rspscduringscanfinishing
msgid "During scan finishing: "
msgstr "在扫描完成期间: "

#: pointerscancontroller.rspscerror
msgid " (Error: "
msgstr " (错误: "

#: pointerscancontroller.rspscfailurecreatingsocket
msgctxt "pointerscancontroller.rspscfailurecreatingsocket"
msgid "Failure creating socket"
msgstr "创建socket失败"

#: pointerscancontroller.rspscfailuretobindport
msgid "Failure to bind port "
msgstr "绑定端口失败 "

#: pointerscancontroller.rspscfailuretolisten
msgid "Failure to listen"
msgstr "监听失败"

#: pointerscancontroller.rspscforsomeunknownreasontheuntrustedchildisntidleanymore
msgid "For some unknown reason the untrusted child isn't idle anymore"
msgstr "由于未知的原因不可信的子节点不再闲置"

#: pointerscancontroller.rspschelloafterinitializtion
msgid "HELLO after initializtion"
msgstr "HELLO 之后初始化"

#: pointerscancontroller.rspscimpossibleerroruseloadedpointermapwasfalseetc
msgid "Impossible error: UseLoadedPointermap was false when a child message got handled"
msgstr "Impossible error: UseLoadedPointermap was false when a child message got handled"

#: pointerscancontroller.rspscinvalidchildpassword
msgid "Invalid child password"
msgstr "无效的子节点密码"

#: pointerscancontroller.rspscinvalidcommandwhilewaitingforhello
msgid "Invalid command while waiting for hello"
msgstr "无效的响应"

#: pointerscancontroller.rspscinvalidhandshakesignature
msgid "Invalid handshake signature"
msgstr "无效的握手(数据)签名"

#: pointerscancontroller.rspscinvalidmessage
msgid "Invalid message"
msgstr "无效的信息"

#: pointerscancontroller.rspscinvalidmessagereceived
msgid "Invalid message received"
msgstr "收到的信息无效"

#: pointerscancontroller.rspscinvalidparentpassword
msgid "Invalid parent password"
msgstr "无效的父节点密码"

#: pointerscancontroller.rspscinvalidqueuefile
msgid "Invalid queue file"
msgstr "无效的队列文件"

#: pointerscancontroller.rspscinvalidreplyforpsupdatereplycmdcurrentscanhasended
msgid "Invalid reply for PSUPDATEREPLYCMD_CURRENTSCANHASENDED"
msgstr "无效的回复 for PSUPDATEREPLYCMD_CURRENTSCANHASENDED"

#: pointerscancontroller.rspscinvalidresultreceivedafteruploadingthescanresults
msgid "Invalid result received after uploading the scanresults"
msgstr "接受到无效结果后上传到扫描报告"

#: pointerscancontroller.rspscinvalidresultreceivedfrompsupdatereplycmdherearesomepaths
msgid "Invalid result received from PSUPDATEREPLYCMD_HEREARESOMEPATHS"
msgstr "从 PSUPDATEREPLYCMD_HEREARESOMEPATHS 接收到无效的结果"

#: pointerscancontroller.rspscinvalidscandatareceivedfilecount
msgid "Invalid scandata received. filecount=0"
msgstr "接收到无效的scandata. filecount=0"

#: pointerscancontroller.rspscinvalidupdatestatusreplyreceived
msgid "Invalid UpdateStatus reply received"
msgstr "接收到无效的 UpdateStatus 回复"

#: pointerscancontroller.rspscnewscanstartedwhilenotdone
msgid "New scan started while not done"
msgstr "重新开始未完成的扫描"

#: pointerscancontroller.rspscnoresumeptrfilereaderpresent
msgid "no resume ptr file reader present"
msgstr "不读取并恢复 .ptr 文件"

#: pointerscancontroller.rspscparentdidntrespondproperlytopscmdprepareformytermination
msgid "Parent didn't respond properly to PSCMD_PREPAREFORMYTERMINATION"
msgstr "父节点没有响应 PSCMD_PREPAREFORMYTERMINATION"

#: pointerscancontroller.rspscsleeping
msgid ":Sleeping"
msgstr ":已休眠"

#: pointerscancontroller.rspscsuccesfullysentscandatatochild
msgid "Succesfully sent scandata to child"
msgstr "成功的发送scandata到子节点"

#: pointerscancontroller.rspscterminated
msgid ":Terminated"
msgstr ":已中止"

#: pointerscancontroller.rspscthechilddidntrespondtopsupdatereplycmdeverythingokasexpected
msgid "The child didn't respond to PSUPDATEREPLYCMD_EVERYTHINGOK as expected"
msgstr "子节点不如预期响应 PSUPDATEREPLYCMD_EVERYTHINGOK"

#: pointerscancontroller.rspscthechildissendingmeresultsofadifferentscan
msgid "The child is sending me results of a different scan"
msgstr "子节点发送给我一个不同的扫描结果"

#: pointerscancontroller.rspscthechildtriedtosendanegativeamount
msgid "The child tried to send a negative amount"
msgstr "子节点试图发送一个负数"

#: pointerscancontroller.rspscthechildtriedtosendmeresultswhileiwasstillbusy
msgid "The child tried to send me results while I was still busy"
msgstr "子节点试图发送给我一个结果但是我正繁忙"

#: pointerscancontroller.rspscthechildtriedtosendmorepathsatoncethanallowed
msgid "The child tried to send more paths at once than allowed"
msgstr "子节点试图允许发送更多的路径"

#: pointerscancontroller.rspscthechildtriedtosendmorepathsthanallowedafterarequest
msgid "The child tried to send more paths than allowed after a request"
msgstr "子节点试图发送超过允许的路径给我"

#: pointerscancontroller.rspsctheparenttriedtosendmeanegativeammountofpaths
msgid "The parent tried to send me a negative ammount of paths"
msgstr "父节点试图发送负数的路径"

#: pointerscancontroller.rspsctheparenttriedtosendmemorepathsthanallowedaafterupdate
msgid "The parent tried to send me more paths than allowed (after update)"
msgstr "父节点尝试发送更多的指针路径 (稍后再更新)"

#: pointerscancontroller.rspscthepointerlisthandlerwasdestroyedwithoutagoodreason
msgid "The pointerlisthandler was destroyed without a good reason"
msgstr "The pointerlisthandler was destroyed without a good reason"

#: pointerscancontroller.rspscthescanwasterminated
msgid "The scan was terminated"
msgstr "扫描被中止"

#: pointerscancontroller.rspsctheuploadwasterminated
msgid "The upload was terminated"
msgstr "上传被中止"

#: pointerscancontroller.rspscworking
msgid ":Working"
msgstr ":工作中"

#: pointerscancontroller.rspscwritingtodisk
msgid ":Writing to disk"
msgstr ":写入磁盘"

#: pointerscannerfrm.rsactive
msgctxt "pointerscannerfrm.rsactive"
msgid "Active"
msgstr "激活"

#: pointerscannerfrm.rsaddressspecifiersfoundinthewholeprocess
msgid "Address specifiers found in the whole process"
msgstr "在整个进程中找到地址说明符:"

#: pointerscannerfrm.rsareyousureyouwishyouforceadisconnect
msgid "Are you sure you wish you force a disconnect. The current paths will be lost"
msgstr "当前找到的路径将会丢失.你确定要断开连接吗?"

#: pointerscannerfrm.rsbaseaddress
msgid "Base Address"
msgstr "基址"

#: pointerscannerfrm.rsceinjectedpointerscan
msgid "CE Injected Pointerscan"
msgstr "CE 注入式指针扫描"

#: pointerscannerfrm.rscurrentlevel
msgid "Current Level"
msgstr "目前级别"

#: pointerscannerfrm.rserrorduringscan
msgid "Error during scan"
msgstr "扫描过程中出现错误"

#: pointerscannerfrm.rsevaluated
msgid "Evaluated"
msgstr "估计"

#: pointerscannerfrm.rsgeneratingpointermap
msgid "Generating pointermap..."
msgstr "正在生成指针映射集..."

#: pointerscannerfrm.rsisnotavalid4bytevalue
msgid "%s is not a valid 4 byte value"
msgstr "%s 不是有效的 4 字节值"

#: pointerscannerfrm.rsisnotavaliddoublevalue
msgid "%s is not a valid double value"
msgstr "%s 不是有效的双浮点值"

#: pointerscannerfrm.rsisnotavalidfloatingpointvalue
msgid "%s is not a valid floating point value"
msgstr "%s 不是有效的浮点值"

#: pointerscannerfrm.rslookingfor
msgid "Looking for"
msgstr "寻找"

#: pointerscannerfrm.rsoffset
msgctxt "pointerscannerfrm.rsoffset"
msgid "Offset"
msgstr "偏移"

#: pointerscannerfrm.rsonlythefirst1000000entrieswillbedisplayed
msgid "Only the first 1000000 entries will be displayed. Rescan will still work with all results.  (This is normal for a pointerscan, you MUST do a few rescans)"
msgstr "首次扫描只会显示前面第1000000条目，再次扫描仍将处理所有结果. (这是一个普通的指针扫描, 你必须多做几次\"再次扫描\")"

#: pointerscannerfrm.rsoutofdiskspacecleanupthediskorstop
msgid "OUT OF DISKSPACE! Clean up the disk or stop"
msgstr "硬盘空间满了! 清理硬盘或停止"

#: pointerscannerfrm.rspointercount
msgid "Pointer paths"
msgstr "指针路径"

#: pointerscannerfrm.rspointerpathsfound
msgid "Pointer paths found"
msgstr "找到指针路径"

#: pointerscannerfrm.rspointerscan
msgctxt "pointerscannerfrm.rspointerscan"
msgid "Pointer scan"
msgstr "指针扫描器"

#: pointerscannerfrm.rspointerscanresult
msgid "pointerscan result"
msgstr "指针扫描结果"

#: pointerscannerfrm.rspointsto
msgid "Points to"
msgstr "指针"

#: pointerscannerfrm.rspsactive
msgid " (Active)"
msgstr " (活动)"

#: pointerscannerfrm.rspschildren
msgid "Children:"
msgstr "子节点:"

#: pointerscannerfrm.rspsconnectingto
msgid "Connecting to:"
msgstr "连接到:"

#: pointerscannerfrm.rspsdisconnected
msgid " (Disconnected)"
msgstr " (断线)"

#: pointerscannerfrm.rspsdownloadingandhandlingresults
msgid " (Downloading and handling results)"
msgstr " (下载和处理的结果)"

#: pointerscannerfrm.rspsdownloadingscandata
msgid "Downloading scandata: %.1f%% (%dKB/%dKB : %d KB/sec)"
msgstr "正下载scandata: %.1f%% (%dKB/%dKB : %d KB/秒)"

#: pointerscannerfrm.rspsdoyouwishtoresumethecurrentpointerscanatalatertime
msgid "Do you wish to resume the current pointerscan at a later time?"
msgstr "你希望稍后恢复当前的指针扫描进度吗?"

#: pointerscannerfrm.rspsdynamicqueusize
msgid " Dynamic queue size:"
msgstr " 动态队列大小:"

#: pointerscannerfrm.rspsexportaborted
msgid "Export aborted"
msgstr "导出已中止"

#: pointerscannerfrm.rspsexporting
msgid "Exporting..."
msgstr "导出..."

#: pointerscannerfrm.rspsexporttodatabase
msgid "Export to database"
msgstr "导出到数据库"

#: pointerscannerfrm.rspsfindbyaddresspart1
msgid "Find by address requires an address. \""
msgstr "需要一个要查找的地址. 但\""

#: pointerscannerfrm.rspsfindbyaddresspart2
msgid "\" is not a valid address"
msgstr "\" 不是一个有效的地址"

#: pointerscannerfrm.rspsgeneratingpointermap
msgctxt "pointerscannerfrm.rspsgeneratingpointermap"
msgid "Generating pointermap"
msgstr "生成指针映射集"

#: pointerscannerfrm.rspsgiveanamefortheseresults
msgid "Give a name for these results"
msgstr "你要给扫描结果起个什么名字?"

#: pointerscannerfrm.rspsidle
msgid " (Idle)"
msgstr " (空闲)"

#: pointerscannerfrm.rspsimporting
msgid "Importing..."
msgstr "导出..."

#: pointerscannerfrm.rspsinvaliddatabase
msgid "Invalid database"
msgstr "无效的数据库"

#: pointerscannerfrm.rspslastupdate
msgid "Last update: "
msgstr "最近更新于: "

#: pointerscannerfrm.rspslowestknownpath
msgid "Lowest known path:"
msgstr "已知最少路径:"

#: pointerscannerfrm.rspsnetwork
msgctxt "pointerscannerfrm.rspsnetwork"
msgid "Network"
msgstr "网络"

#: pointerscannerfrm.rspsparent
msgid "Parent: "
msgstr "父节点: "

#: pointerscannerfrm.rspsparentdisconnectedwaitingforreconnect
msgid "Parent: <disconnected> (Waiting for reconnect)"
msgstr "父节点: <断开连接> (等待重新连接)"

#: pointerscannerfrm.rspsparentnone
msgid "Parent: <none>"
msgstr "父节点: <无>"

#: pointerscannerfrm.rspspathsevaluated
msgid "Paths evaluated: "
msgstr "路径预计: "

#: pointerscannerfrm.rspspathsseconds
msgid "Paths / seconds: (%.0n / s)"
msgstr "路径 / 秒: (%.0n / s)"

#: pointerscannerfrm.rspspscanguiupdatetimererror
msgid "pscangui update timer error: "
msgstr "pscangui 更新计时器出错: "

#: pointerscannerfrm.rspsqueued
msgid " (Queued: "
msgstr " (队列: "

#: pointerscannerfrm.rspsqueuesize
msgid "Queuesize: "
msgstr "队列大小: "

#: pointerscannerfrm.rspsrescanning
msgid "Rescanning"
msgstr "重新扫描"

#: pointerscannerfrm.rspsresultsfound
msgid "Results found: "
msgstr "发现的结果数: "

#: pointerscannerfrm.rspsscanduration
msgid "Scan duration: "
msgstr "扫描花费时间: "

#: pointerscannerfrm.rspssecondsago
msgid " seconds ago"
msgstr " 秒之前"

#: pointerscannerfrm.rspsstaticqueuesize
msgid "Static queue size: "
msgstr "静态队列大小: "

#: pointerscannerfrm.rspsstatistics
msgid "Statistics"
msgstr "统计"

#: pointerscannerfrm.rspsthereisalreadyapointerfilewiththsinamepresentinthisdatabase
msgid "There is already a pointerfile with this name present in this database. Replace it's content with this one ?"
msgstr "数据库中已有相同名称的pointerfile. 要覆盖它吗 ?"

#: pointerscannerfrm.rspsthisdatabasedoesntcontainanypointerfiles
msgid "This database does not contain any pointer files"
msgstr "这个数据库文件不包含任何指针"

#: pointerscannerfrm.rspsthreadcount
msgid "Threadcount: "
msgstr "线程数: "

#: pointerscannerfrm.rspsthreads
msgctxt "pointerscannerfrm.rspsthreads"
msgid "Threads"
msgstr "线程"

#: pointerscannerfrm.rspstimespentwriting
msgid "Time spent writing: "
msgstr "写入花费的时间: "

#: pointerscannerfrm.rspstotalpathsevaluater
msgid "Total paths evaluated: "
msgstr "总路径预计: "

#: pointerscannerfrm.rspstotalpathsseconds
msgid "Total paths / seconds: (%.0n / s)"
msgstr "总路径 / 秒: (%.0n / s)"

#: pointerscannerfrm.rspstotalqueuesize
msgid "Total Queuesize: "
msgstr "总队列大小: "

#: pointerscannerfrm.rspstrusted
msgid " (Trusted)"
msgstr " (可信任)"

#: pointerscannerfrm.rspstrusted2
msgid "Trusted: "
msgstr "可信任: "

#: pointerscannerfrm.rspsuniquepointervaluesintarget
msgid "Unique pointervalues in target:"
msgstr "目标中独特的指针值:"

#: pointerscannerfrm.rspsuploadingscandata
msgid " (Uploading scandata: %.1f%% (%dKB/%dKB : %d KB/sec)"
msgstr " (正在上传 scandata: %.1f%% (%dKB/%dKB : %d KB/秒)"

#: pointerscannerfrm.rssavingandterminating
msgctxt "pointerscannerfrm.rssavingandterminating"
msgid "Saving..."
msgstr "保存中..."

#: pointerscannerfrm.rssleeping
msgid "Sleeping"
msgstr "挂起"

#: pointerscannerfrm.rsstop
msgctxt "pointerscannerfrm.rsstop"
msgid "Stop"
msgstr "停止"

#: pointerscannerfrm.rsterminating
msgid "Terminating"
msgstr "终止"

#: pointerscannerfrm.rsthread
msgid "Thread"
msgstr "线程"

#: pointerscannerfrm.rsthreads
msgctxt "pointerscannerfrm.rsthreads"
msgid "Threads"
msgstr "线程"

#: pointerscannerfrm.rstime
msgid "Time"
msgstr "时间"

#: pointerscannerfrm.rswritingtodisk
msgid "Writing to disk"
msgstr "正在写入硬盘"

#: pointerscannersettingsfrm.rsadd
msgctxt "pointerscannersettingsfrm.rsadd"
msgid "Add"
msgstr "添加"

#: pointerscannersettingsfrm.rsaddress
msgctxt "pointerscannersettingsfrm.rsaddress"
msgid "Address"
msgstr "地址"

#: pointerscannersettingsfrm.rscouldnotberesolvedtoanipaddress
msgid "could not be resolved to an IP address"
msgstr "无法解析 IP 地址"

#: pointerscannersettingsfrm.rsfilename
msgctxt "pointerscannersettingsfrm.rsfilename"
msgid "Filename"
msgstr "文件名"

#: pointerscannersettingsfrm.rsfrom
msgctxt "pointerscannersettingsfrm.rsfrom"
msgid "From"
msgstr "从"

#: pointerscannersettingsfrm.rshasaninvalidport
msgid "%s has an invalid port (%s)"
msgstr "%s 为一个无效的端口 (%s)"

#: pointerscannersettingsfrm.rshasnotbeengivenavalidaddress
msgid "%s has not been given a valid address"
msgstr "%s 为无效的地址"

#: pointerscannersettingsfrm.rshigher
msgctxt "pointerscannersettingsfrm.rshigher"
msgid "Higher"
msgstr "较高"

#: pointerscannersettingsfrm.rshighest
msgctxt "pointerscannersettingsfrm.rshighest"
msgid "Highest"
msgstr "最高"

#: pointerscannersettingsfrm.rsidle
msgctxt "pointerscannersettingsfrm.rsidle"
msgid "Idle"
msgstr "空闲"

#: pointerscannersettingsfrm.rsinvalidaddress
msgctxt "pointerscannersettingsfrm.rsinvalidaddress"
msgid "Invalid address"
msgstr "无效地址"

#: pointerscannersettingsfrm.rslastoffset
msgctxt "pointerscannersettingsfrm.rslastoffset"
msgid "Last offset"
msgstr "最后一个偏移"

#: pointerscannersettingsfrm.rslimitscantospecifiedregionfile
msgctxt "pointerscannersettingsfrm.rslimitscantospecifiedregionfile"
msgid "Limit scan to specified region file"
msgstr "限制扫描指定的内存区域的文件"

#: pointerscannersettingsfrm.rslower
msgctxt "pointerscannersettingsfrm.rslower"
msgid "Lower"
msgstr "较低"

#: pointerscannersettingsfrm.rslowest
msgctxt "pointerscannersettingsfrm.rslowest"
msgid "Lowest"
msgstr "最低"

#: pointerscannersettingsfrm.rsnocomparefiles
msgid "You will get billions of useless results and giga/terrabytes of wasted diskspace if you do not use the compare results with other saved pointermap option. Are you sure ?"
msgstr "如果你不使用\"与其它保存的\"指针映射集\"结果相比对\"选项, 你将获得数十亿个无效的结果, 浪费你的磁盘空间. 你确定吗 ?"

#: pointerscannersettingsfrm.rsnormal
msgctxt "pointerscannersettingsfrm.rsnormal"
msgid "Normal"
msgstr "标准"

#: pointerscannersettingsfrm.rspleasefillinanaddresstolookfor
msgid "Please fill in an address to look for"
msgstr "请填写一个要寻找的地址"

#: pointerscannersettingsfrm.rsremove
msgctxt "pointerscannersettingsfrm.rsremove"
msgid "Remove"
msgstr "移除"

#: pointerscannersettingsfrm.rsreusedthesamefile
msgid "This file is already in the list of scandata files to be used"
msgstr "这个文件已经在scandata文件的列表中被使用"

#: pointerscannersettingsfrm.rsscandatafilter
msgid "All files (*.*)|*.*|Scan Data (*.scandata)|*.scandata"
msgstr "所有文件 (*.*)|*.*|扫描数据 (*.scandata)|*.scandata"

#: pointerscannersettingsfrm.rsselectafile
msgid "<Select a file>"
msgstr "<请选择文件>"

#: pointerscannersettingsfrm.rstimecritical
msgctxt "pointerscannersettingsfrm.rstimecritical"
msgid "TimeCritical"
msgstr "临界"

#: pointerscannersettingsfrm.rsto
msgctxt "pointerscannersettingsfrm.rsto"
msgid "To"
msgstr "至"

#: pointerscannersettingsfrm.rsuseloadedpointermap
msgctxt "pointerscannersettingsfrm.rsuseloadedpointermap"
msgid "Use saved pointermap"
msgstr "使用保存的\"指针映射集\""

#: pointerscannersettingsfrm.strmaxoffsetsisstupid
msgid "Sorry, but the max offsets should be 1 or higher, or else disable the checkbox"
msgstr "对不起, 最大偏移值应为1或更高, 否则禁用复选框"

#: pointerscanresultreader.rsbuggedlist
msgid "BuggedList"
msgstr "BuggedList"

#: pointerscanresultreader.rspsrcorruptedpointerscanfile
msgid "Corrupted pointerscan file"
msgstr "\"指针扫描\"(.PTR)文件已损坏"

#: pointerscanresultreader.rspsrinvalidpointerscanfileversion
msgid "Invalid pointerscan file version"
msgstr "无效的\"指针扫描\"(.PTR)文件版本"

#: pointerscansettingsipconnectionlist.rspssicladd
msgctxt "pointerscansettingsipconnectionlist.rspssicladd"
msgid "Add"
msgstr "添加"

#: pointerscansettingsipconnectionlist.rspssiclhost
msgctxt "pointerscansettingsipconnectionlist.rspssiclhost"
msgid "Host"
msgstr "主机"

#: pointerscansettingsipconnectionlist.rspssiclpassword
msgctxt "pointerscansettingsipconnectionlist.rspssiclpassword"
msgid "Password"
msgstr "密码"

#: pointerscansettingsipconnectionlist.rspssiclport
msgctxt "pointerscansettingsipconnectionlist.rspssiclport"
msgid "Port"
msgstr "端口"

#: pointerscansettingsipconnectionlist.rspssiclremove
msgctxt "pointerscansettingsipconnectionlist.rspssiclremove"
msgid "Remove"
msgstr "移除"

#: pointerscansettingsipconnectionlist.rspssiclstable
msgid "Stable"
msgstr "是否稳定"

#: pointervaluelist.rsallocatingbytestobuffer
msgid "Allocating %s bytes to 'buffer'"
msgstr "分配 %s 字节到 '缓冲区'"

#: pointervaluelist.rsnomemoryfoundinthespecifiedregion
msgctxt "pointervaluelist.rsnomemoryfoundinthespecifiedregion"
msgid "No memory found in the specified region"
msgstr "指定区域中没有找到内存"

#: pointervaluelist.rspointervaluesetuperror
msgid "Pointer value setup error"
msgstr "指针值配置错误"

#: pointervaluelist.rspvinvalidscandatafile
msgctxt "pointervaluelist.rspvinvalidscandatafile"
msgid "Invalid scandata file"
msgstr "无效的 scandata 文件"

#: pointervaluelist.rspvinvalidscandataversion
msgctxt "pointervaluelist.rspvinvalidscandataversion"
msgid "Invalid scandata version"
msgstr "无效的 scandata 版本"

#: pointervaluelist.rspvnotenoughmemoryfreetoscan
msgctxt "pointervaluelist.rspvnotenoughmemoryfreetoscan"
msgid "Not enough memory free to scan"
msgstr "没有足够的空闲内存完成扫描"

#: processlist.rsicantgettheprocesslistyouarepropablyusingwindowsnt
msgctxt "processlist.rsicantgettheprocesslistyouarepropablyusingwindowsnt"
msgid "I can't get the process list. You are propably using windows NT. Use the window list instead!"
msgstr "无法获取进程列表, 你使用的或许是 windows NT, 现在改用窗口列表代替!"

#: processwindowunit.rsattachdebuggerornot
msgid "Are you sure you want to attach the debugger and not just open this process? (You can later on always attach the debugger)"
msgstr "你确定要将调试器附加到进程而不仅仅是打开? (你以后可以总是附加调试器)"

#: processwindowunit.rscreateprocess
msgctxt "processwindowunit.rscreateprocess"
msgid "Create Process"
msgstr "创建进程"

#: processwindowunit.rsentertheprocessid
msgid "Enter the ProcessID"
msgstr "输入进程的ID"

#: processwindowunit.rsfilter
msgctxt "processwindowunit.rsfilter"
msgid "Filter"
msgstr "过滤器"

#: processwindowunit.rsfirstselectaprocess
msgctxt "processwindowunit.rsfirstselectaprocess"
msgid "First select a process!"
msgstr "请先选择一个进程!"

#: processwindowunit.rsisntavalidprocessid
msgctxt "processwindowunit.rsisntavalidprocessid"
msgid "%s isn't a valid processID"
msgstr "%s 进程 ID 无效"

#: processwindowunit.rsmanualpid
msgid "Manual PID"
msgstr "手动输入 PID"

#: processwindowunit.rsoptionallaunchparameters
msgid "Optional launch parameters"
msgstr "可选启动参数"

#: processwindowunit.rsphysicalmemory
msgctxt "processwindowunit.rsphysicalmemory"
msgid "Physical Memory"
msgstr "物理内存"

#: processwindowunit.rspleaseselectanotherprocess
msgid "Please select another process"
msgstr "请选择另一个进程"

#: processwindowunit.rsprocesslist
msgctxt "processwindowunit.rsprocesslist"
msgid "Process List"
msgstr "进程列表"

#: processwindowunit.rsprocesslistlong
msgctxt "processwindowunit.rsprocesslistlong"
msgid "Process List(long)"
msgstr "进程列表(详细)"

#: processwindowunit.rsscanningclicktostop
msgid "Scanning (Click to stop)"
msgstr "正在扫描 (点击停止)"

#: processwindowunit.rswhatareyoulookingfor
msgid "What are you looking for?"
msgstr "你在寻找什么?"

#: processwindowunit.rsyoucanonlyloadexefiles
msgctxt "processwindowunit.rsyoucanonlyloadexefiles"
msgid "You can only load EXE files"
msgstr "你只能加载 EXE 文件"

#: savedisassemblyfrm.rscopy
msgctxt "savedisassemblyfrm.rscopy"
msgid "Copy"
msgstr "复制"

#: savedisassemblyfrm.rscopydisassembledoutput
msgid "Copy disassembled output"
msgstr "复制反汇编输出"

#: savedisassemblyfrm.rssave
msgctxt "savedisassemblyfrm.rssave"
msgid "Save"
msgstr "保存"

#: savedisassemblyfrm.rssavedisassembledoutput
msgctxt "savedisassemblyfrm.rssavedisassembledoutput"
msgid "Save disassembled output"
msgstr "保存反汇编输出"

#: savedisassemblyfrm.rsstopcopying
msgid "Stop copying"
msgstr "停止复制"

#: savedisassemblyfrm.rsstopsaving
msgid "Stop saving"
msgstr "停止保存"

#: savedscanhandler.rsfailureinfindinginthefirstscanresults
msgid "Failure in finding %s in the first scan results"
msgstr "首次扫描结果中无法找到 %s"

#: savedscanhandler.rsfailureinfindinginthepreviousscanresults
msgid "Failure in finding %s in the previous scan results"
msgstr "上次扫描结果中无法找到 %s"

#: savedscanhandler.rsinvalidorderofcallinggetpointertoaddress
msgid "Invalid order of calling getpointertoaddress"
msgstr "无法调用命令 getpointertoaddress"

#: savedscanhandler.rsmaxaddresslistcountis0meanstheaddresslistisbad
msgid "maxaddresslistcount is 0 (Means: the addresslist is bad)"
msgstr "地址列表最大总数为 0 (注: 这个地址列表可能已经损坏)"

#: savedscanhandler.rsnofirstscandatafilesfound
msgid "No first scan data files found"
msgstr "未找到首次扫描时的数据文件"

#: speedhack2.rsfailureconfiguringspeedhackpart
msgid "Failure configuring speedhack part"
msgstr "速度配置失败"

#: speedhack2.rsfailureenablingspeedhackdllinjectionfailed
msgid "Failure enabling speedhack. (DLL injection failed)"
msgstr "变速失败. (DLL 注入失败)"

#: speedhack2.rsfailuresettingspeed
msgid "Failure setting speed"
msgstr "设置速度失败"

#: structuresaddelementfrm.rssae2bytes
msgctxt "structuresaddelementfrm.rssae2bytes"
msgid "2 字节"
msgstr "2 字节"

#: structuresaddelementfrm.rssae4bytes
msgctxt "structuresaddelementfrm.rssae4bytes"
msgid "4 字节"
msgstr "4 字节"

#: structuresfrm.rsareyousureyouwanttodelete
msgctxt "structuresfrm.rsareyousureyouwanttodelete"
msgid "Are you sure you want to delete %s?"
msgstr "你确定要删除 %s吗?"

#: structuresfrm.rsareyousureyouwanttoremoveallstructures
msgctxt "structuresfrm.rsareyousureyouwanttoremoveallstructures"
msgid "Are you sure you want to remove all structures?"
msgstr "你确定要删除所有结构吗?"

#: structuresfrm.rsautogeneratedfor
msgctxt "structuresfrm.rsautogeneratedfor"
msgid "Autogenerated for %s"
msgstr "从 %s 自动产生"

#: structuresfrm.rschangegroup
msgctxt "structuresfrm.rschangegroup"
msgid "Change group"
msgstr "更改群组"

#: structuresfrm.rsdissectdata
msgctxt "structuresfrm.rsdissectdata"
msgid "Dissect Data"
msgstr "分析数据"

#: structuresfrm.rsdoyouwantcheatenginetotryandfillinthemostbasictype
msgctxt "structuresfrm.rsdoyouwantcheatenginetotryandfillinthemostbasictype"
msgid "Do you want Cheat Engine to try and fill in the most basic types of the struct using the current address?"
msgstr "你希望 Cheat Engine 使用当前地址尝试填入结构的最基本类型?"

#: structuresfrm.rsfirstselectastructureyouwanttomodifyordefine
msgctxt "structuresfrm.rsfirstselectastructureyouwanttomodifyordefine"
msgid "First select a structure you want to modify or define one first"
msgstr "请先选择你想修改或定义的首个结构"

#: structuresfrm.rsgivetheaddressofthiselement
msgctxt "structuresfrm.rsgivetheaddressofthiselement"
msgid "Give the address of this element"
msgstr "给出目前元素的地址"

#: structuresfrm.rsgivethenameforthisstructure
msgctxt "structuresfrm.rsgivethenameforthisstructure"
msgid "Give the name for this structure"
msgstr "为当前结构命名"

#: structuresfrm.rsgivethenewnameofthisstructure
msgctxt "structuresfrm.rsgivethenewnameofthisstructure"
msgid "Give the new name of this structure"
msgstr "为当前结构重命名"

#: structuresfrm.rshowmanybytesdoyouwanttoshiftthisandfollowingoffset
msgid "How many bytes do you want to shift this and following offsets?"
msgstr "要移动偏移多少字节?"

#: structuresfrm.rsihavenoideawhatmeans
msgctxt "structuresfrm.rsihavenoideawhatmeans"
msgid "I have no idea what %s means"
msgstr "我不知道 %s 是什么方法"

#: structuresfrm.rslockmemory
msgctxt "structuresfrm.rslockmemory"
msgid "Lock memory"
msgstr "锁定内存"

#: structuresfrm.rsmemorydissect
msgctxt "structuresfrm.rsmemorydissect"
msgid "Memory dissect"
msgstr "内存分析"

#: structuresfrm.rsnewinterval
msgctxt "structuresfrm.rsnewinterval"
msgid "New interval"
msgstr "新的间隔"

#: structuresfrm.rspleasegiveastartingoffsettoevaluate
msgctxt "structuresfrm.rspleasegiveastartingoffsettoevaluate"
msgid "Please give a starting offset to evaluate"
msgstr "请给出一个初始偏移以便估计"

#: structuresfrm.rspleasegiveastartingsizeofthestructyoucanchangethis
msgctxt "structuresfrm.rspleasegiveastartingsizeofthestructyoucanchangethis"
msgid "Please give a starting size of the struct (You can change this later if needed)"
msgstr "请给出结构的初始大小(你也可以在需要的时候更改)"

#: structuresfrm.rspleasegivethesizeoftheblocktoevaluate
msgctxt "structuresfrm.rspleasegivethesizeoftheblocktoevaluate"
msgid "Please give the size of the block to evaluate"
msgstr "请给出区块大小以便估计"

#: structuresfrm.rspointerto
msgctxt "structuresfrm.rspointerto"
msgid "Pointer"
msgstr "指针"

#: structuresfrm.rsrecalculatebaseofstructure
msgctxt "structuresfrm.rsrecalculatebaseofstructure"
msgid "Recalculate base of structure"
msgstr "重新计算基址的结构"

#: structuresfrm.rsrenamestructure
msgctxt "structuresfrm.rsrenamestructure"
msgid "Rename structure"
msgstr "重命名结构"

#: structuresfrm.rsstructuredefine
msgctxt "structuresfrm.rsstructuredefine"
msgid "Structure define"
msgstr "结构定义"

#: structuresfrm.rsstructuredefiner
msgctxt "structuresfrm.rsstructuredefiner"
msgid "Structure definer"
msgstr "结构定义器"

#: structuresfrm.rsstructureviewlock
msgctxt "structuresfrm.rsstructureviewlock"
msgid "Structure view lock"
msgstr "锁定结构视图"

#: structuresfrm.rsthisisnotavalidstructurefile
msgctxt "structuresfrm.rsthisisnotavalidstructurefile"
msgid "This is not a valid structure file"
msgstr "结构文件无法识别"

#: structuresfrm.rsthisisquiteabigstructurehowmanybytesdoyouwanttosav
msgctxt "structuresfrm.rsthisisquiteabigstructurehowmanybytesdoyouwanttosav"
msgid "This is quite a big structure. How many bytes do you want to save?"
msgstr "这是一个相当大的结构. 你想保存多少个字节?"

#: structuresfrm.rsunkownfileextension
msgctxt "structuresfrm.rsunkownfileextension"
msgid "Unknown file extension"
msgstr "未知的文件扩展名"

#: structuresfrm.rsunlockmemory
msgctxt "structuresfrm.rsunlockmemory"
msgid "Unlock memory"
msgstr "解锁内存"

#: structuresfrm.rsunnamedstructure
msgctxt "structuresfrm.rsunnamedstructure"
msgid "unnamed structure"
msgstr "未命名的结构"

#: structuresfrm.rsupdateinterval
msgctxt "structuresfrm.rsupdateinterval"
msgid "Update interval"
msgstr "更新间隔"

#: structuresfrm.rswhichgroupdoyouwanttosetthisaddressto
msgctxt "structuresfrm.rswhichgroupdoyouwanttosetthisaddressto"
msgid "Which group do you want to set this address to?"
msgstr "哪一群组要设置这个地址?"

#: structuresfrm.rswrongversion
msgctxt "structuresfrm.rswrongversion"
msgid "This structure file was generated with a newer version of Cheat Engine. (That means there's more than likely a new version so please update....)"
msgstr "结构文件由较新版本的 Cheat Engine 制作. (这意味着升级更新....)"

#: structuresfrm2.rsaddressvalue
msgctxt "structuresfrm2.rsaddressvalue"
msgid "Address: Value"
msgstr "地址: 数值"

#: structuresfrm2.rsareyousureyouwanttodelete
msgctxt "structuresfrm2.rsareyousureyouwanttodelete"
msgid "Are you sure you want to delete %s?"
msgstr "你确定要删除 %s?"

#: structuresfrm2.rsareyousureyouwanttoremoveallstructures
msgctxt "structuresfrm2.rsareyousureyouwanttoremoveallstructures"
msgid "Are you sure you want to remove all structures?"
msgstr "你确定要删除所有结构?"

#: structuresfrm2.rsautogeneratedfor
msgctxt "structuresfrm2.rsautogeneratedfor"
msgid "Autogenerated for %s"
msgstr "从 %s 自动产生"

#: structuresfrm2.rschangegroup
msgctxt "structuresfrm2.rschangegroup"
msgid "Change group"
msgstr "更改群组"

#: structuresfrm2.rschangegroup2
msgid "Change Group"
msgstr "更改群组"

#: structuresfrm2.rscopy
msgctxt "structuresfrm2.rscopy"
msgid "Copy"
msgstr "复制"

#: structuresfrm2.rscut
msgctxt "structuresfrm2.rscut"
msgid "Cut"
msgstr "剪切"

#: structuresfrm2.rsdefinepointer
msgid "Define pointer"
msgstr "默认指针"

#: structuresfrm2.rsdeleteaddress
msgid "Delete address"
msgstr "删除地址"

#: structuresfrm2.rsdissectdata
msgctxt "structuresfrm2.rsdissectdata"
msgid "Dissect Data"
msgstr "分析数据"

#: structuresfrm2.rsdoyouwantcheatenginetotryandfillinthemostbasictype
msgctxt "structuresfrm2.rsdoyouwantcheatenginetotryandfillinthemostbasictype"
msgid "Do you want Cheat Engine to try and fill in the most basic types of the struct using the current address?"
msgstr "你希望 Cheat Engine 填写结构的最基本类型时使用当前的地址吗?"

#: structuresfrm2.rsfirstselectastructureyouwanttomodifyordefine
msgctxt "structuresfrm2.rsfirstselectastructureyouwanttomodifyordefine"
msgid "First select a structure you want to modify or define one first"
msgstr "请先选择一个你想要修改或定义的结构"

#: structuresfrm2.rsfs2structuredefine
msgctxt "structuresfrm2.rsfs2structuredefine"
msgid "Structure define"
msgstr "结构定义"

#: structuresfrm2.rsgivetheaddressofthiselement
msgctxt "structuresfrm2.rsgivetheaddressofthiselement"
msgid "Give the address of this element"
msgstr "给出目前元素的地址"

#: structuresfrm2.rsgivethenameforthisstructure
msgctxt "structuresfrm2.rsgivethenameforthisstructure"
msgid "Give the name for this structure"
msgstr "命名当前结构"

#: structuresfrm2.rsgivethenewnameofthisstructure
msgctxt "structuresfrm2.rsgivethenewnameofthisstructure"
msgid "Give the new name of this structure"
msgstr "重命名当前结构"

#: structuresfrm2.rshexadecimal
msgctxt "structuresfrm2.rshexadecimal"
msgid "Hexadecimal"
msgstr "十六进制"

#: structuresfrm2.rshowmanybytesdoyouwanttoshiftthisandfollowingoffset
msgid "How many bytes do you want to shift this and following offsets? (Decimal)"
msgstr "要移动偏移多少字节(十进制)"

#: structuresfrm2.rsihavenoideawhatmeans
msgctxt "structuresfrm2.rsihavenoideawhatmeans"
msgid "I have no idea what %s means"
msgstr "我不知道 %s 是什么意思"

#: structuresfrm2.rslock
msgid "Lock"
msgstr "锁定"

#: structuresfrm2.rslockmemory
msgctxt "structuresfrm2.rslockmemory"
msgid "Lock memory"
msgstr "锁定内存"

#: structuresfrm2.rsmemorydissect
msgctxt "structuresfrm2.rsmemorydissect"
msgid "Memory dissect"
msgstr "内存分析"

#: structuresfrm2.rsnewinterval
msgctxt "structuresfrm2.rsnewinterval"
msgid "New interval"
msgstr "新的间隔"

#: structuresfrm2.rsp
msgctxt "structuresfrm2.rsp"
msgid "P->"
msgstr "P->"

#: structuresfrm2.rspaste
msgctxt "structuresfrm2.rspaste"
msgid "Paste"
msgstr "粘贴"

#: structuresfrm2.rspleasegiveastartingoffsettoevaluate
msgctxt "structuresfrm2.rspleasegiveastartingoffsettoevaluate"
msgid "Please give a starting offset to evaluate"
msgstr "请给出你估计的起始偏移"

#: structuresfrm2.rspleasegiveastartingsizeofthestructyoucanchangethis
msgctxt "structuresfrm2.rspleasegiveastartingsizeofthestructyoucanchangethis"
msgid "Please give a starting size of the struct (You can change this later if needed)"
msgstr "请给出结构的初始大小 (你也可以在需要的时候更改)"

#: structuresfrm2.rspleasegivethesizeoftheblocktoevaluate
msgctxt "structuresfrm2.rspleasegivethesizeoftheblocktoevaluate"
msgid "Please give the size of the block to evaluate"
msgstr "请给出你估计的区块大小"

#: structuresfrm2.rspointerto
msgctxt "structuresfrm2.rspointerto"
msgid "Pointer"
msgstr "指针"

#: structuresfrm2.rsrecalculatebaseofstructure
msgctxt "structuresfrm2.rsrecalculatebaseofstructure"
msgid "Recalculate base of structure"
msgstr "重新计算基址的结构"

#: structuresfrm2.rsrenamestructure
msgctxt "structuresfrm2.rsrenamestructure"
msgid "Rename structure"
msgstr "重命名结构"

#: structuresfrm2.rssf2areyousureyouwanttodeleteallthedefinedstructures
msgid "Are you sure you want to delete all the defined structures ?"
msgstr "你确定要删除所有已定义的结构吗 ?"

#: structuresfrm2.rssf2areyousureyouwanttodeletethestructurenamed
msgid "Are you sure you want to delete the structure named :"
msgstr "你确定要删除名称为 :"

#: structuresfrm2.rssf2autocreatedfrom
msgid "Autocreated from "
msgstr "自动创建 从 "

#: structuresfrm2.rssf2autocreatestructure
msgid "Autocreate structure"
msgstr "自动创建结构"

#: structuresfrm2.rssf2autocreatestructuresize
msgid "Autocreate structure size: "
msgstr "自动创建的结构大小: "

#: structuresfrm2.rssf2changevalue
msgctxt "structuresfrm2.rssf2changevalue"
msgid "Change Value"
msgstr "更改数值"

#: structuresfrm2.rssf2defaultsize
msgid "Default size:"
msgstr "默认大小:"

#: structuresfrm2.rssf2deletegroup
msgid "Delete group"
msgstr "删除群组"

#: structuresfrm2.rssf2givethenewname
msgid "Give the new name"
msgstr "请输入新的名称"

#: structuresfrm2.rssf2givethenewnameforthegroup
msgid "Give the new name for the group"
msgstr "请输入新群组的名称"

#: structuresfrm2.rssf2givethenewnameforthisstructure
msgid "Give the new name for this structure"
msgstr "请给出此结构的新名称"

#: structuresfrm2.rssf2group
msgctxt "structuresfrm2.rssf2group"
msgid "Group "
msgstr "群组 "

#: structuresfrm2.rssf2group1
msgctxt "structuresfrm2.rssf2group1"
msgid "Group 1"
msgstr "群组 1"

#: structuresfrm2.rssf2group2
msgctxt "structuresfrm2.rssf2group2"
msgid "Group "
msgstr "群组 "

#: structuresfrm2.rssf2grouppicker
msgid "Group picker"
msgstr "选择一个群组"

#: structuresfrm2.rssf2hex
msgid " (Hex)"
msgstr " (Hex)"

#: structuresfrm2.rssf2nameforthisgroup
msgid "Name for this group"
msgstr "此群组的名称"

#: structuresfrm2.rssf2newcolumnname
msgid "New column name"
msgstr "新的栏目名"

#: structuresfrm2.rssf2newgroup
msgid "<New group>"
msgstr "<新建群组>"

#: structuresfrm2.rssf2newgroup2
msgid "New group"
msgstr "新建群组"

#: structuresfrm2.rssf2newvalueforthisaddress
msgid "New value for this address:"
msgstr "此地址的新数值:"

#: structuresfrm2.rssf2rename
msgctxt "structuresfrm2.rssf2rename"
msgid "Rename"
msgstr "重命名"

#: structuresfrm2.rssf2renamegroup
msgid "Rename group"
msgstr "重命名群组"

#: structuresfrm2.rssf2secletthegroupthiscolumnshouldbecomepartof
msgid "Select the group this column should become part of"
msgstr "选择的群组成为此列的一部分"

#: structuresfrm2.rssf2setnamerename
msgid "Set name/Rename"
msgstr "设定 名称/重命名"

#: structuresfrm2.rssf2shadowcopyat
msgid "Lock ( Shadowcopy at %s )"
msgstr "锁定 ( 影子复制于 %s )"

#: structuresfrm2.rssf2signed
msgid " (Signed)"
msgstr " (有符号)"

#: structuresfrm2.rssf2structurerename
msgid "Structure rename"
msgstr "重命名结构"

#: structuresfrm2.rssf2thegapbetweenoffset
msgid "The gap between offset %x and %x is %d bytes long. Autofill this?"
msgstr "偏移 %x 与偏移 %x 之间的相差是 %d 字节长度. 自动填充吗?"

#: structuresfrm2.rssf2thegroupscancommandhasbeencopiedtotheclipboard
msgid "The groupscan command has been copied to the clipboard"
msgstr "\"群组扫描\"命令已被复制到剪贴板"

#: structuresfrm2.rssf2to
msgid " to "
msgstr " 到 "

#: structuresfrm2.rssf2tstructcolumncreateerror
msgid "TStructColumn.create Error"
msgstr "TStructColumn.create 错误"

#: structuresfrm2.rssf2unknowncustomtype
msgid "Unknown custom type"
msgstr "未知的自定义类型"

#: structuresfrm2.rssf2whatnameshouldthiscolumnhave
msgid "What name should this column have?"
msgstr "本栏目要叫什么名字?"

#: structuresfrm2.rssignedinteger
msgid "Signed Integer"
msgstr "带符号整数"

#: structuresfrm2.rsspider
msgid "Spider"
msgstr "Spider"

#: structuresfrm2.rsstructalreadyexists
msgid "The structure named %s already exists. Are you sure you want to make another structure with this name ?"
msgstr "已存在名为 %s 的结构. 你确定要用该名称制作其他结构?"

#: structuresfrm2.rsstructuredefine
msgctxt "structuresfrm2.rsstructuredefine"
msgid "Structure define"
msgstr "结构定义"

#: structuresfrm2.rsstructuredefiner
msgctxt "structuresfrm2.rsstructuredefiner"
msgid "Structure definer"
msgstr "结构定义"

#: structuresfrm2.rsstructuredissect
msgctxt "structuresfrm2.rsstructuredissect"
msgid "Structure dissect"
msgstr "结构分析"

#: structuresfrm2.rsstructureviewlock
msgctxt "structuresfrm2.rsstructureviewlock"
msgid "Structure view lock"
msgstr "锁定结构视图"

#: structuresfrm2.rsthisisnotavalidstructurefile
msgctxt "structuresfrm2.rsthisisnotavalidstructurefile"
msgid "This is not a valid structure file"
msgstr "结构文件无法识别"

#: structuresfrm2.rsthisisquiteabigstructurehowmanybytesdoyouwanttosav
msgctxt "structuresfrm2.rsthisisquiteabigstructurehowmanybytesdoyouwanttosav"
msgid "This is quite a big structure. How many bytes do you want to save?"
msgstr "这是一个相当大的结构. 你想保存多少个字节?"

#: structuresfrm2.rsundefined
msgctxt "structuresfrm2.rsundefined"
msgid "undefined"
msgstr "未定义"

#: structuresfrm2.rsunkownfileextension
msgctxt "structuresfrm2.rsunkownfileextension"
msgid "Unknown file extension"
msgstr "未知的文件扩展名"

#: structuresfrm2.rsunlockmemory
msgctxt "structuresfrm2.rsunlockmemory"
msgid "Unlock memory"
msgstr "解锁内存"

#: structuresfrm2.rsunnamedstructure
msgctxt "structuresfrm2.rsunnamedstructure"
msgid "unnamed structure"
msgstr "未命名的结构"

#: structuresfrm2.rsunsignedinteger
msgid "Unsigned Integer"
msgstr "无符号整数"

#: structuresfrm2.rsupdateinterval
msgctxt "structuresfrm2.rsupdateinterval"
msgid "Update interval"
msgstr "更新间隔"

#: structuresfrm2.rsupgradepointer
msgid "Upgrade child structure to full structure"
msgstr "子结构升级到完整结构"

#: structuresfrm2.rswhichgroupdoyouwanttosetthisaddressto
msgctxt "structuresfrm2.rswhichgroupdoyouwanttosetthisaddressto"
msgid "Which group do you want to set this address to?"
msgstr "你要把地址设置到哪一群组?"

#: structuresfrm2.rswrongversion
msgctxt "structuresfrm2.rswrongversion"
msgid "This structure file was generated with a newer version of Cheat Engine. (That means there's more than likely a new version so please update....)"
msgstr "结构文件由新版本的 Cheat Engine 制作. (这意味着升级更新....)"

#: symbolconfigunit.rsareyousureyouwanttoremoveallsymbolsfromthelist
msgid "Are you sure you want to remove all symbols from the list?"
msgstr "你确定要从列表中删除所有的符号吗?"

#: symbolconfigunit.rsareyousureyouwanttoremovethissymbolfromthelist
msgid "Are you sure you want to remove this symbol from the list?"
msgstr "你确定要从列表中删除此符号吗?"

#: symbolhandler.rsalreadyexists
msgid "already exists"
msgstr "已存在"

#: symbolhandler.rserrorallocatingmemory
msgid "Error allocating memory"
msgstr "分配内存时出错"

#: symbolhandler.rsfailuredeterminingwhatmeans
msgid "Failure determining what %s means"
msgstr "确定失败为 %s 方法"

#: symbolhandler.rspleaseprovideabiggersize
msgid "Please provide a bigger size"
msgstr "请提供更大的大小"

#: symbolhandler.rssymbolloaderthreadhascrashed
msgid "Symbolloaderthread has crashed"
msgstr "Symbolloaderthread 已经损坏"

#: symbolhandler.rsthesymbolnamedwaspreviouslydeclared
msgid "The symbol named %s was previously declared with a size of %s instead of %s. all scripts that use this memory must give the same size. Adjust the size, or delete the old alloc from the userdefined symbol list"
msgstr "符号名为 %s 是先前声明为 %s 大小而不是 %s. 使用当前内存的脚本必须定义为同样的大小. 调整大小或从用户定义符号列表中删除原分配"

#: symbolhandler.rsyoucantaddasymbolwithaddress0
msgid "You can't add a symbol with address 0"
msgstr "你不能添加为 0 的符号与地址"

#: symbolhandler.rsyoucantchangethissettingatthemoment
msgid "You can't change this setting at the moment"
msgstr "你现在不能改变当前设置"

#: tableconverter.rstoooldtable
msgid "This table is too old to be used. Get Cheat engine 5.6 and open/resave this table"
msgstr "生成表单的版本过于陈旧. 只能转换 Cheat Engine 5.6 以上版本生成的表单"

#: tabout.button1.caption
msgctxt "tabout.button1.caption"
msgid "OK"
msgstr "确定"

#: tabout.button2.caption
msgid "Donate"
msgstr "捐赠"

#: tabout.button2.hint
msgid "If you like Cheat Engine and want to contribute something to the development, just send a donation using Paypal."
msgstr "如果你喜欢 Cheat Engine 并愿意为它的发展作出贡献，请使用贝宝为我们尽一份心意."

#: tabout.caption
msgid "About Cheat Engine"
msgstr "关于 Cheat Engine"

#: tabout.groupbox1.caption
msgctxt "tabout.groupbox1.caption"
msgid "Cheat Engine 7.0"
msgstr "Cheat Engine 7.0"

#: tabout.label1.caption
msgid "Made by: Dark Byte"
msgstr "作者:Dark Byte\r\n汉化:红尘旧梦i\nQQ：260909016"

#: tabout.label10.caption
msgid "Script engine powered by Lua"
msgstr "使用 Lua 脚本引擎"

#: tabout.label2.caption
msgid "Special thanks to:"
msgstr "特别感谢:"

#: tabout.label21.caption
msgctxt "TABOUT.LABEL21.CAPTION"
msgid "Metael"
msgstr "Metael"

#: tabout.label22.caption
msgctxt "TABOUT.LABEL22.CAPTION"
msgid "wh1t3y"
msgstr "wh1t3y"

#: tabout.label23.caption
msgctxt "TABOUT.LABEL23.CAPTION"
msgid "A. Wiseman"
msgstr "A. Wiseman"

#: tabout.label24.caption
msgctxt "TABOUT.LABEL24.CAPTION"
msgid "SWAT-Squad"
msgstr "SWAT-Squad"

#: tabout.label25.caption
msgctxt "TABOUT.LABEL25.CAPTION"
msgid "Psy"
msgstr "Psy"

#: tabout.label26.caption
msgctxt "TABOUT.LABEL26.CAPTION"
msgid "Kickclock"
msgstr "Kickclock"

#: tabout.label27.caption
msgctxt "TABOUT.LABEL27.CAPTION"
msgid "Recifense"
msgstr "Recifense"

#: tabout.label28.caption
msgctxt "TABOUT.LABEL28.CAPTION"
msgid "Geri"
msgstr "Geri"

#: tabout.label29.caption
msgctxt "TABOUT.LABEL29.CAPTION"
msgid "Emperor"
msgstr "Emperor"

#: tabout.label30.caption
msgctxt "TABOUT.LABEL30.CAPTION"
msgid "Jgoemat"
msgstr "Jgoemat"

#: tabout.label31.caption
msgctxt "TABOUT.LABEL31.CAPTION"
msgid "mgr.inz.Player"
msgstr "mgr.inz.Player"

#: tabout.label32.caption
msgctxt "TABOUT.LABEL32.CAPTION"
msgid "justa_dude"
msgstr "justa_dude"

#: tabout.label33.caption
msgctxt "TABOUT.LABEL33.CAPTION"
msgid "SER[G]ANT"
msgstr "SER[G]ANT"

#: tabout.label34.caption
msgctxt "TABOUT.LABEL34.CAPTION"
msgid "Atom0s"
msgstr "Atom0s"

#: tabout.label5.caption
msgid "And all the other people that helped me out"
msgstr "以及所有帮助过我的人"

#: tabout.label8.caption
msgid "Website"
msgstr "网站"

#: tabout.label9.caption
msgid "Message Board"
msgstr "留言板"

#: tabout.lbldbvm.caption
msgid "your system may or may not support dbvm"
msgstr "你的系统可能不支持DBVM"

#: tadvancedoptions.button1.caption
msgctxt "TADVANCEDOPTIONS.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tadvancedoptions.button2.caption
msgid "Button2"
msgstr "Button2"

#: tadvancedoptions.button3.caption
msgid "Button3"
msgstr "Button3"

#: tadvancedoptions.caption
msgctxt "tadvancedoptions.caption"
msgid "Advanced Options"
msgstr "高级选项"

#: tadvancedoptions.codelist2.columns[0].caption
msgctxt "tadvancedoptions.codelist2.columns[0].caption"
msgid "Address"
msgstr "地址"

#: tadvancedoptions.codelist2.columns[1].caption
msgctxt "tadvancedoptions.codelist2.columns[1].caption"
msgid "Name"
msgstr "名称"

#: tadvancedoptions.findoutwhatthiscodechanges1.caption
msgctxt "tadvancedoptions.findoutwhatthiscodechanges1.caption"
msgid "Find out what addresses this code writes to"
msgstr "找出代码改写的地址"

#: tadvancedoptions.label1.caption
msgid "Code list:"
msgstr "代码列表:"

#: tadvancedoptions.mireplacewithnops.caption
msgctxt "tadvancedoptions.mireplacewithnops.caption"
msgid "Replace with code that does nothing"
msgstr "使用空指令替换"

#: tadvancedoptions.mirestorewithoriginal.caption
msgctxt "tadvancedoptions.mirestorewithoriginal.caption"
msgid "Restore with original code"
msgstr "还原代码"

#: tadvancedoptions.n1.caption
msgctxt "TADVANCEDOPTIONS.N1.CAPTION"
msgid "-"
msgstr "-"

#: tadvancedoptions.n2.caption
msgctxt "tadvancedoptions.n2.caption"
msgid "-"
msgstr "-"

#: tadvancedoptions.n3.caption
msgctxt "TADVANCEDOPTIONS.N3.CAPTION"
msgid "-"
msgstr "-"

#: tadvancedoptions.opendialog1.title
msgid "Select the file you want to search"
msgstr "选择你想搜索的文件"

#: tadvancedoptions.openthedisassemblerhere1.caption
msgid "Open the disassembler at this location"
msgstr "查看此区域汇编代码"

#: tadvancedoptions.pausebutton.hint
msgctxt "tadvancedoptions.pausebutton.hint"
msgid "Pause the game"
msgstr "暂停游戏"

#: tadvancedoptions.remove1.caption
msgid "Remove from list"
msgstr "从列表中删除"

#: tadvancedoptions.rename1.caption
msgctxt "tadvancedoptions.rename1.caption"
msgid "Rename"
msgstr "重命名"

#: tadvancedoptions.replaceall1.caption
msgid "Replace all"
msgstr "全部替换"

#: tadvancedoptions.savebutton.hint
msgid "Create a standalone trainer"
msgstr "创建独立使用的修改器"

#: tchangeoffset.cancel.caption
msgctxt "TCHANGEOFFSET.CANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tchangeoffset.caption
msgctxt "tchangeoffset.caption"
msgid "Recalculate addres"
msgstr "重新计算地址"

#: tchangeoffset.cbhexadecimal.caption
msgctxt "tchangeoffset.cbhexadecimal.caption"
msgid "Hexadecimal"
msgstr "十六进制"

#: tchangeoffset.change.caption
msgid "Change"
msgstr "更改"

#: tcomments.button1.caption
msgctxt "tcomments.button1.caption"
msgid "Close"
msgstr "关闭"

#: tcomments.caption
msgctxt "tcomments.caption"
msgid "Comments"
msgstr "注释"

#: tcomments.tscomment.caption
msgctxt "TCOMMENTS.TSCOMMENT.CAPTION"
msgid "Comments"
msgstr "注释"

#: tfindwindow.btncancel.caption
msgctxt "TFINDWINDOW.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfindwindow.btnok.caption
msgctxt "TFINDWINDOW.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfindwindow.caption
msgctxt "TFINDWINDOW.CAPTION"
msgid "Find"
msgstr "查找"

#: tfindwindow.cbunicode.caption
msgctxt "tfindwindow.cbunicode.caption"
msgid "Unicode"
msgstr "Unicode"

#: tfindwindow.editstart.text
msgctxt "tfindwindow.editstart.text"
msgid "00400000"
msgstr "00400000"

#: tfindwindow.editstop.text
msgctxt "tfindwindow.editstop.text"
msgid "7FFFFFFF"
msgstr "7FFFFFFF"

#: tfindwindow.label2.caption
msgctxt "tfindwindow.label2.caption"
msgid "From"
msgstr "从"

#: tfindwindow.label3.caption
msgctxt "tfindwindow.label3.caption"
msgid "To"
msgstr "至"

#: tfindwindow.labelarray.caption
msgid "Array to scan"
msgstr "数组搜索"

#: tfindwindow.labeltype.caption
msgid "Type:"
msgstr "类型:"

#: tfindwindow.rbarbyte.caption
msgid "(Array of) byte"
msgstr "字节(数组)"

#: tfindwindow.rbtext.caption
msgctxt "TFINDWINDOW.RBTEXT.CAPTION"
msgid "Text"
msgstr "字符串"

#: tform1.caption
msgid "Form1"
msgstr "Form1"

#: tformaddresschange.btncancel.caption
msgctxt "TFORMADDRESSCHANGE.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tformaddresschange.btnok.caption
msgctxt "TFORMADDRESSCHANGE.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tformaddresschange.caption
msgctxt "tformaddresschange.caption"
msgid "Change address"
msgstr "更改地址"

#: tformaddresschange.cbcodepage.caption
msgctxt "TFORMADDRESSCHANGE.CBCODEPAGE.CAPTION"
msgid "Codepage"
msgstr "代码页"

#: tformaddresschange.cbpointer.caption
msgctxt "tformaddresschange.cbpointer.caption"
msgid "Pointer"
msgstr "指针"

#: tformaddresschange.cbunicode.caption
msgctxt "TFORMADDRESSCHANGE.CBUNICODE.CAPTION"
msgid "Unicode"
msgstr "Unicode"

#: tformaddresschange.cbvartype.text
msgctxt "tformaddresschange.cbvartype.text"
msgid "4 字节"
msgstr "4 字节"

#: tformaddresschange.edtsize.text
msgctxt "tformaddresschange.edtsize.text"
msgid "10"
msgstr "10"

#: tformaddresschange.label1.caption
msgctxt "tformaddresschange.label1.caption"
msgid "Address:"
msgstr "地址:"

#: tformaddresschange.label10.caption
msgctxt "tformaddresschange.label10.caption"
msgid "6"
msgstr "6"

#: tformaddresschange.label11.caption
msgctxt "tformaddresschange.label11.caption"
msgid "7"
msgstr "7"

#: tformaddresschange.label12.caption
msgctxt "tformaddresschange.label12.caption"
msgid "Type"
msgstr "类型"

#: tformaddresschange.label2.caption
msgid "Startbit:"
msgstr "起始位:"

#: tformaddresschange.label3.caption
msgctxt "tformaddresschange.label3.caption"
msgid "Description"
msgstr "描述"

#: tformaddresschange.label4.caption
msgctxt "TFORMADDRESSCHANGE.LABEL4.CAPTION"
msgid "0"
msgstr "0"

#: tformaddresschange.label5.caption
msgctxt "tformaddresschange.label5.caption"
msgid "1"
msgstr "1"

#: tformaddresschange.label6.caption
msgctxt "tformaddresschange.label6.caption"
msgid "2"
msgstr "2"

#: tformaddresschange.label7.caption
msgctxt "tformaddresschange.label7.caption"
msgid "3"
msgstr "3"

#: tformaddresschange.label8.caption
msgctxt "tformaddresschange.label8.caption"
msgid "4"
msgstr "4"

#: tformaddresschange.label9.caption
msgctxt "tformaddresschange.label9.caption"
msgid "5"
msgstr "5"

#: tformaddresschange.lblvalue.caption
msgid "=Value"
msgstr "=数值"

#: tformaddresschange.lengthlabel.caption
msgctxt "tformaddresschange.lengthlabel.caption"
msgid "Length"
msgstr "长度"

#: tformaddresschange.miupdateafterinterval.caption
msgctxt "TFORMADDRESSCHANGE.MIUPDATEAFTERINTERVAL.CAPTION"
msgid "Only update the offset after a specific amount of time"
msgstr "仅在特定时间之后更新偏移量"

#: tformaddresschange.miupdateonreinterpretonly.caption
msgctxt "TFORMADDRESSCHANGE.MIUPDATEONREINTERPRETONLY.CAPTION"
msgid "Only update the offset when the memory record gets reinterpreted"
msgstr "仅在内存被重新 reinterpreted 时才更新偏移量"

#: tformdebugstrings.button1.caption
msgctxt "TFORMDEBUGSTRINGS.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tformdebugstrings.caption
msgctxt "tformdebugstrings.caption"
msgid "Debug strings"
msgstr "调试字符串"

#: tformdesigner.calendar.caption
msgctxt "TFORMDESIGNER.CALENDAR.CAPTION"
msgid "Calendar"
msgstr "Calendar"

#: tformdesigner.calendar.hint
msgctxt "tformdesigner.calendar.hint"
msgid "Calendar"
msgstr "日历控件"

#: tformdesigner.caption
msgctxt "tformdesigner.caption"
msgid "Form Designer"
msgstr "窗体设计器"

#: tformdesigner.cebutton.caption
msgid "CEButton"
msgstr "CEButton"

#: tformdesigner.cebutton.hint
msgid "Button"
msgstr "按钮控件"

#: tformdesigner.cecheckbox.caption
msgid "CECheckbox"
msgstr "CECheckbox"

#: tformdesigner.cecheckbox.hint
msgid "CheckBox"
msgstr "复选框"

#: tformdesigner.cecombobox.caption
msgid "CEComboBox"
msgstr "CEComboBox"

#: tformdesigner.cecombobox.hint
msgid "ComboBox"
msgstr "组合框"

#: tformdesigner.ceedit.caption
msgid "CEEdit"
msgstr "CEEdit"

#: tformdesigner.ceedit.hint
msgctxt "tformdesigner.ceedit.hint"
msgid "Edit"
msgstr "编辑框"

#: tformdesigner.cegroupbox.caption
msgid "CEGroupBox"
msgstr "CEGroupBox"

#: tformdesigner.cegroupbox.hint
msgid "GroupBox"
msgstr "框架控件"

#: tformdesigner.ceimage.caption
msgid "CEImage"
msgstr "CEImage"

#: tformdesigner.ceimage.hint
msgctxt "tformdesigner.ceimage.hint"
msgid "Image"
msgstr "图像控件"

#: tformdesigner.celabel.caption
msgid "CELabel"
msgstr "CELabel"

#: tformdesigner.celabel.hint
msgid "Label"
msgstr "标签控件"

#: tformdesigner.celistbox.caption
msgid "CEListBox"
msgstr "CEListBox"

#: tformdesigner.celistbox.hint
msgid "Listbox"
msgstr "列表框"

#: tformdesigner.celistview.caption
msgid "CEListView"
msgstr "CEListView"

#: tformdesigner.celistview.hint
msgid "ListView"
msgstr "列表视图控件"

#: tformdesigner.cememo.caption
msgid "CEMemo"
msgstr "CEMemo"

#: tformdesigner.cememo.hint
msgid "Memo"
msgstr "Memo控件"

#: tformdesigner.ceopendialog.caption
msgid "CEOpenDialog"
msgstr "CEOpenDialog"

#: tformdesigner.ceopendialog.hint
msgid "Opendialog"
msgstr "打开对话框"

#: tformdesigner.cepagecontrol.caption
msgid "CEPageControl"
msgstr "CEPageControl"

#: tformdesigner.cepagecontrol.hint
msgid "PageControl"
msgstr "多页控件"

#: tformdesigner.cepanel.caption
msgid "CEPanel"
msgstr "CEPanel"

#: tformdesigner.cepanel.hint
msgid "Panel"
msgstr "面板控件"

#: tformdesigner.ceprogressbar.caption
msgid "CEProgressbar"
msgstr "CEProgressbar"

#: tformdesigner.ceprogressbar.hint
msgid "ProgressBar"
msgstr "进度条"

#: tformdesigner.ceradiogroup.caption
msgid "CERadioGroup"
msgstr "CERadioGroup"

#: tformdesigner.ceradiogroup.hint
msgid "RadioGroup"
msgstr "选项按钮组"

#: tformdesigner.cesavedialog.caption
msgid "CESavedialog"
msgstr "CESavedialog"

#: tformdesigner.cesavedialog.hint
msgid "Savedialog"
msgstr "保存对话框"

#: tformdesigner.cesplitter.caption
msgid "CESplitter"
msgstr "CESplitter"

#: tformdesigner.cesplitter.hint
msgid "Splitter"
msgstr "侧边栏控件"

#: tformdesigner.cetimer.caption
msgid "CETimer"
msgstr "CETimer"

#: tformdesigner.cetimer.hint
msgid "Timer"
msgstr "时钟控件"

#: tformdesigner.cetogglebox.caption
msgid "CEToggleBox"
msgstr "CEToggleBox"

#: tformdesigner.cetogglebox.hint
msgid "Togglebox"
msgstr "开关按钮"

#: tformdesigner.cetrackbar.caption
msgid "CETrackBar"
msgstr "CETrackBar"

#: tformdesigner.cetrackbar.hint
msgid "TrackBar"
msgstr "滑动块"

#: tformdesigner.cetreeview.caption
msgid "CETreeview"
msgstr "CETreeview"

#: tformdesigner.cetreeview.hint
msgid "Treeview"
msgstr "树状视图"

#: tformdesigner.finddialog.caption
msgctxt "TFORMDESIGNER.FINDDIALOG.CAPTION"
msgid "FindDialog"
msgstr "FindDialog"

#: tformdesigner.finddialog.hint
msgctxt "tformdesigner.finddialog.hint"
msgid "FindDialog"
msgstr "查找对话框"

#: tformdesigner.mainmenu.caption
msgctxt "TFORMDESIGNER.MAINMENU.CAPTION"
msgid "MainMenu"
msgstr "MainMenu"

#: tformdesigner.mainmenu.hint
msgctxt "tformdesigner.mainmenu.hint"
msgid "MainMenu"
msgstr "菜单栏"

#: tformdesigner.menuitem1.caption
msgctxt "tformdesigner.menuitem1.caption"
msgid "File"
msgstr "文件"

#: tformdesigner.miadditems.caption
msgid "Add Items"
msgstr "添加项目"

#: tformdesigner.miaddsubmenu.caption
msgid "Add sub menu items"
msgstr "添加项目的子菜单"

#: tformdesigner.miaddtab.caption
msgid "Add Tab"
msgstr "添加标签"

#: tformdesigner.mianchoreditor.caption
msgid "Anchor Editor"
msgstr "锚点编辑器"

#: tformdesigner.mibringtofront.caption
msgid "Bring to front"
msgstr "置于顶层"

#: tformdesigner.midelete.caption
msgctxt "TFORMDESIGNER.MIDELETE.CAPTION"
msgid "Delete"
msgstr "删除"

#: tformdesigner.miload.caption
msgctxt "tformdesigner.miload.caption"
msgid "Load"
msgstr "读取"

#: tformdesigner.miloadlfm.caption
msgid "Load LFM"
msgstr "载入 LFM"

#: tformdesigner.mimenumovedown.caption
msgid "Move down"
msgstr "下移"

#: tformdesigner.mimenumoveup.caption
msgid "Move up"
msgstr "上移"

#: tformdesigner.mimenusep.caption
msgctxt "TFORMDESIGNER.MIMENUSEP.CAPTION"
msgid "-"
msgstr "-"

#: tformdesigner.misave.caption
msgctxt "tformdesigner.misave.caption"
msgid "Save"
msgstr "保存"

#: tformdesigner.misavelfm.caption
msgid "Save LFM"
msgstr "保存 LFM"

#: tformdesigner.misendtoback.caption
msgid "Send to back"
msgstr "置于底层"

#: tformdesigner.misetupmainmenu.caption
msgid "Add menu items"
msgstr "添加菜单项目"

#: tformdesigner.noselection.caption
msgid "NoSelection"
msgstr "NoSelection"

#: tformdesigner.paintbox.caption
msgid "CEPaintBox"
msgstr "CEPaintBox"

#: tformdesigner.paintbox.hint
msgid "PaintBox"
msgstr "颜料盒"

#: tformdesigner.popupmenu.caption
msgctxt "TFORMDESIGNER.POPUPMENU.CAPTION"
msgid "PopupMenu"
msgstr "PopupMenu"

#: tformdesigner.popupmenu.hint
msgctxt "tformdesigner.popupmenu.hint"
msgid "PopupMenu"
msgstr "弹出菜单"

#: tformdesigner.radiobutton.caption
msgctxt "TFORMDESIGNER.RADIOBUTTON.CAPTION"
msgid "RadioButton"
msgstr "RadioButton"

#: tformdesigner.radiobutton.hint
msgctxt "tformdesigner.radiobutton.hint"
msgid "RadioButton"
msgstr "单选框"

#: tformdesigner.scrollbox.caption
msgctxt "TFORMDESIGNER.SCROLLBOX.CAPTION"
msgid "ScrollBox"
msgstr "ScrollBox"

#: tformdesigner.scrollbox.hint
msgctxt "tformdesigner.scrollbox.hint"
msgid "ScrollBox"
msgstr "滚动条"

#: tformdesigner.selectdirectorydialog.caption
msgctxt "TFORMDESIGNER.SELECTDIRECTORYDIALOG.CAPTION"
msgid "SelectDirectoryDialog"
msgstr "SelectDirectoryDialog"

#: tformdesigner.selectdirectorydialog.hint
msgctxt "tformdesigner.selectdirectorydialog.hint"
msgid "SelectDirectoryDialog"
msgstr "选择目录对话框"

#: tformdesigner.toolbar1.caption
msgid "ToolBar1"
msgstr "工具栏1"

#: tformdesigner.toolbutton1.caption
msgctxt "tformdesigner.toolbutton1.caption"
msgid "ToolButton1"
msgstr "工具按钮1"

#: tformdesigner.toolbutton6.caption
msgid "ToolButton6"
msgstr "工具按钮6"

#: tformdifferentbitsize.button1.caption
msgctxt "TFORMDIFFERENTBITSIZE.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tformdifferentbitsize.caption
msgid "Different sized row of bits"
msgstr "不同大小的行位"

#: tformdifferentbitsize.edit1.text
msgid "Edit1"
msgstr "Edit1"

#: tformdifferentbitsize.label1.caption
msgid "The last time you scanned the number of bits was 90 and now it is 12. Please tell me how and how much i must change the bit offset to successfully scan. (Left arrow+right arrow move the bits)"
msgstr "你最后一次扫描的比特数是90,现在是12. 请告诉我如何我要改变位偏移以成功扫描. (左箭头键/右箭头移动位)"

#: tformdifferentbitsize.labelnew.caption
msgid "LabelNew"
msgstr "LabelNew"

#: tformdifferentbitsize.labelold.caption
msgid "LabelOld"
msgstr "LabelOld"

#: tformdifferentbitsize.ohnoyoufoundme1.caption
msgid "Oh no! You found me!!!"
msgstr "哦不! 你找到我了!!!"

#: tformfoundcodelistextra.button1.caption
msgctxt "TFORMFOUNDCODELISTEXTRA.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tformfoundcodelistextra.caption
msgid "Extra info"
msgstr "详细信息"

#: tformfoundcodelistextra.copyaddresstoclipboard1.caption
msgid "Copy info to clipboard"
msgstr "复制信息到剪贴板"

#: tformfoundcodelistextra.copyguesstoclipboard1.caption
msgid "Copy easy guess to clipboard"
msgstr "复制简易猜测到剪贴板"

#: tformfoundcodelistextra.label1.caption
msgctxt "tformfoundcodelistextra.label1.caption"
msgid "Label1"
msgstr "Label1"

#: tformfoundcodelistextra.label10.caption
msgid ">>"
msgstr ">>"

#: tformfoundcodelistextra.label17.caption
msgid "yyy123"
msgstr "yyy123"

#: tformfoundcodelistextra.label18.caption
msgid "The registers shown here are AFTER the instruction has been executed"
msgstr "指令执行后寄存器的状态显示在这里"

#: tformfoundcodelistextra.label2.caption
msgid "Label2"
msgstr "Label2"

#: tformfoundcodelistextra.label3.caption
msgctxt "tformfoundcodelistextra.label3.caption"
msgid "Label3"
msgstr "Label3"

#: tformfoundcodelistextra.label4.caption
msgctxt "tformfoundcodelistextra.label4.caption"
msgid "Label4"
msgstr "Label4"

#: tformfoundcodelistextra.label5.caption
msgctxt "tformfoundcodelistextra.label5.caption"
msgid "Label5"
msgstr "Label5"

#: tformfoundcodelistextra.label6.caption
msgid "xxx123"
msgstr "xxx123"

#: tformfoundcodelistextra.lblrax.caption
msgid "EAX=DDDDDDDD"
msgstr "EAX=DDDDDDDD"

#: tformfoundcodelistextra.lblrbp.caption
msgid "EBP=DDDDDDDD"
msgstr "EBP=DDDDDDDD"

#: tformfoundcodelistextra.lblrbx.caption
msgid "EBX=DDDDDDDD"
msgstr "EBX=DDDDDDDD"

#: tformfoundcodelistextra.lblrcx.caption
msgid "ECX=DDDDDDDD"
msgstr "ECX=DDDDDDDD"

#: tformfoundcodelistextra.lblrdi.caption
msgid "EDI=DDDDDDDD"
msgstr "EDI=DDDDDDDD"

#: tformfoundcodelistextra.lblrdx.caption
msgid "EDX=DDDDDDDD"
msgstr "EDX=DDDDDDDD"

#: tformfoundcodelistextra.lblrip.caption
msgid "EIP=DDDDDDDD"
msgstr "EIP=DDDDDDDD"

#: tformfoundcodelistextra.lblrsi.caption
msgid "ESI=DDDDDDDD"
msgstr "ESI=DDDDDDDD"

#: tformfoundcodelistextra.lblrsp.caption
msgid "ESP=DDDDDDDD"
msgstr "ESP=DDDDDDDD"

#: tformfoundcodelistextra.sbshowfloats.caption
msgctxt "tformfoundcodelistextra.sbshowfloats.caption"
msgid "F"
msgstr "F"

#: tformfoundcodelistextra.sbshowfloats.hint
msgctxt "tformfoundcodelistextra.sbshowfloats.hint"
msgid "Floating point registers"
msgstr "浮点寄存器"

#: tformfoundcodelistextra.sbshowstack.caption
msgctxt "tformfoundcodelistextra.sbshowstack.caption"
msgid "S"
msgstr "S"

#: tformfoundcodelistextra.sbshowstack.hint
msgctxt "tformfoundcodelistextra.sbshowstack.hint"
msgid "Stack view"
msgstr "堆栈视图"

#: tformhotkey.button1.caption
msgctxt "TFORMHOTKEY.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tformhotkey.button2.caption
msgctxt "TFORMHOTKEY.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tformhotkey.caption
msgid "Change hotkey"
msgstr "更改热键"

#: tformhotkey.label1.caption
msgid "Press the new hotkey combination in below:"
msgstr "你按下的组合键如下:"

#: tformmemoryregions.button1.caption
msgctxt "TFORMMEMORYREGIONS.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tformmemoryregions.caption
msgctxt "tformmemoryregions.caption"
msgid "Memory regions"
msgstr "内存区域"

#: tformmemoryregions.listview1.columns[0].caption
msgctxt "TFORMMEMORYREGIONS.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tformmemoryregions.listview1.columns[1].caption
msgid "Allocation Protect"
msgstr "分配保护"

#: tformmemoryregions.listview1.columns[2].caption
msgid "State"
msgstr "状态"

#: tformmemoryregions.listview1.columns[3].caption
msgctxt "tformmemoryregions.listview1.columns[3].caption"
msgid "Protect"
msgstr "保护"

#: tformmemoryregions.listview1.columns[4].caption
msgctxt "TFORMMEMORYREGIONS.LISTVIEW1.COLUMNS[4].CAPTION"
msgid "Type"
msgstr "类型"

#: tformmemoryregions.listview1.columns[5].caption
msgctxt "tformmemoryregions.listview1.columns[5].caption"
msgid "Size"
msgstr "长度"

#: tformmemoryregions.listview1.columns[6].caption
msgctxt "tformmemoryregions.listview1.columns[6].caption"
msgid "Extra"
msgstr "其它"

#: tformmemoryregions.n1.caption
msgctxt "TFORMMEMORYREGIONS.N1.CAPTION"
msgid "-"
msgstr "-"

#: tformmemoryregions.savedialog1.title
msgid "Give the basename of the regions to be saved. (Can be a CT file)"
msgstr "命名并保存(建立 CT 文件)"

#: tformmemoryregions.saveselectedregions1.caption
msgid "Save selected regions"
msgstr "保存选择的区域"

#: tformmemoryregions.selectallreadablememory1.caption
msgid "Select all readable memory"
msgstr "选择全部可读内存"

#: tformmemoryregions.setselectedregionstobewritable1.caption
msgid "Set selected regions to be writable"
msgstr "将选择的区域设置为可写"

#: tformmemoryregions.statusbar1.panels[0].text
msgid "Hold ctrl when opening this window to also see kernelmode regions"
msgstr "当按住Ctrl打开反汇编窗口时可以看到内核模式区域"

#: tformpointerorpointee.button1.caption
msgid "Find what writes to this pointer"
msgstr "找出是什么改写了这个指针"

#: tformpointerorpointee.button2.caption
msgid "Find what writes to the address pointed at by this pointer"
msgstr "找出是什么改写了这个指针指向的地址"

#: tformpointerorpointee.caption
msgid "Cheat Engine Pointer"
msgstr "Cheat Engine 指针"

#: tformpointerorpointee.label1.caption
msgid "This is a pointer."
msgstr "这是一个指针."

#: tformscanning.caption
msgid "Scanning..."
msgstr "扫描中..."

#: tformsettings.aboutlabel.caption
msgid "About CE"
msgstr "关于 CE"

#: tformsettings.askforreplacewithnops.caption
msgid "Ask for replace with nop"
msgstr "当使用空指令替换时询问"

#: tformsettings.assembler.caption
msgctxt "tformsettings.assembler.caption"
msgid "Debugger Options"
msgstr "调试器选项"

#: tformsettings.btncancel.caption
msgctxt "TFORMSETTINGS.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tformsettings.btnexcludeprocesses.caption
msgid "More..."
msgstr "更多设置..."

#: tformsettings.btnok.caption
msgctxt "TFORMSETTINGS.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tformsettings.btnselectlanguage.caption
msgid "Select Language"
msgstr "选择语言"

#: tformsettings.btnsetfont.caption
msgctxt "TFORMSETTINGS.BTNSETFONT.CAPTION"
msgid "Set Font"
msgstr "设置字体"

#: tformsettings.btnsettoolshortcut.caption
msgid "Set shortcut"
msgstr "设置快捷键"

#: tformsettings.btntooldelete.caption
msgctxt "TFORMSETTINGS.BTNTOOLDELETE.CAPTION"
msgid "Delete"
msgstr "删除"

#: tformsettings.btntoolnew.caption
msgctxt "tformsettings.btntoolnew.caption"
msgid "New"
msgstr "新建"

#: tformsettings.button4.caption
msgid "Add new"
msgstr "添加"

#: tformsettings.button5.caption
msgctxt "TFORMSETTINGS.BUTTON5.CAPTION"
msgid "Delete"
msgstr "删除"

#: tformsettings.caption
msgid "Cheat Engine settings"
msgstr "Cheat Engine 设置"

#: tformsettings.cbalwaysaskforpassword.caption
msgid "Always ask for password when signing"
msgstr "当签署时始终询问密码"

#: tformsettings.cbalwaysautoattach.caption
msgid "Even autoattach when another process has already been selected"
msgstr "选定其它进程时自动附加"

#: tformsettings.cbalwayssigntable.caption
msgid "Always sign Cheat Tables when saving"
msgstr "始终在保存时签署Cheat Tables"

#: tformsettings.cbcanstepkernelcode.caption
msgid "Ability to step through kernel code"
msgstr "单步调试内核代码的能力"

#: tformsettings.cbcenteronpopup.caption
msgid "Center Cheat Engine when bringing to front"
msgstr "置前时 Cheat Engine 窗口居中"

#: tformsettings.cbdontopenhandle.caption
msgid "Do not try to obtain handles"
msgstr "不要尝试获取句柄"

#: tformsettings.cbdontusetempdir.caption
msgid "Don't store the temporary scanfiles in the windows tempdir"
msgstr "不要把扫描结果保存在 Windows 临时目录中"

#: tformsettings.cbdpiaware.caption
msgctxt "tformsettings.cbdpiaware.caption"
msgid "DPI Aware (Requires a restart)"
msgstr "DPI-Aware (需要重新启动)"

#: tformsettings.cbfastscan.caption
msgid "Fast scan on by default"
msgstr "默认开启快速扫描"

#: tformsettings.cbglobaldebug.caption
msgid "Use Global Debug routines"
msgstr "使用全局调试"

#: tformsettings.cbhandlebreakpoints.caption
msgid "Handle beakpoints not caused by CE"
msgstr "处理非CE引起的中断"

#: tformsettings.cbincremental.caption
msgctxt "TFORMSETTINGS.CBINCREMENTAL.CAPTION"
msgid "Incremental value"
msgstr "增加的数值"

#: tformsettings.cbinjectdllwithapc.caption
msgctxt "TFORMSETTINGS.CBINJECTDLLWITHAPC.CAPTION"
msgid "Use APC to inject dll's"
msgstr "使用 APC 注入dll"

#: tformsettings.cbkdebug.caption
msgid "Use kernelmode debugger"
msgstr "使用内核模式调试器"

#: tformsettings.cbkernelopenprocess.caption
msgctxt "tformsettings.cbkernelopenprocess.caption"
msgid "Open Process"
msgstr "打开进程"

#: tformsettings.cbkernelquerymemoryregion.caption
msgid "Query memory region routines"
msgstr "查询常规内存区"

#: tformsettings.cbkernelreadwriteprocessmemory.caption
msgid "Read/Write Process Memory  (Will cause slower scans)"
msgstr "读写进程内存  (会导致扫描速度减慢)"

#: tformsettings.cbmemimage.caption
msgid "MEM_IMAGE:Memory that is mapped into the view of an image section"
msgstr "MEM_IMAGE:即内存地址映射于可执行的镜像文件片段. (即被加载到内存的EXE和DLL等可执行文件)"

#: tformsettings.cbmemmapped.caption
msgid "MEM_MAPPED:Memory that is mapped into the view of a section. (E.g:File mapping, emulator memory, slow)"
msgstr "MEM_MAPPED:即内存地址映射于不可执行的镜像文件片段. (例如:页文件, 模拟器内存, 慢)"

#: tformsettings.cbmemprivate.caption
msgid "MEM_PRIVATE:Memory that is private."
msgstr "MEM_PRIVATE:即私有有内存."

#: tformsettings.cboldpointeraddmethod.caption
msgid "Pointer adding: Append pointerline instead of insert"
msgstr "添加指针:使用反向插入方式追加指针行"

#: tformsettings.cboverridedefaultfont.caption
msgctxt "TFORMSETTINGS.CBOVERRIDEDEFAULTFONT.CAPTION"
msgid "Override the default font (Requires a restart)"
msgstr "覆盖CE默认的字体设置 (需要重新启动)"

#: tformsettings.cboverrideexistingbps.caption
msgid "Override existing breakpoints when setting breakpoints"
msgstr "设置断点时覆盖现有的断点"

#: tformsettings.cbpausewhenscanningonbydefault.caption
msgid "Pause while scanning on by default"
msgstr "默认开启\"扫描时暂停游戏\""

#: tformsettings.cbprocessicons.caption
msgid "Get process icons for processlist"
msgstr "在进程列表中显示图标"

#: tformsettings.cbprocessiconsonly.caption
msgid "Only show processes with an icon"
msgstr "仅显示有图标的进程"

#: tformsettings.cbprocesswatcher.caption
msgid "Enable use of the Process Watcher"
msgstr "启用进程监视器"

#: tformsettings.cbprocesswatcheropenshandles.caption
msgctxt "TFORMSETTINGS.CBPROCESSWATCHEROPENSHANDLES.CAPTION"
msgid "Open handle to processes on creation"
msgstr "打开并处理创建的进程"

#: tformsettings.cbsavewindowpos.caption
msgid "Save window positions"
msgstr "保存窗口状态"

#: tformsettings.cbshowallwindows.caption
msgid "Show all windows in the taskbar"
msgstr "在任务栏中显示所有窗口"

#: tformsettings.cbshowassigned.caption
msgid "Show values as if they are signed"
msgstr "显示带符号的值"

#: tformsettings.cbshowlanguagemenuitem.caption
msgctxt "TFORMSETTINGS.CBSHOWLANGUAGEMENUITEM.CAPTION"
msgid "Show language selection menu item at the main screen"
msgstr "在主窗口的菜单栏显示\"语言\"菜单项"

#: tformsettings.cbshowmainmenu.caption
msgid "Show main menu"
msgstr "显示菜单栏"

#: tformsettings.cbshowprocesslist.caption
msgid "Show process list in main menu"
msgstr "在菜单栏上显示\"进程列表\""

#: tformsettings.cbshowtools.caption
msgid "Show 'tools' menu item"
msgstr "菜单栏上显示 \"工具\" 菜单项"

#: tformsettings.cbshowundo.caption
msgid "Show undo button"
msgstr "显示 \"撤销扫描\" 按钮"

#: tformsettings.cbsimplecopypaste.caption
msgid "Simple paste"
msgstr "简单粘贴"

#: tformsettings.cbskip_page_nocache.caption
msgid "Don't scan memory that is protected with the No Cache option"
msgstr "不扫描受保护的内存,也不缓存扫描数据"

#: tformsettings.cbskip_page_nocache.hint
msgid "Some systems crash when trying to read memory with this protection. If that happens check this option."
msgstr "如读取受保护的内存而引发系统崩溃, 勾选本项."

#: tformsettings.cbupdatefoundlist.caption
msgid "Update the list of found addresses even after scanning"
msgstr "扫描后平滑更新地址列表"

#: tformsettings.cbusevehdebugger.caption
msgid "Use VEH Debugger"
msgstr "使用 VEH 调试器"

#: tformsettings.cbusewindowsdebugger.caption
msgid "Use windows debugger"
msgstr "使用 Windows 调试器"

#: tformsettings.cbvehrealcontextonthreadcreation.caption
msgid "Get/Set thread context on creation"
msgstr "创建时获取/设置线程上下文"

#: tformsettings.cbwaitafterguiupdate.caption
msgid "Wait after gui updates"
msgstr "稍后再更新GUI"

#: tformsettings.cbwaitafterguiupdate.hint
msgid "Sometimes when using one of the \"find out what...\" functions they get hit so often that the gui has no time to update, resulting in anoying behaviour like certain windows not closing or unresponsive buttons."
msgstr "有时,当使用一个\"找出是什么...\"功能时,功能实现但偶尔GUI没时间更新就会导致烦人行为, 比如像某些窗口不关闭或按键无响应."

#: tformsettings.cbwriteloggingon.caption
msgid "Enable write logging by default"
msgstr "默认启用写入日志"

#: tformsettings.cbwriteloggingon.hint
msgid "When enabled CE will record writes done by the user and allows the user to undo those changes"
msgstr "当启用后,CE将记录用户已写入完成的记录并允许用户取消这些更改"

#: tformsettings.cgalltypes.caption
msgid "The all type includes"
msgstr "\"所有类型\"包括:"

#: tformsettings.checkbox1.caption
msgid "Try to prevent detection of the debugger"
msgstr "尽量避免调试器被发现"

#: tformsettings.checkbox4.caption
msgid "Use APC to create new threads"
msgstr "使用 APC 创建新的线程"

#: tformsettings.codefinder.caption
msgid "CodeFinder"
msgstr "CodeFinder"

#: tformsettings.combothreadpriority.text
msgctxt "tformsettings.combothreadpriority.text"
msgid "Higher"
msgstr "较高"

#: tformsettings.default1.caption
msgctxt "tformsettings.default1.caption"
msgid "Default"
msgstr "默认"

#: tformsettings.editautoattach.hint
msgid "Type the name of the process you want to automatically open. Note: Only works when NO process has been opened yet"
msgstr "输入想附加的进程名称. 注意: 进程已运行方能附加"

#: tformsettings.editbufsize.text
msgid "1024"
msgstr "1024"

#: tformsettings.editfreezeinterval.hint
msgid "The number of milliseconds that Cheat Engine will wait before resetting the frozen addresses to their original value."
msgstr "Cheat Engine 等候锁定的地址重置为初始值之间的间隔(毫秒)."

#: tformsettings.editfreezeinterval.text
msgctxt "tformsettings.editfreezeinterval.text"
msgid "100"
msgstr "100"

#: tformsettings.editupdatefoundinterval.hint
msgctxt "TFORMSETTINGS.EDITUPDATEFOUNDINTERVAL.HINT"
msgid "The number of milliseconds that Cheat Engine will wait before refreshing the list of found addresses"
msgstr "Cheat Engine 刷新地址列表间隔时间(毫秒)"

#: tformsettings.editupdatefoundinterval.text
msgctxt "tformsettings.editupdatefoundinterval.text"
msgid "1000"
msgstr "1000"

#: tformsettings.editupdateinterval.hint
msgctxt "tformsettings.editupdateinterval.hint"
msgid "The number of milliseconds that Cheat Engine will wait before refreshing the list of addresses"
msgstr "Cheat Engine 刷新地址列表间隔时间(毫秒)"

#: tformsettings.editupdateinterval.text
msgctxt "tformsettings.editupdateinterval.text"
msgid "500"
msgstr "500"

#: tformsettings.edtdefault.text
msgctxt "TFORMSETTINGS.EDTDEFAULT.TEXT"
msgid "0"
msgstr "0"

#: tformsettings.edtstacksize.text
msgctxt "tformsettings.edtstacksize.text"
msgid "4096"
msgstr "4096"

#: tformsettings.edtwritelogsize.text
msgctxt "TFORMSETTINGS.EDTWRITELOGSIZE.TEXT"
msgid "250"
msgstr "250"

#: tformsettings.extra.caption
msgctxt "TFORMSETTINGS.EXTRA.CAPTION"
msgid "Extra"
msgstr "其它"

#: tformsettings.generalsettings.caption
msgctxt "tformsettings.generalsettings.caption"
msgid "General Settings"
msgstr "常规设置"

#: tformsettings.groupbox1.caption
msgid "Address list specific"
msgstr "地址列表"

#: tformsettings.groupbox2.caption
msgid "Debugger interface config"
msgstr "调试器界面配置"

#: tformsettings.groupbox3.caption
msgid "Use the following CE Kernel routines instead of the original windows version"
msgstr "更改 CE 工作方式以适应早期版本的Windows"

#: tformsettings.groupbox4.caption
msgid "Prefered breakpoint method"
msgstr "首选的断点方式"

#: tformsettings.groupbox5.caption
msgid "Table: Lua scripts"
msgstr "表单: Lua 脚本"

#: tformsettings.label1.caption
msgid "Size of scanbuffer (KB) :"
msgstr "扫描缓冲区大小 (KB) :"

#: tformsettings.label10.caption
msgid "Current Language:"
msgstr "自定义语言:"

#: tformsettings.label11.caption
msgctxt "tformsettings.label11.caption"
msgid "ms"
msgstr "毫秒"

#: tformsettings.label12.caption
msgctxt "TFORMSETTINGS.LABEL12.CAPTION"
msgid "ms"
msgstr "毫秒"

#: tformsettings.label13.caption
msgctxt "tformsettings.label13.caption"
msgid "Update interval"
msgstr "更新间隔"

#: tformsettings.label14.caption
msgid "Freeze interval"
msgstr "锁定间隔"

#: tformsettings.label15.caption
msgid "(Can affect scan speed a lot)"
msgstr "(可能会影响扫描速度)"

#: tformsettings.label16.caption
msgctxt "TFORMSETTINGS.LABEL16.CAPTION"
msgid "When a table has a lua script, execute it:"
msgstr "当表单中含有Lua 脚本时执行以下操作:"

#: tformsettings.label18.caption
msgid "Found address list update interval"
msgstr "地址列表更新间隔"

#: tformsettings.label19.caption
msgctxt "TFORMSETTINGS.LABEL19.CAPTION"
msgid "ms"
msgstr "毫秒"

#: tformsettings.label2.caption
msgid "Store the temporary scanfiles here instead:"
msgstr "选择扫描结果的保存位置:"

#: tformsettings.label21.caption
msgid "Scan the following memory region types:"
msgstr "扫描以下内存类型:"

#: tformsettings.label22.caption
msgid "The following plugins are available:"
msgstr "可以使用的插件:"

#: tformsettings.label23.caption
msgid "Automatically attach to processes named"
msgstr "要自动附加的进程名称"

#: tformsettings.label24.caption
msgid "(Seperate entries with ; )"
msgstr "(使用 ; 分隔多个条目)"

#: tformsettings.label3.caption
msgid "Thread priority"
msgstr "线程优先级"

#: tformsettings.label4.caption
msgid "Single line assembler:"
msgstr "逐行汇编程序:"

#: tformsettings.label5.caption
msgctxt "TFORMSETTINGS.LABEL5.CAPTION"
msgid "Default return value"
msgstr "默认的返回值"

#: tformsettings.label6.caption
msgid "Size of stack to record on \"Find what... routines\""
msgstr "在\"找出...代码\"中记录堆栈的大小"

#: tformsettings.label7.caption
msgctxt "tformsettings.label7.caption"
msgid "字节"
msgstr "字节"

#: tformsettings.label8.caption
msgctxt "TFORMSETTINGS.LABEL8.CAPTION"
msgid "Max log size (nr of entries)"
msgstr "输入最大允许的日志大小"

#: tformsettings.label9.caption
msgid "The folowing languages are available:"
msgstr "可以使用的语言:"

#: tformsettings.languages.caption
msgctxt "tformsettings.languages.caption"
msgid "Languages"
msgstr "语言"

#: tformsettings.lblapplicationtool.caption
msgid "Application/Command"
msgstr "应用程序/命令"

#: tformsettings.lblcurrentlanguage.caption
msgctxt "TFORMSETTINGS.LBLCURRENTLANGUAGE.CAPTION"
msgid "English"
msgstr "英语"

#: tformsettings.lblshortcut.caption
msgid "Shortcut:"
msgstr "快捷键:"

#: tformsettings.lblshortcuttext.caption
msgid "xxxxxxxx"
msgstr "xxxxxxxx"

#: tformsettings.lblthreadfollowing.caption
msgid "Thread following:"
msgstr "以下线程:"

#: tformsettings.lbltoolsname.caption
msgctxt "TFORMSETTINGS.LBLTOOLSNAME.CAPTION"
msgid "Name"
msgstr "名称"

#: tformsettings.lvtools.columns[0].caption
msgctxt "TFORMSETTINGS.LVTOOLS.COLUMNS[0].CAPTION"
msgid "Name"
msgstr "名称"

#: tformsettings.lvtools.columns[1].caption
msgid "Application"
msgstr "应用程序"

#: tformsettings.lvtools.columns[2].caption
msgid "Shortcut"
msgstr "快捷键"

#: tformsettings.menuitem1.caption
msgctxt "TFORMSETTINGS.MENUITEM1.CAPTION"
msgid "Refresh"
msgstr "刷新"

#: tformsettings.miluaexecalways.caption
msgctxt "TFORMSETTINGS.MILUAEXECALWAYS.CAPTION"
msgid "Always"
msgstr "总是执行"

#: tformsettings.miluaexecask.caption
msgctxt "TFORMSETTINGS.MILUAEXECASK.CAPTION"
msgid "Always ask"
msgstr "始终询问"

#: tformsettings.miluaexecnever.caption
msgctxt "TFORMSETTINGS.MILUAEXECNEVER.CAPTION"
msgid "Never"
msgstr "从不执行"

#: tformsettings.miluaexecsignedonly.caption
msgctxt "TFORMSETTINGS.MILUAEXECSIGNEDONLY.CAPTION"
msgid "Only when signed, else ask"
msgstr "仅在有签署时询问"

#: tformsettings.plugins.caption
msgctxt "tformsettings.plugins.caption"
msgid "Plugins"
msgstr "插件"

#: tformsettings.rbdebugasbreakpoint.caption
msgid "Hardware Breakpoints (Max 4)"
msgstr "使用硬件断点 (最多4个)"

#: tformsettings.rbgdebuggerinterface.caption
msgid "Debugger method"
msgstr "调试方法"

#: tformsettings.rbint3asbreakpoint.caption
msgid "Int3 instructions (Execute BP only, falls back to hardware bp) (Unlimited)"
msgstr "使用Int3指令中断 (仅执行断点,回落到硬件断点) (无限制)"

#: tformsettings.rbpageexceptions.caption
msgid "Page exceptions (Extremely slow to unplayable, buggy, best used if nothing else works. Does not use debug registers)"
msgstr "页面异常 (如果其它断点方式不管用才建议使用. 但此方式极其缓慢、BUG超多以及不能使用调试寄存器)"

#: tformsettings.rbvehhookthreadcreation.caption
msgid "Hook thread-create and destroy api's"
msgstr "钩住线程创建和销毁的API"

#: tformsettings.rbvehpollthread.caption
msgid "Poll for threads"
msgstr "轮询线程"

#: tformsettings.rbvehuseprocesswatcher.caption
msgid "Use processwatcher"
msgstr "使用进程监视器"

#: tformsettings.replacewithnops.caption
msgid "Replace incomplete opcodes with nops"
msgstr "使用空指令替换多余的操作码"

#: tformsettings.replacewithnops.hint
msgid "If you type in a opcode and it is smaller than the opcode you replaced, it will fill the missing bytes with NOP instructions. If the opcode is longer it will replace the opcode(s) that have been overwritten with NOP's"
msgstr "如果输入的操作码比需要替换的操作码少, 将用空指令填补剩余的字节. 反之, 将操作码替换成之前的空指令"

#: tformsettings.scansettings.caption
msgctxt "tformsettings.scansettings.caption"
msgid "Scan Settings"
msgstr "扫描设置"

#: tformsettings.tauntoldosuser.caption
msgid "I really recommend upgrading to Windows 2000 or later mister Flintstone"
msgstr "强烈建议升级至 Win2000 或更高的 Windows 系统"

#: tformsettings.tshotkeys.caption
msgctxt "tformsettings.tshotkeys.caption"
msgid "Hotkeys"
msgstr "热键"

#: tformsettings.tskerneldebugconfig.caption
msgid "Kernel Debug"
msgstr "内核调试器"

#: tformsettings.tssigning.caption
msgid "tsSigning"
msgstr "tsSigning"

#: tformsettings.tstools.caption
msgid "tsTools"
msgstr "tsTools"

#: tformsettings.tsvehdebugconfig.caption
msgctxt "tformsettings.tsvehdebugconfig.caption"
msgid "VEH Debugger"
msgstr "VEH 调试器"

#: tformsettings.tswindowsdebuggerconfig.caption
msgid "Windows Debugger"
msgstr "Windows 调试器"

#: tformsettings.unrandomizer.caption
msgctxt "tformsettings.unrandomizer.caption"
msgid "Unrandomizer"
msgstr "禁止随机"

#: tfoundcodedialog.addtothecodelist1.caption
msgctxt "TFOUNDCODEDIALOG.ADDTOTHECODELIST1.CAPTION"
msgid "Add to the codelist"
msgstr "添加到代码表"

#: tfoundcodedialog.btnaddtocodelist.caption
msgctxt "tfoundcodedialog.btnaddtocodelist.caption"
msgid "Add to the codelist"
msgstr "添加到代码表"

#: tfoundcodedialog.btnaddtocodelist.hint
msgid "The selected addresses will be added to the code list in the advanced options window."
msgstr "所选地址将被添加到\"高级选项\"的代码列表内."

#: tfoundcodedialog.btnextrainfo.caption
msgid "More information"
msgstr "详细信息"

#: tfoundcodedialog.btnextrainfo.hint
msgid "Shows more information about the state of the system when that instruction got executed"
msgstr "当指令被执行后显示系统状态的更多信息"

#: tfoundcodedialog.btnok.caption
msgctxt "TFOUNDCODEDIALOG.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfoundcodedialog.btnopendisassembler.caption
msgid "Show disassembler"
msgstr "显示反汇编程序"

#: tfoundcodedialog.btnopendisassembler.hint
msgid "This will open the memoryview and bring the disassemblerview to the selected address"
msgstr "打开内存查看窗口并在反汇编窗口中显示选择的地址"

#: tfoundcodedialog.btnreplacewithnops.caption
msgctxt "tfoundcodedialog.btnreplacewithnops.caption"
msgid "Replace"
msgstr "替换"

#: tfoundcodedialog.btnreplacewithnops.hint
msgid "This will replace the selected addresses with code that does nothing. (Nops)"
msgstr "使用空指令(Nop)替换原有指令"

#: tfoundcodedialog.caption
msgctxt "tfoundcodedialog.caption"
msgid "The following opcodes changed the selected address"
msgstr "下列操作码更改了选择的地址"

#: tfoundcodedialog.copyselectiontoclipboard1.caption
msgctxt "tfoundcodedialog.copyselectiontoclipboard1.caption"
msgid "Copy selection to clipboard"
msgstr "将选中内容复制到剪贴板"

#: tfoundcodedialog.foundcodelist.columns[0].caption
msgctxt "TFOUNDCODEDIALOG.FOUNDCODELIST.COLUMNS[0].CAPTION"
msgid "Count"
msgstr "计数"

#: tfoundcodedialog.foundcodelist.columns[1].caption
msgctxt "tfoundcodedialog.foundcodelist.columns[1].caption"
msgid "Instruction"
msgstr "指令"

#: tfoundcodedialog.menuitem1.caption
msgctxt "tfoundcodedialog.menuitem1.caption"
msgid "Select all"
msgstr "全选"

#: tfoundcodedialog.menuitem2.caption
msgctxt "TFOUNDCODEDIALOG.MENUITEM2.CAPTION"
msgid "-"
msgstr "-"

#: tfoundcodedialog.mifindwhataccesses.caption
msgid "Check if found opcodes also access other addresses (max 8)"
msgstr "如果发现操作码也访问其他地址则检查(最多8个)"

#: tfoundcodedialog.misavetofile.caption
msgid "Save selection to file"
msgstr "选择文件保存"

#: tfoundcodedialog.moreinfo1.caption
msgctxt "tfoundcodedialog.moreinfo1.caption"
msgid "More Info"
msgstr "详细信息"

#: tfoundcodedialog.n1.caption
msgctxt "TFOUNDCODEDIALOG.N1.CAPTION"
msgid "-"
msgstr "-"

#: tfoundcodedialog.replacewithcodethatdoesnothingnop1.caption
msgid "Replace with code that does nothing (NOP)"
msgstr "使用空指令替换 (NOP)"

#: tfoundcodedialog.showthisaddressinthedisassembler1.caption
msgid "Show this address in the disassembler"
msgstr "在反汇编程序中显示地址"

#: tframehotkeyconfig.button3.caption
msgctxt "TFRAMEHOTKEYCONFIG.BUTTON3.CAPTION"
msgid "Clear"
msgstr "清除"

#: tframehotkeyconfig.cbstoponrelease.caption
msgid "Stop on release"
msgstr "停止释放"

#: tframehotkeyconfig.edit4.text
msgctxt "TFRAMEHOTKEYCONFIG.EDIT4.TEXT"
msgid "1"
msgstr "1"

#: tframehotkeyconfig.edthotkeydelay.hint
msgid "Lets you specify how quickly a hotkey is repeated"
msgstr "你可以指定一个热键让速度加快"

#: tframehotkeyconfig.edthotkeydelay.text
msgctxt "TFRAMEHOTKEYCONFIG.EDTHOTKEYDELAY.TEXT"
msgid "100"
msgstr "100"

#: tframehotkeyconfig.edtkeypollinterval.hint
msgid "Determines how quickly a hotkey keypress is detected"
msgstr "检测到一个按键按下后就加快速度"

#: tframehotkeyconfig.edtkeypollinterval.text
msgctxt "TFRAMEHOTKEYCONFIG.EDTKEYPOLLINTERVAL.TEXT"
msgid "100"
msgstr "100"

#: tframehotkeyconfig.edtshspeed.text
msgctxt "TFRAMEHOTKEYCONFIG.EDTSHSPEED.TEXT"
msgid "2"
msgstr "2"

#: tframehotkeyconfig.label1.caption
msgid "Functions"
msgstr "功能"

#: tframehotkeyconfig.label2.caption
msgctxt "tframehotkeyconfig.label2.caption"
msgid "Hotkey"
msgstr "热键"

#: tframehotkeyconfig.label3.caption
msgid "Speed delta"
msgstr "速度增量"

#: tframehotkeyconfig.label4.caption
msgid "Keypoll interval (milliseconds)"
msgstr "按键间隔(毫秒)"

#: tframehotkeyconfig.label5.caption
msgid "Delay between reactivating hotkeys"
msgstr "重新启用热键的延迟"

#: tframehotkeyconfig.label52.caption
msgctxt "tframehotkeyconfig.label52.caption"
msgid "Speed        "
msgstr "速度        "

#: tframehotkeyconfig.menuitem1.caption
msgctxt "TFRAMEHOTKEYCONFIG.MENUITEM1.CAPTION"
msgid "Clear list"
msgstr "清空列表"

#: tfrmaaeditprefs.btnfont.caption
msgid "font"
msgstr "字体"

#: tfrmaaeditprefs.button1.caption
msgctxt "TFRMAAEDITPREFS.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmaaeditprefs.button2.caption
msgctxt "TFRMAAEDITPREFS.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmaaeditprefs.caption
msgid "Editor preferences"
msgstr "编辑器参数"

#: tfrmaaeditprefs.cbshowgutter.caption
msgid "Show bar on left"
msgstr "左侧显示行号条"

#: tfrmaaeditprefs.cbshowlinenumbers.caption
msgid "Show line numbers"
msgstr "显示行号"

#: tfrmaaeditprefs.cbsmarttab.caption
msgid "Smart tab"
msgstr "智能标签"

#: tfrmaaeditprefs.cbtabstospace.caption
msgid "Tabs to space"
msgstr "制表符转为空格"

#: tfrmaaeditprefs.edttabwidth.text
msgctxt "TFRMAAEDITPREFS.EDTTABWIDTH.TEXT"
msgid "4"
msgstr "4"

#: tfrmaaeditprefs.label1.caption
msgid "Tab Width"
msgstr "标签宽度"

#: tfrmaccessedmemory.btnclearsmallsnapshot.caption
msgctxt "TFRMACCESSEDMEMORY.BTNCLEARSMALLSNAPSHOT.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmaccessedmemory.btnclearsmallsnapshot1.caption
msgctxt "TFRMACCESSEDMEMORY.BTNCLEARSMALLSNAPSHOT1.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmaccessedmemory.button1.caption
msgctxt "TFRMACCESSEDMEMORY.BUTTON1.CAPTION"
msgid "Set hotkeys"
msgstr "设置热键"

#: tfrmaccessedmemory.button2.caption
msgctxt "TFRMACCESSEDMEMORY.BUTTON2.CAPTION"
msgid "Start"
msgstr "开始"

#: tfrmaccessedmemory.button3.caption
msgctxt "TFRMACCESSEDMEMORY.BUTTON3.CAPTION"
msgid "Stop"
msgstr "停止"

#: tfrmaccessedmemory.caption
msgid "Find accessed memory regions"
msgstr "查找访问了的内存区域"

#: tfrmaccessedmemory.label1.caption
msgid "Hotkey to (re)start monitoring"
msgstr "\"开始监视\" 的热键"

#: tfrmaccessedmemory.label2.caption
msgid "This function will try to find out all accessed memory regions over a period of time and store it's results into an .mregion file which you can use with other functions that can make use of it (e.g pointerscan)"
msgstr "此功能将尝试查找所有访问了的内存区域一段时间并保存结果到 .mregion 文件内，然后你可以在使用其它功能时利用它 (例如:指针扫描器)"

#: tfrmaccessedmemory.label3.caption
msgid "Hotkey to stop monitoring"
msgstr "\"停止监视\" 热键"

#: tfrmaccessedmemory.listview1.columns[0].caption
msgctxt "TFRMACCESSEDMEMORY.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "From"
msgstr "从"

#: tfrmaccessedmemory.listview1.columns[1].caption
msgctxt "TFRMACCESSEDMEMORY.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "To"
msgstr "至"

#: tfrmaccessedmemory.menuitem1.caption
msgctxt "TFRMACCESSEDMEMORY.MENUITEM1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmaccessedmemory.menuitem2.caption
msgid "Open region file"
msgstr "打开区域文件"

#: tfrmaccessedmemory.menuitem3.caption
msgid "Save region file"
msgstr "保存区域文件"

#: tfrmaccessedmemory.menuitem4.caption
msgctxt "TFRMACCESSEDMEMORY.MENUITEM4.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmadconfig.caption
msgid "Support Cheat Engine"
msgstr "支持 Cheat Engine"

#: tfrmadconfig.cbcanclose.caption
msgid "Has header"
msgstr "是否带标题栏"

#: tfrmadconfig.cbownurl.caption
msgid "Show my own page"
msgstr "访问自己的网页"

#: tfrmadconfig.combobox1.text
msgid "468x60"
msgstr "468x60"

#: tfrmadconfig.edtheight.text
msgid "60"
msgstr "60"

#: tfrmadconfig.edtwidth.text
msgid "468"
msgstr "468"

#: tfrmadconfig.groupbox1.caption
msgctxt "tfrmadconfig.groupbox1.caption"
msgid "Position"
msgstr "位置"

#: tfrmadconfig.groupbox2.caption
msgid "Extra url"
msgstr "外部 URL"

#: tfrmadconfig.label1.caption
msgid "Thank you for supporting Cheat Engine. And to thank you for that I'll let you share in the generated traffic"
msgstr "感谢你支持 Cheat Engine. 为了表示感谢我会让你在分享后生成流量"

#: tfrmadconfig.label2.caption
msgid "Format"
msgstr "格式"

#: tfrmadconfig.label3.caption
msgid "x"
msgstr "x"

#: tfrmadconfig.lblextra.caption
msgid "Extra parameters"
msgstr "额外的参数"

#: tfrmadconfig.lblpercentage.caption
msgid "Percentage shown: 75%"
msgstr "显示比例: 75%"

#: tfrmadconfig.lblurl.caption
msgid "Url"
msgstr "Url"

#: tfrmaddtocodelist.button1.caption
msgctxt "TFRMADDTOCODELIST.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmaddtocodelist.button2.caption
msgctxt "TFRMADDTOCODELIST.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmaddtocodelist.caption
msgctxt "tfrmaddtocodelist.caption"
msgid "Region to add"
msgstr "添加区域"

#: tfrmaddtocodelist.edit1.text
msgid "DDDDDDDD"
msgstr "DDDDDDDD"

#: tfrmaddtocodelist.edit2.text
msgid "FFFFFFFF"
msgstr "FFFFFFFF"

#: tfrmaddtocodelist.label1.caption
msgctxt "TFRMADDTOCODELIST.LABEL1.CAPTION"
msgid "From"
msgstr "从"

#: tfrmaddtocodelist.label2.caption
msgctxt "TFRMADDTOCODELIST.LABEL2.CAPTION"
msgid "To"
msgstr "至"

#: tfrmapihooktemplatesettings.button1.caption
msgctxt "TFRMAPIHOOKTEMPLATESETTINGS.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmapihooktemplatesettings.button2.caption
msgctxt "tfrmapihooktemplatesettings.button2.caption"
msgid "Cancel"
msgstr "取消"

#: tfrmapihooktemplatesettings.caption
msgid "Api hook template"
msgstr "API Hook 模板"

#: tfrmapihooktemplatesettings.label1.caption
msgid "Original api address:"
msgstr "原始 API 地址:"

#: tfrmapihooktemplatesettings.label2.caption
msgid "New api address:"
msgstr "新的 API 地址:"

#: tfrmapihooktemplatesettings.label3.caption
msgid "Address where to store the new original api call:"
msgstr "存储新的原始 API 调用地址:"

#: tfrmapihooktemplatesettings.label4.caption
msgid "(optional, in case your dll uses a variable for the call)"
msgstr "(可选, 如果你的 dll 文件使用变量传递参数)"

#: tfrmapihooktemplatesettings.label5.caption
msgid "Note: The addresses can also be exportnames of dll's"
msgstr "注意: 这些地址也是 DLL 文件的输出名称"

#: tfrmassemblyscan.btnok.caption
msgctxt "tfrmassemblyscan.btnok.caption"
msgid "Scan"
msgstr "扫描"

#: tfrmassemblyscan.caption
msgctxt "tfrmassemblyscan.caption"
msgid "Assembly scan"
msgstr "汇编扫描"

#: tfrmassemblyscan.edtfrom.text
msgctxt "tfrmassemblyscan.edtfrom.text"
msgid "0000000000000000"
msgstr "0000000000000000"

#: tfrmassemblyscan.edtto.text
msgctxt "tfrmassemblyscan.edtto.text"
msgid "FFFFFFFFFFFFFFFF"
msgstr "FFFFFFFFFFFFFFFF"

#: tfrmassemblyscan.lblfrom.caption
msgctxt "TFRMASSEMBLYSCAN.LBLFROM.CAPTION"
msgid "From"
msgstr "从"

#: tfrmassemblyscan.lblinputassemblycode.caption
msgctxt "tfrmassemblyscan.lblinputassemblycode.caption"
msgid "Input the assembly code to find. Wildcards( * ) supported."
msgstr "输入要查找的汇编码. 支持通配符( * )."

#: tfrmassemblyscan.lblto.caption
msgctxt "TFRMASSEMBLYSCAN.LBLTO.CAPTION"
msgid "To"
msgstr "至"

#: tfrmautoinject.aapref1.caption
msgctxt "tfrmautoinject.aapref1.caption"
msgid "Preferences"
msgstr "首选项"

#: tfrmautoinject.apihook1.caption
msgid "API Hook"
msgstr "API 钩子"

#: tfrmautoinject.assigntocurrentcheattable1.caption
msgid "Assign to current cheat table"
msgstr "分配到当前的CT表"

#: tfrmautoinject.button1.caption
msgctxt "tfrmautoinject.button1.caption"
msgid "Execute"
msgstr "执行"

#: tfrmautoinject.caption
msgid "Auto assemble"
msgstr "自动汇编"

#: tfrmautoinject.cheattablecompliantcodee1.caption
msgid "Cheat Table framework code"
msgstr "CT表框架代码"

#: tfrmautoinject.close1.caption
msgctxt "TFRMAUTOINJECT.CLOSE1.CAPTION"
msgid "Close"
msgstr "关闭"

#: tfrmautoinject.codeinjection1.caption
msgid "Code injection"
msgstr "代码注入"

#: tfrmautoinject.coderelocation1.caption
msgid "Code relocation"
msgstr "代码重置"

#: tfrmautoinject.copy1.caption
msgctxt "tfrmautoinject.copy1.caption"
msgid "&Copy"
msgstr "复制(&C)"

#: tfrmautoinject.cut1.caption
msgctxt "tfrmautoinject.cut1.caption"
msgid "Cu&t"
msgstr "剪切(&T)"

#: tfrmautoinject.emplate1.caption
msgid "Template"
msgstr "模板"

#: tfrmautoinject.exit1.caption
msgid "Exit"
msgstr "退出"

#: tfrmautoinject.file1.caption
msgctxt "TFRMAUTOINJECT.FILE1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmautoinject.find1.caption
msgid "&Find..."
msgstr "查找(&F)..."

#: tfrmautoinject.inject1.caption
msgid "Inject"
msgstr "注入"

#: tfrmautoinject.injectincurrentprocess1.caption
msgid "Inject into current process"
msgstr "注入当前的进程"

#: tfrmautoinject.injectintocurrentprocessandexecute1.caption
msgid "Inject into current process and execute"
msgstr "注入当前的进程并执行"

#: tfrmautoinject.load1.caption
msgctxt "tfrmautoinject.load1.caption"
msgid "Open"
msgstr "打开"

#: tfrmautoinject.menuaobinjection.caption
msgid "AOB Injection"
msgstr "AOB 注入"

#: tfrmautoinject.menufullinjection.caption
msgid "Full Injection"
msgstr "全部注入"

#: tfrmautoinject.menuitem1.caption
msgctxt "TFRMAUTOINJECT.MENUITEM1.CAPTION"
msgid "Replace"
msgstr "替换"

#: tfrmautoinject.micalllua.caption
msgid "Call CE lua function"
msgstr "调用CE Lua函数"

#: tfrmautoinject.mifindnext.caption
msgctxt "tfrmautoinject.mifindnext.caption"
msgid "Find Next"
msgstr "查找下一个"

#: tfrmautoinject.minewwindow.caption
msgctxt "tfrmautoinject.minewwindow.caption"
msgid "New Window"
msgstr "新建窗口"

#: tfrmautoinject.n2.caption
msgctxt "TFRMAUTOINJECT.N2.CAPTION"
msgid "-"
msgstr "-"

#: tfrmautoinject.n6.caption
msgctxt "TFRMAUTOINJECT.N6.CAPTION"
msgid "-"
msgstr "-"

#: tfrmautoinject.new1.caption
msgid "New Tab"
msgstr "新建标签"

#: tfrmautoinject.opendialog1.title
msgctxt "tfrmautoinject.opendialog1.title"
msgid "Open CE assembly file"
msgstr "打开 CE 汇编文件"

#: tfrmautoinject.paste1.caption
msgctxt "tfrmautoinject.paste1.caption"
msgid "&Paste"
msgstr "粘贴(&P)"

#: tfrmautoinject.save1.caption
msgctxt "TFRMAUTOINJECT.SAVE1.CAPTION"
msgid "Save"
msgstr "保存"

#: tfrmautoinject.saveas1.caption
msgctxt "tfrmautoinject.saveas1.caption"
msgid "Save As..."
msgstr "另存为..."

#: tfrmautoinject.savedialog1.title
msgctxt "TFRMAUTOINJECT.SAVEDIALOG1.TITLE"
msgid "Open CE assembly file"
msgstr "打开 CE 汇编文件"

#: tfrmautoinject.syntaxhighlighting1.caption
msgid "Syntax highlighting"
msgstr "语法高亮"

#: tfrmautoinject.undo1.caption
msgctxt "tfrmautoinject.undo1.caption"
msgid "&Undo"
msgstr "撤销(&U)"

#: tfrmautoinject.view1.caption
msgctxt "tfrmautoinject.view1.caption"
msgid "View"
msgstr "视图"

#: tfrmbreakpointcondition.button1.caption
msgctxt "TFRMBREAKPOINTCONDITION.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmbreakpointcondition.button2.caption
msgctxt "TFRMBREAKPOINTCONDITION.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmbreakpointcondition.caption
msgid "Breakpoint condition"
msgstr "条件断点"

#: tfrmbreakpointcondition.label1.caption
msgid "Give a formula that must return true if you want the breakpoint to trigger a break"
msgstr "如果你需要在断点处触发中断,给出的规则的返回值必须为 true"

#: tfrmbreakpointcondition.label2.caption
msgid "Example: EAX == 0x1234  (case sensitive and hexadecimal starts with 0x )"
msgstr "例如: EAX == 0x1234  (区分大小写和十六进制以0x开头 )"

#: tfrmbreakpointcondition.rbcomplex.caption
msgid "Complex"
msgstr "复杂"

#: tfrmbreakpointcondition.rbeasy.caption
msgid "Easy"
msgstr "简易"

#: tfrmbreakpointcondition.tabsheet1.caption
msgid "Easy formula"
msgstr "简易的规则"

#: tfrmbreakpointcondition.tabsheet2.caption
msgid "Complex function"
msgstr "复杂的规则"

#: tfrmbreakpointlist.caption
msgid "Breakpoint list"
msgstr "断点列表"

#: tfrmbreakpointlist.listview1.columns[0].caption
msgctxt "TFRMBREAKPOINTLIST.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmbreakpointlist.listview1.columns[1].caption
msgctxt "TFRMBREAKPOINTLIST.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "Size"
msgstr "大小"

#: tfrmbreakpointlist.listview1.columns[2].caption
msgid "Trigger"
msgstr "触发条件"

#: tfrmbreakpointlist.listview1.columns[3].caption
msgctxt "TFRMBREAKPOINTLIST.LISTVIEW1.COLUMNS[3].CAPTION"
msgid "Type"
msgstr "类型"

#: tfrmbreakpointlist.listview1.columns[4].caption
msgid "On Hit"
msgstr "命中"

#: tfrmbreakpointlist.listview1.columns[5].caption
msgctxt "tfrmbreakpointlist.listview1.columns[5].caption"
msgid "Active"
msgstr "激活"

#: tfrmbreakpointlist.listview1.columns[6].caption
msgid "Pending deletion"
msgstr "等候删除"

#: tfrmbreakpointlist.menuitem1.caption
msgctxt "TFRMBREAKPOINTLIST.MENUITEM1.CAPTION"
msgid "-"
msgstr "-"

#: tfrmbreakpointlist.menuitem2.caption
msgid "Disable breakpoint"
msgstr "禁用断点"

#: tfrmbreakpointlist.midelbreakpoint.caption
msgid "Delete breakpoint"
msgstr "删除断点"

#: tfrmbreakpointlist.mipagewide.caption
msgid "Change to pagewide breakpoint"
msgstr "改变pagewide断点"

#: tfrmbreakpointlist.misetcondition.caption
msgid "Set/change condition"
msgstr "设置/更改条件"

#: tfrmbreakpointlist.mishowshadow.caption
msgid "Show shadow breakpoints"
msgstr "显示影子断点"

#: tfrmbreakthread.button1.caption
msgctxt "TFRMBREAKTHREAD.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmbreakthread.button2.caption
msgctxt "TFRMBREAKTHREAD.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmbreakthread.caption
msgid "Break Thread"
msgstr "中止线程"

#: tfrmbreakthread.label1.caption
msgid "This process has more than 1 thread. Select the thread you wish to break"
msgstr "此进程拥有多个线程，选择你想中止的线程"

#: tfrmcapturedtimers.button1.caption
msgctxt "TFRMCAPTUREDTIMERS.BUTTON1.CAPTION"
msgid "Close"
msgstr "关闭"

#: tfrmcapturedtimers.button2.caption
msgid "Send timer messages"
msgstr "发送计时器消息"

#: tfrmcapturedtimers.caption
msgid "Captured Timers"
msgstr "计时器捕捉"

#: tfrmcapturedtimers.edit1.text
msgctxt "TFRMCAPTUREDTIMERS.EDIT1.TEXT"
msgid "10"
msgstr "10"

#: tfrmcapturedtimers.label1.caption
msgid "The following WM_TIMER messages have been received"
msgstr "接收到下列 WM_TIMER 消息"

#: tfrmcapturedtimers.listview1.columns[0].caption
msgid "Window Handle"
msgstr "窗口句柄"

#: tfrmcapturedtimers.listview1.columns[1].caption
msgid "Window Title"
msgstr "窗口标题"

#: tfrmcapturedtimers.listview1.columns[2].caption
msgid "Window Class"
msgstr "窗口类"

#: tfrmcapturedtimers.listview1.columns[3].caption
msgid "Timer ID"
msgstr "计时器 ID"

#: tfrmcapturedtimers.listview1.columns[4].caption
msgid "TimerProc"
msgstr "TimerProc"

#: tfrmcapturedtimers.listview1.columns[5].caption
msgctxt "TFRMCAPTUREDTIMERS.LISTVIEW1.COLUMNS[5].CAPTION"
msgid "Count"
msgstr "计数"

#: tfrmcelistviewitemeditor.btnadditem.caption
msgctxt "tfrmcelistviewitemeditor.btnadditem.caption"
msgid "Add Item"
msgstr "添加项目"

#: tfrmcelistviewitemeditor.btnaddsubitem.caption
msgid "Add SubItem"
msgstr "添加子项"

#: tfrmcelistviewitemeditor.btncancel.caption
msgctxt "TFRMCELISTVIEWITEMEDITOR.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmcelistviewitemeditor.btndelete.caption
msgctxt "tfrmcelistviewitemeditor.btndelete.caption"
msgid "Delete"
msgstr "删除"

#: tfrmcelistviewitemeditor.btnok.caption
msgctxt "TFRMCELISTVIEWITEMEDITOR.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmcelistviewitemeditor.caption
msgid "Item editor"
msgstr "项目编辑器"

#: tfrmcelistviewitemeditor.lbltext.caption
msgctxt "tfrmcelistviewitemeditor.lbltext.caption"
msgid "Text"
msgstr "文本"

#: tfrmchangedaddresses.browsethismemoryregion1.caption
msgctxt "tfrmchangedaddresses.browsethismemoryregion1.caption"
msgid "Browse this memory region"
msgstr "浏览相关内存区域"

#: tfrmchangedaddresses.caption
msgid "Changed Addresses"
msgstr "改变的地址"

#: tfrmchangedaddresses.cbdisplaytype.text
msgctxt "TFRMCHANGEDADDRESSES.CBDISPLAYTYPE.TEXT"
msgid "4 字节"
msgstr "4 字节"

#: tfrmchangedaddresses.changedlist.columns[0].caption
msgctxt "TFRMCHANGEDADDRESSES.CHANGEDLIST.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmchangedaddresses.changedlist.columns[1].caption
msgctxt "tfrmchangedaddresses.changedlist.columns[1].caption"
msgid "Value"
msgstr "数值"

#: tfrmchangedaddresses.changedlist.columns[2].caption
msgctxt "tfrmchangedaddresses.changedlist.columns[2].caption"
msgid "Count"
msgstr "计数"

#: tfrmchangedaddresses.lblinfo.caption
msgid "The following addresses have been changed by the code you selected"
msgstr "你选择的代码更改了下列地址"

#: tfrmchangedaddresses.menuitem1.caption
msgid "Copy address to clipboard"
msgstr "将地址复制到剪贴板"

#: tfrmchangedaddresses.menuitem2.caption
msgctxt "TFRMCHANGEDADDRESSES.MENUITEM2.CAPTION"
msgid "-"
msgstr "-"

#: tfrmchangedaddresses.micbshowashexadecimal.caption
msgctxt "tfrmchangedaddresses.micbshowashexadecimal.caption"
msgid "Show as hexadecimal"
msgstr "以十六进制显示"

#: tfrmchangedaddresses.micopytoaddresslist.caption
msgid "Copy selected addresses to addresslist"
msgstr "复制选定的地址到地址列表"

#: tfrmchangedaddresses.mideleteselectedentries.caption
msgid "Delete selected entries"
msgstr "删除选定的条目"

#: tfrmchangedaddresses.midissect.caption
msgid "Open dissect data with selected addresses"
msgstr "打开选中地址的分析数据"

#: tfrmchangedaddresses.miresetcount.caption
msgid "Reset count"
msgstr "重置计数"

#: tfrmchangedaddresses.okbutton.caption
msgctxt "tfrmchangedaddresses.okbutton.caption"
msgid "Stop"
msgstr "停止"

#: tfrmchangedaddresses.showregisterstates1.caption
msgid "Show register states"
msgstr "显示寄存器状态"

#: tfrmcodecavescanner.btnstart.caption
msgctxt "TFRMCODECAVESCANNER.BTNSTART.CAPTION"
msgid "Start"
msgstr "开始"

#: tfrmcodecavescanner.caption
msgid "Scan for codecaves"
msgstr "扫描 codecaves"

#: tfrmcodecavescanner.cbnoexecute.caption
msgid "Also scan non-executable read-only memory"
msgstr "同时扫描只读内存"

#: tfrmcodecavescanner.copytoclipboard1.caption
msgid "Copy selection(s) to clipboard"
msgstr "复制被选中的内容到剪贴板"

#: tfrmcodecavescanner.editsize.text
msgid "12"
msgstr "12"

#: tfrmcodecavescanner.editstart.text
msgctxt "TFRMCODECAVESCANNER.EDITSTART.TEXT"
msgid "00400000"
msgstr "00400000"

#: tfrmcodecavescanner.editstop.text
msgctxt "TFRMCODECAVESCANNER.EDITSTOP.TEXT"
msgid "7FFFFFFF"
msgstr "7FFFFFFF"

#: tfrmcodecavescanner.label1.caption
msgid "Start Address"
msgstr "起始地址"

#: tfrmcodecavescanner.label2.caption
msgid "Stop Address"
msgstr "结束地址"

#: tfrmcodecavescanner.label3.caption
msgid "Size of cave"
msgstr "Cave 大小"

#: tfrmconfigunrandomizer.button1.caption
msgctxt "TFRMCONFIGUNRANDOMIZER.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmconfigunrandomizer.button2.caption
msgctxt "TFRMCONFIGUNRANDOMIZER.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmconfigunrandomizer.caption
msgid "Randomizer config"
msgstr "配置随机数生成器"

#: tfrmconfigunrandomizer.cbincremental.caption
msgctxt "tfrmconfigunrandomizer.cbincremental.caption"
msgid "Incremental value"
msgstr "增加的数值"

#: tfrmconfigunrandomizer.edtdefault.text
msgctxt "tfrmconfigunrandomizer.edtdefault.text"
msgid "0"
msgstr "0"

#: tfrmconfigunrandomizer.label1.caption
msgctxt "tfrmconfigunrandomizer.label1.caption"
msgid "Default return value"
msgstr "默认的返回值"

#: tfrmd3dhooksnapshotconfig.btnclearfullsnapshot.caption
msgctxt "TFRMD3DHOOKSNAPSHOTCONFIG.BTNCLEARFULLSNAPSHOT.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmd3dhooksnapshotconfig.btnclearsmallsnapshot.caption
msgctxt "TFRMD3DHOOKSNAPSHOTCONFIG.BTNCLEARSMALLSNAPSHOT.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmd3dhooksnapshotconfig.caption
msgid "D3DHook: Configure snapshot"
msgstr "D3DHook:配置快照"

#: tfrmd3dhooksnapshotconfig.cbalsooutputpng.caption
msgid "Also save picture next to snapshot file"
msgstr "紧接截图文件旁保存图片"

#: tfrmd3dhooksnapshotconfig.cbalsooutputpng.hint
msgid "Use this if you have a more efficient way of rendering .PNG's to select the snapshot you wish"
msgstr "请用.PNG格式保存你想要的截图. 除非你有更有效的渲染方式."

#: tfrmd3dhooksnapshotconfig.cbcleardepth.caption
msgid "Clear depth buffer"
msgstr "清除深度缓存"

#: tfrmd3dhooksnapshotconfig.cbprogressive.caption
msgid "Progressive snapshots (Do not clear background)"
msgstr "渐进式快照(背景不清除)"

#: tfrmd3dhooksnapshotconfig.edtfullsnapshot.hint
msgid "When this key is pressed all renders that occur will be stored"
msgstr "当此键被按下时,全部渲染图将被储存"

#: tfrmd3dhooksnapshotconfig.edtsmallsnapshot.hint
msgid "When this key is pressed all renders that occur below the mouse cursor will be stored"
msgstr "当此键被按下时,鼠标光标下面的全部渲染图将被储存"

#: tfrmd3dhooksnapshotconfig.label1.caption
msgid "Snapshot folder"
msgstr "截图文件夹"

#: tfrmd3dhooksnapshotconfig.label2.caption
msgid "Full snapshot key"
msgstr "捕获全屏键"

#: tfrmd3dhooksnapshotconfig.label3.caption
msgid "Focused snapshot key"
msgstr "捕获焦点窗口键"

#: tfrmd3dhooksnapshotconfig.mbcancel.caption
msgctxt "TFRMD3DHOOKSNAPSHOTCONFIG.MBCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmd3dhooksnapshotconfig.mbok.caption
msgctxt "TFRMD3DHOOKSNAPSHOTCONFIG.MBOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmd3dhooksnapshotconfig.rgpictureformat.caption
msgid "Picture format"
msgstr "图片格式"

#: tfrmd3dtrainergeneratoroptions.btnclear.caption
msgctxt "TFRMD3DTRAINERGENERATOROPTIONS.BTNCLEAR.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmd3dtrainergeneratoroptions.button2.caption
msgid "Set background picture"
msgstr "设置背景图片"

#: tfrmd3dtrainergeneratoroptions.button3.caption
msgctxt "tfrmd3dtrainergeneratoroptions.button3.caption"
msgid "Set Font"
msgstr "设置字体"

#: tfrmd3dtrainergeneratoroptions.button4.caption
msgctxt "TFRMD3DTRAINERGENERATOROPTIONS.BUTTON4.CAPTION"
msgid "Close"
msgstr "关闭"

#: tfrmd3dtrainergeneratoroptions.caption
msgid "Direct3D Hook Options"
msgstr "Direct3D 钩子选项"

#: tfrmd3dtrainergeneratoroptions.cballowdrag.caption
msgid "Allow user to drag trainer around"
msgstr "允许用户拖放修改器大小"

#: tfrmd3dtrainergeneratoroptions.cbhascheckbox.caption
msgid "Cheats have checkboxes"
msgstr "修改项有复选框"

#: tfrmd3dtrainergeneratoroptions.cbshowhotkeys.caption
msgid "Show hotkeys"
msgstr "显示热键"

#: tfrmd3dtrainergeneratoroptions.cbstretch.caption
msgid "Scale the background image"
msgstr "按比例缩小背景图"

#: tfrmd3dtrainergeneratoroptions.cbstretch.hint
msgid "If selected the background image will get resized so that cheat entries will fit inside it"
msgstr "选择的背景图片将重新调整大小以便修改项在其中更合适"

#: tfrmd3dtrainergeneratoroptions.cbused3dkeys.caption
msgid "Use button to hide/show trainer"
msgstr "使用按键\"隐藏/显示\"修改器"

#: tfrmd3dtrainergeneratoroptions.edtdistancebetweenlines.text
msgctxt "TFRMD3DTRAINERGENERATOROPTIONS.EDTDISTANCEBETWEENLINES.TEXT"
msgid "4"
msgstr "4"

#: tfrmd3dtrainergeneratoroptions.edtdistancefromborder.text
msgctxt "TFRMD3DTRAINERGENERATOROPTIONS.EDTDISTANCEFROMBORDER.TEXT"
msgid "8"
msgstr "8"

#: tfrmd3dtrainergeneratoroptions.edtdistancefromtop.text
msgctxt "tfrmd3dtrainergeneratoroptions.edtdistancefromtop.text"
msgid "8"
msgstr "8"

#: tfrmd3dtrainergeneratoroptions.label1.caption
msgid "Transparency"
msgstr "透明度"

#: tfrmd3dtrainergeneratoroptions.label2.caption
msgctxt "TFRMD3DTRAINERGENERATOROPTIONS.LABEL2.CAPTION"
msgid "Position"
msgstr "位置"

#: tfrmd3dtrainergeneratoroptions.label3.caption
msgid "Background picture:"
msgstr "背景图片:"

#: tfrmd3dtrainergeneratoroptions.label4.caption
msgid "Distance between lines in pixels"
msgstr "行间像素间距"

#: tfrmd3dtrainergeneratoroptions.label5.caption
msgid "Text distance from top"
msgstr "文本距离顶部长度"

#: tfrmd3dtrainergeneratoroptions.label6.caption
msgid "Text distance from border"
msgstr "文本距离边界长度"

#: tfrmd3dtrainergeneratoroptions.lbltextcolor.caption
msgid "Font text"
msgstr "文本字体"

#: tfrmd3dtrainergeneratoroptions.rbbottomleft.caption
msgid "Bottom left"
msgstr "左下"

#: tfrmd3dtrainergeneratoroptions.rbbottomright.caption
msgid "Bottom right"
msgstr "右下"

#: tfrmd3dtrainergeneratoroptions.rbcenter.caption
msgid "Center"
msgstr "居中"

#: tfrmd3dtrainergeneratoroptions.rbtopleft.caption
msgid "Top left"
msgstr "左上"

#: tfrmd3dtrainergeneratoroptions.rbtopright.caption
msgid "Top right"
msgstr "右上"

#: tfrmdbvmloadmanual.caption
msgid "Manual load DBVM"
msgstr "手动加载 DBVM"

#: tfrmdebugevents.button1.caption
msgctxt "TFRMDEBUGEVENTS.BUTTON1.CAPTION"
msgid "Close"
msgstr "关闭"

#: tfrmdebugevents.caption
msgid "Debug Events"
msgstr "调试事件"

#: tfrmdebugevents.menuitem1.caption
msgctxt "TFRMDEBUGEVENTS.MENUITEM1.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmdisassemblyscan.btncancel.caption
msgctxt "TFRMDISASSEMBLYSCAN.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmdisassemblyscan.caption
msgid "Assemblyscan"
msgstr "汇编扫描"

#: tfrmdisassemblyscan.label1.caption
msgid "                      "
msgstr "                      "

#: tfrmdisassemblyscan.menuitem1.caption
msgid "Go to this address"
msgstr "跳转到这个地址"

#: tfrmdisassemblyscan.menuitem2.caption
msgctxt "TFRMDISASSEMBLYSCAN.MENUITEM2.CAPTION"
msgid "Copy selection to clipboard"
msgstr "复制选择的内容到剪贴板"

#: tfrmdissectcode.btnstart.caption
msgctxt "tfrmdissectcode.btnstart.caption"
msgid "Start"
msgstr "开始"

#: tfrmdissectcode.caption
msgid "Dissect Code"
msgstr "分析代码"

#: tfrmdissectcode.cbincludesystemmodules.caption
msgctxt "tfrmdissectcode.cbincludesystemmodules.caption"
msgid "Include system modules"
msgstr "包括系统模块"

#: tfrmdissectcode.label1.caption
msgid "Maximum offset:"
msgstr "最大允许的偏移量:"

#: tfrmdissectcode.label11.caption
msgid "Calls:"
msgstr "调用:"

#: tfrmdissectcode.label2.caption
msgid "Select the module(s) to dissect"
msgstr "分析选择的模块"

#: tfrmdissectcode.label3.caption
msgid "Hold CTRL to select multiple"
msgstr "按住CTRL键选择多个项目"

#: tfrmdissectcode.label4.caption
msgid "String references found:"
msgstr "字符串参考:"

#: tfrmdissectcode.label5.caption
msgid "Conditional Jumps:"
msgstr "条件跳转:"

#: tfrmdissectcode.label6.caption
msgctxt "tfrmdissectcode.label6.caption"
msgid "Estimated time left:"
msgstr "剩余时间:"

#: tfrmdissectcode.label7.caption
msgid "00:00:00"
msgstr "00:00:00"

#: tfrmdissectcode.label9.caption
msgid "Unconditional Jumps:"
msgstr "无条件跳转:"

#: tfrmdissectcode.lblcalls.caption
msgctxt "TFRMDISSECTCODE.LBLCALLS.CAPTION"
msgid "0"
msgstr "0"

#: tfrmdissectcode.lblconditionaljumps.caption
msgctxt "TFRMDISSECTCODE.LBLCONDITIONALJUMPS.CAPTION"
msgid "0"
msgstr "0"

#: tfrmdissectcode.lblmaxoffset.caption
msgctxt "TFRMDISSECTCODE.LBLMAXOFFSET.CAPTION"
msgid "0"
msgstr "0"

#: tfrmdissectcode.lblstringref.caption
msgctxt "TFRMDISSECTCODE.LBLSTRINGREF.CAPTION"
msgid "0"
msgstr "0"

#: tfrmdissectcode.lblunconditionaljumps.caption
msgctxt "TFRMDISSECTCODE.LBLUNCONDITIONALJUMPS.CAPTION"
msgid "0"
msgstr "0"

#: tfrmdissectwindow.button1.caption
msgctxt "TFRMDISSECTWINDOW.BUTTON1.CAPTION"
msgid "More Info"
msgstr "更多信息"

#: tfrmdissectwindow.button2.caption
msgid "Toggle visible"
msgstr "切换可见"

#: tfrmdissectwindow.button3.caption
msgid "Close window"
msgstr "关闭窗口"

#: tfrmdissectwindow.button4.caption
msgid "Capture Timer Messages"
msgstr "捕获计时器消息"

#: tfrmdissectwindow.button5.caption
msgctxt "TFRMDISSECTWINDOW.BUTTON5.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmdissectwindow.button6.caption
msgid "Change text"
msgstr "更改文本"

#: tfrmdissectwindow.caption
msgctxt "tfrmdissectwindow.caption"
msgid "Dissect Windows"
msgstr "分析窗口"

#: tfrmdriverlist.button1.caption
msgctxt "TFRMDRIVERLIST.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmdriverlist.caption
msgctxt "tfrmdriverlist.caption"
msgid "Driver list"
msgstr "驱动列表"

#: tfrmdriverlist.find1.caption
msgctxt "tfrmdriverlist.find1.caption"
msgid "Find"
msgstr "查找"

#: tfrmdriverloaded.label1.caption
msgid "DBK64 LOADED"
msgstr "DBK64 LOADED"

#: tfrmedithistory.button1.caption
msgctxt "TFRMEDITHISTORY.BUTTON1.CAPTION"
msgid "Refresh"
msgstr "刷新"

#: tfrmedithistory.caption
msgid "Write log"
msgstr "写入日志"

#: tfrmedithistory.cblogwrites.caption
msgctxt "TFRMEDITHISTORY.CBLOGWRITES.CAPTION"
msgid "Enable write logging"
msgstr "开启写入日志"

#: tfrmedithistory.edtmaxwritelogsize.text
msgctxt "TFRMEDITHISTORY.EDTMAXWRITELOGSIZE.TEXT"
msgid "250"
msgstr "250"

#: tfrmedithistory.label1.caption
msgctxt "tfrmedithistory.label1.caption"
msgid "Max log size (nr of entries)"
msgstr "输入最大允许的日志大小"

#: tfrmedithistory.lvwritelog.columns[0].caption
msgctxt "TFRMEDITHISTORY.LVWRITELOG.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmedithistory.lvwritelog.columns[1].caption
msgctxt "TFRMEDITHISTORY.LVWRITELOG.COLUMNS[1].CAPTION"
msgid "Original bytes"
msgstr "原始字节"

#: tfrmedithistory.lvwritelog.columns[2].caption
msgctxt "TFRMEDITHISTORY.LVWRITELOG.COLUMNS[2].CAPTION"
msgid "New bytes"
msgstr "新的字节"

#: tfrmedithistory.miundo.caption
msgctxt "TFRMEDITHISTORY.MIUNDO.CAPTION"
msgid "Undo"
msgstr "撤销"

#: tfrmenumeratedlls.button1.caption
msgctxt "TFRMENUMERATEDLLS.BUTTON1.CAPTION"
msgid "Close"
msgstr "关闭"

#: tfrmenumeratedlls.button2.caption
msgid "Cancel Enum"
msgstr "取消枚举"

#: tfrmenumeratedlls.caption
msgid "Enumerate DLL's"
msgstr "枚举 DLL"

#: tfrmenumeratedlls.find.caption
msgctxt "TFRMENUMERATEDLLS.FIND.CAPTION"
msgid "Find"
msgstr "查找"

#: tfrmenumeratedlls.label2.caption
msgid "Symbols"
msgstr "符号表"

#: tfrmexcludehide.button1.caption
msgctxt "TFRMEXCLUDEHIDE.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmexcludehide.button2.caption
msgctxt "TFRMEXCLUDEHIDE.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmexcludehide.caption
msgid "Show/Hide settings"
msgstr "显示/隐藏 设置"

#: tfrmexcludehide.label1.caption
msgid "Select the processes you want to exclude from being hidden. Doubleclick the process to add it to the list. (Or remove it from the list)"
msgstr "要排除不打算隐藏的进程，双击进程添加到列表. (或从列表中移除)即可"

#: tfrmexcludehide.label2.caption
msgid "Current process list"
msgstr "当前进程列表"

#: tfrmexcludehide.label3.caption
msgid "List of processes that will not hide"
msgstr "不打算隐藏的进程"

#: tfrmexcludehide.label4.caption
msgid "Select the way cheat Engine hides/shows windows. (Will not work if a window that gets hidden or shown is not responding. E.g:Paused)"
msgstr "选择 Cheat Engine 隐藏/显示窗口的方式. (当窗口被隐藏或无响应时无法生效. 例如:暂停)"

#: tfrmexcludehide.radiobutton1.caption
msgid "Only hide/show the foreground window"
msgstr "仅仅隐藏/显示前台窗口"

#: tfrmexcludehide.radiobutton2.caption
msgid "Hide/show ALL windows"
msgstr "隐藏/显示所有窗口"

#: tfrmexetrainergenerator.btnaddfile.caption
msgid "Add file(s)"
msgstr "添加文件"

#: tfrmexetrainergenerator.btngeneratetrainer.caption
msgctxt "tfrmexetrainergenerator.btngeneratetrainer.caption"
msgid "Generate"
msgstr "制作"

#: tfrmexetrainergenerator.btnremovefile.caption
msgid "Remove file"
msgstr "删除文件"

#: tfrmexetrainergenerator.button1.caption
msgid "Change EXE Icon"
msgstr "更改 EXE 图标"

#: tfrmexetrainergenerator.button3.caption
msgid "Add folder"
msgstr "添加文件夹"

#: tfrmexetrainergenerator.caption
msgid "Exe Trainer Generator"
msgstr "制作 EXE 修改器"

#: tfrmexetrainergenerator.cbd3dhook.caption
msgid "D3DHook"
msgstr "D3DHook"

#: tfrmexetrainergenerator.cbdotnet.caption
msgid ".NET"
msgstr ".NET"

#: tfrmexetrainergenerator.cbgigantic.caption
msgid "Gigantic"
msgstr "巨大"

#: tfrmexetrainergenerator.cbgigantic.hint
msgid "Puts the Cheat Engine executable, dll's and other requirements into the trainer"
msgstr "放入 Cheat Engine 可执行档, DLL和其它需求到修改器中"

#: tfrmexetrainergenerator.cbkerneldebug.caption
msgid "Kernel Tools"
msgstr "内核工具"

#: tfrmexetrainergenerator.cbmodplayer.caption
msgid "Module Player"
msgstr "播放器模块"

#: tfrmexetrainergenerator.cbspeedhack.caption
msgid "Speedhack"
msgstr "变速"

#: tfrmexetrainergenerator.cbtiny.caption
msgid "Tiny"
msgstr "微型"

#: tfrmexetrainergenerator.cbtiny.hint
msgid "Only put the trainer data in the trainer. The user must have Cheat Engine installed to run this trainer"
msgstr "仅放入修改数据到修改器中，使用者必须安装 Cheat Engine 才能运行它"

#: tfrmexetrainergenerator.cbvehdebug.caption
msgctxt "TFRMEXETRAINERGENERATOR.CBVEHDEBUG.CAPTION"
msgid "VEH Debugger"
msgstr "VEH 调试器"

#: tfrmexetrainergenerator.combocompression.text
msgctxt "tfrmexetrainergenerator.combocompression.text"
msgid "Max"
msgstr "最大"

#: tfrmexetrainergenerator.groupbox1.caption
msgid "Process"
msgstr "进程"

#: tfrmexetrainergenerator.groupbox2.caption
msgid "Features used"
msgstr "其它功能"

#: tfrmexetrainergenerator.groupbox3.caption
msgid "Extra files"
msgstr "扩展文件"

#: tfrmexetrainergenerator.groupbox4.caption
msgid "Trainer size"
msgstr "修改器大小"

#: tfrmexetrainergenerator.label1.caption
msgctxt "tfrmexetrainergenerator.label1.caption"
msgid "Compression"
msgstr "压缩率"

#: tfrmexetrainergenerator.listview1.columns[0].caption
msgctxt "TFRMEXETRAINERGENERATOR.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "File"
msgstr "文件"

#: tfrmexetrainergenerator.listview1.columns[1].caption
msgid "Relative folder"
msgstr "相对文件夹"

#: tfrmexetrainergenerator.mieditfolder.caption
msgid "Edit folder"
msgstr "编辑文件夹"

#: tfrmexetrainergenerator.rb32.caption
msgid "Target process is 32-bit"
msgstr "目标进程是 32位"

#: tfrmexetrainergenerator.rb64.caption
msgid "Target process is 64-bit"
msgstr "目标进程是 64位"

#: tfrmfilepatcher.button1.caption
msgid "Patch"
msgstr "补丁"

#: tfrmfilepatcher.caption
msgid "File patcher"
msgstr "文件补丁"

#: tfrmfilepatcher.edtbaseaddress.text
msgctxt "TFRMFILEPATCHER.EDTBASEADDRESS.TEXT"
msgid "00400000"
msgstr "00400000"

#: tfrmfilepatcher.lblbaseaddress.caption
msgid "Base address (MZ header)"
msgstr "基地址 (MZ header)"

#: tfrmfilepatcher.lblfilename.caption
msgctxt "tfrmfilepatcher.lblfilename.caption"
msgid "Filename"
msgstr "文件名"

#: tfrmfilepatcher.rbauto.caption
msgid "Apply changes for specified module"
msgstr "指定的模块应用更改"

#: tfrmfilepatcher.rbmanual.caption
msgid "Manual"
msgstr "手动"

#: tfrmfillmemory.button1.caption
msgctxt "TFRMFILLMEMORY.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmfillmemory.button2.caption
msgctxt "TFRMFILLMEMORY.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmfillmemory.caption
msgid "Fill memory"
msgstr "填充内存"

#: tfrmfillmemory.edit3.text
msgctxt "TFRMFILLMEMORY.EDIT3.TEXT"
msgid "0"
msgstr "0"

#: tfrmfillmemory.label1.caption
msgctxt "TFRMFILLMEMORY.LABEL1.CAPTION"
msgid "From"
msgstr "从"

#: tfrmfillmemory.label2.caption
msgctxt "TFRMFILLMEMORY.LABEL2.CAPTION"
msgid "To"
msgstr "至"

#: tfrmfillmemory.label3.caption
msgid "Fill with"
msgstr "填充为"

#: tfrmfindstatics.button1.caption
msgctxt "tfrmfindstatics.button1.caption"
msgid "Stopping..."
msgstr "正在停止..."

#: tfrmfindstatics.caption
msgctxt "tfrmfindstatics.caption"
msgid "Find static addresses"
msgstr "查找静态地址"

#: tfrmfindstatics.checkbox1.caption
msgid "Only disassemble and check executable code"
msgstr "仅分析/检查可执行代码"

#: tfrmfindstatics.edit1.text
msgid "00401000"
msgstr "00401000"

#: tfrmfindstatics.edit2.text
msgid "00700000"
msgstr "00700000"

#: tfrmfindstatics.edit3.text
msgctxt "TFRMFINDSTATICS.EDIT3.TEXT"
msgid "00400000"
msgstr "00400000"

#: tfrmfindstatics.edit4.text
msgctxt "TFRMFINDSTATICS.EDIT4.TEXT"
msgid "7FFFFFFF"
msgstr "7FFFFFFF"

#: tfrmfindstatics.label1.caption
msgctxt "TFRMFINDSTATICS.LABEL1.CAPTION"
msgid "From"
msgstr "从"

#: tfrmfindstatics.label2.caption
msgctxt "tfrmfindstatics.label2.caption"
msgid "To:"
msgstr "至:"

#: tfrmfindstatics.label4.caption
msgid "Filter addresses"
msgstr "过滤地址"

#: tfrmfindstatics.label5.caption
msgctxt "TFRMFINDSTATICS.LABEL5.CAPTION"
msgid "From"
msgstr "从"

#: tfrmfindstatics.label6.caption
msgctxt "TFRMFINDSTATICS.LABEL6.CAPTION"
msgid "To:"
msgstr "至:"

#: tfrmfindstatics.listview1.columns[0].caption
msgctxt "TFRMFINDSTATICS.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmfindstatics.listview1.columns[1].caption
msgid "Pointer?"
msgstr "指针?"

#: tfrmfindstatics.listview1.columns[2].caption
msgid "Reference count"
msgstr "引用数"

#: tfrmfloatingpointpanel.caption
msgid "FPU"
msgstr "FPU"

#: tfrmfloatingpointpanel.combobox2.text
msgctxt "tfrmfloatingpointpanel.combobox2.text"
msgid "Float"
msgstr "单浮点"

#: tfrmfloatingpointpanel.combobox3.text
msgid "XMMRegisters"
msgstr "XMM寄存器"

#: tfrmfloatingpointpanel.tabsheet2.caption
msgctxt "tfrmfloatingpointpanel.tabsheet2.caption"
msgid "Extended"
msgstr "扩展"

#: tfrmgdtinfo.caption
msgid "GDT"
msgstr "GDT"

#: tfrmgnuassembler.caption
msgid "GNU Assembler+"
msgstr "GNU 汇编+"

#: tfrmgroupscanalgoritmgenerator.btncancel.caption
msgctxt "TFRMGROUPSCANALGORITMGENERATOR.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmgroupscanalgoritmgenerator.btnok.caption
msgctxt "TFRMGROUPSCANALGORITMGENERATOR.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmgroupscanalgoritmgenerator.caption
msgctxt "tfrmgroupscanalgoritmgenerator.caption"
msgid "Generate groupscan command"
msgstr "生成\"群组扫描\"参数"

#: tfrmgroupscanalgoritmgenerator.cboutoforder.caption
msgid "Out of order"
msgstr "次序颠倒"

#: tfrmgroupscanalgoritmgenerator.cbtypealigned.caption
msgid "Must be type-aligned"
msgstr "类型必须一致"

#: tfrmgroupscanalgoritmgenerator.edtblockalignment.text
msgctxt "TFRMGROUPSCANALGORITMGENERATOR.EDTBLOCKALIGNMENT.TEXT"
msgid "4"
msgstr "4"

#: tfrmgroupscanalgoritmgenerator.lblblockalignment.caption
msgid "Block alignment"
msgstr "区块对齐"

#: tfrmgroupscanalgoritmgenerator.lblblocksize.caption
msgid "Blocksize"
msgstr "区块大小"

#: tfrmgroupscanalgoritmgenerator.lblmin.caption
msgid "lblMin"
msgstr "lblMin"

#: tfrmgroupscanalgoritmgenerator.lblmustbedividable.caption
msgid "Must be dividable by 4"
msgstr "必须能被4整除"

#: tfrmgroupscanalgoritmgenerator.lblwildcardexplanation.caption
msgid "Leave a value empty or use * for wildcard"
msgstr "可留下一个空值或使用通配符 * "

#: tfrmheaps.button1.caption
msgctxt "TFRMHEAPS.BUTTON1.CAPTION"
msgid "Close"
msgstr "关闭"

#: tfrmheaps.caption
msgid "Current Heaplist"
msgstr "当前堆列表"

#: tfrmheaps.listview1.columns[0].caption
msgctxt "TFRMHEAPS.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmheaps.listview1.columns[1].caption
msgctxt "TFRMHEAPS.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "Size"
msgstr "大小"

#: tfrmhotkeyex.button1.caption
msgctxt "TFRMHOTKEYEX.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmhotkeyex.button2.caption
msgctxt "TFRMHOTKEYEX.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmhotkeyex.button3.caption
msgctxt "TFRMHOTKEYEX.BUTTON3.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmhotkeyex.caption
msgid "Set Hotkey"
msgstr "设置热键"

#: tfrmhotkeyex.label1.caption
msgid "Press the key combination you want to use"
msgstr "按下你想使用的按键组合"

#: tfrmidt.caption
msgid "IDT"
msgstr "IDT"

#: tfrmloadmemory.button1.caption
msgctxt "TFRMLOADMEMORY.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmloadmemory.button2.caption
msgctxt "TFRMLOADMEMORY.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmloadmemory.button3.caption
msgctxt "TFRMLOADMEMORY.BUTTON3.CAPTION"
msgid "Edit"
msgstr "编辑"

#: tfrmloadmemory.caption
msgid "Load Memory Region"
msgstr "载入内存区域"

#: tfrmloadmemory.label1.caption
msgctxt "TFRMLOADMEMORY.LABEL1.CAPTION"
msgid "Address:"
msgstr "地址:"

#: tfrmluaengine.btnexecute.caption
msgctxt "TFRMLUAENGINE.BTNEXECUTE.CAPTION"
msgid "Execute"
msgstr "执行"

#: tfrmluaengine.caption
msgctxt "tfrmluaengine.caption"
msgid "Lua Engine"
msgstr "Lua 引擎"

#: tfrmluaengine.cbshowonprint.caption
msgid "Show on \"print\""
msgstr "显示 \"打印\""

#: tfrmluaengine.groupbox1.caption
msgctxt "tfrmluaengine.groupbox1.caption"
msgid "Output"
msgstr "输出"

#: tfrmluaengine.menuitem1.caption
msgctxt "TFRMLUAENGINE.MENUITEM1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmluaengine.menuitem10.caption
msgctxt "tfrmluaengine.menuitem10.caption"
msgid "Undo"
msgstr "撤销"

#: tfrmluaengine.menuitem11.caption
msgctxt "tfrmluaengine.menuitem11.caption"
msgid "New window"
msgstr "新建窗口"

#: tfrmluaengine.menuitem12.caption
msgctxt "tfrmluaengine.menuitem12.caption"
msgid "Debug"
msgstr "调试"

#: tfrmluaengine.menuitem13.caption
msgctxt "TFRMLUAENGINE.MENUITEM13.CAPTION"
msgid "Find"
msgstr "查找"

#: tfrmluaengine.menuitem2.caption
msgid "Open script"
msgstr "打开脚本"

#: tfrmluaengine.menuitem3.caption
msgid "Save current script"
msgstr "保存当前的脚本"

#: tfrmluaengine.menuitem4.caption
msgctxt "TFRMLUAENGINE.MENUITEM4.CAPTION"
msgid "-"
msgstr "-"

#: tfrmluaengine.menuitem5.caption
msgid "Clear output"
msgstr "清除输出"

#: tfrmluaengine.menuitem6.caption
msgid "Find/Replace"
msgstr "查找/替换"

#: tfrmluaengine.menuitem7.caption
msgctxt "tfrmluaengine.menuitem7.caption"
msgid "Cut"
msgstr "剪切"

#: tfrmluaengine.menuitem8.caption
msgctxt "tfrmluaengine.menuitem8.caption"
msgid "Copy"
msgstr "复制"

#: tfrmluaengine.menuitem9.caption
msgctxt "tfrmluaengine.menuitem9.caption"
msgid "Paste"
msgstr "粘贴"

#: tfrmluaengine.miresizeoutput.caption
msgid "Resize output on resize"
msgstr "调整输出大小"

#: tfrmluaengine.mirun.caption
msgctxt "tfrmluaengine.mirun.caption"
msgid "Run"
msgstr "运行"

#: tfrmluaengine.misavecurrentscriptas.caption
msgid "Save current script as..."
msgstr "脚本另存为..."

#: tfrmluaengine.misetbreakpoint.caption
msgid "Set breakpoint"
msgstr "设置断点"

#: tfrmluaengine.mishowscriptinoutput.caption
msgid "Show script in output"
msgstr "显示脚本的输出"

#: tfrmluaengine.misinglestep.caption
msgid "Single step"
msgstr "单步执行"

#: tfrmluaengine.miview.caption
msgctxt "TFRMLUAENGINE.MIVIEW.CAPTION"
msgid "View"
msgstr "视图"

#: tfrmluaengine.tbdebug.caption
msgid "tbDebug"
msgstr "tbDebug"

#: tfrmluaengine.tbrun.caption
msgid "tbRun"
msgstr "tbRun"

#: tfrmluaengine.tbrun.hint
msgid "Continue"
msgstr "继续"

#: tfrmluaengine.tbsinglestep.caption
msgid "tbSingleStep"
msgstr "tbSingleStep"

#: tfrmluaengine.tbsinglestep.hint
msgid "Single Step"
msgstr "单步执行"

#: tfrmluaengine.tbstopdebug.caption
msgid "tbStopDebug"
msgstr "tbStopDebug"

#: tfrmluaengine.tbstopdebug.hint
msgctxt "TFRMLUAENGINE.TBSTOPDEBUG.HINT"
msgid "Stop"
msgstr "停止"

#: tfrmluaengine.toolbutton1.caption
msgctxt "TFRMLUAENGINE.TOOLBUTTON1.CAPTION"
msgid "ToolButton1"
msgstr "ToolButton1"

#: tfrmluascriptquestion.button1.caption
msgctxt "TFRMLUASCRIPTQUESTION.BUTTON1.CAPTION"
msgid "Yes"
msgstr "是"

#: tfrmluascriptquestion.button2.caption
msgctxt "TFRMLUASCRIPTQUESTION.BUTTON2.CAPTION"
msgid "No"
msgstr "否"

#: tfrmluascriptquestion.caption
msgid "Execute this lua script?"
msgstr "执行Lua脚本吗?"

#: tfrmluascriptquestion.groupbox5.caption
msgid "Future reference:"
msgstr "以便将来参考:"

#: tfrmluascriptquestion.label16.caption
msgctxt "tfrmluascriptquestion.label16.caption"
msgid "When a table has a lua script, execute it:"
msgstr "当表单中含有Lua 脚本时执行以下操作:"

#: tfrmluascriptquestion.rbalways.caption
msgctxt "tfrmluascriptquestion.rbalways.caption"
msgid "Always"
msgstr "总是执行"

#: tfrmluascriptquestion.rbalways.hint
msgid "Don't show this dialog anymore and always execute the scripts"
msgstr "不要再显示这个对话框并总是执行脚本"

#: tfrmluascriptquestion.rbalwaysask.caption
msgctxt "tfrmluascriptquestion.rbalwaysask.caption"
msgid "Always ask"
msgstr "始终询问"

#: tfrmluascriptquestion.rbalwaysask.hint
msgid "Always show this dialog, even if trusted"
msgstr "总是显示这个对话框 (即使脚本是可信任的)"

#: tfrmluascriptquestion.rbnever.caption
msgctxt "tfrmluascriptquestion.rbnever.caption"
msgid "Never"
msgstr "从不执行"

#: tfrmluascriptquestion.rbnever.hint
msgid "Don't show this dialog anymore and never execute the scripts"
msgstr "不要再显示这个对话框,也不要执行脚本"

#: tfrmluascriptquestion.rbsignedonly.caption
msgctxt "tfrmluascriptquestion.rbsignedonly.caption"
msgid "Only when signed, else ask"
msgstr "仅在有签署时询问"

#: tfrmluascriptquestion.rbsignedonly.hint
msgid "Only automatically execute lua scripts from trusted sources, else ask"
msgstr "仅执行来自可靠来源的Lua脚本"

#: tfrmmanualstacktraceconfig.btncancel.caption
msgctxt "TFRMMANUALSTACKTRACECONFIG.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmmanualstacktraceconfig.btnok.caption
msgctxt "TFRMMANUALSTACKTRACECONFIG.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmmanualstacktraceconfig.caption
msgid "Manual stacktrace config"
msgstr "手动堆栈跟踪配置"

#: tfrmmanualstacktraceconfig.cbuseshadow.caption
msgid "Use shadow copy"
msgstr "使用影子复制"

#: tfrmmanualstacktraceconfig.edtshadowsize.text
msgctxt "TFRMMANUALSTACKTRACECONFIG.EDTSHADOWSIZE.TEXT"
msgid "4096"
msgstr "4096"

#: tfrmmanualstacktraceconfig.label1.caption
msgid "Original base"
msgstr "原始基数"

#: tfrmmanualstacktraceconfig.label2.caption
msgid "->"
msgstr "->"

#: tfrmmanualstacktraceconfig.label3.caption
msgctxt "TFRMMANUALSTACKTRACECONFIG.LABEL3.CAPTION"
msgid "Size"
msgstr "大小"

#: tfrmmanualstacktraceconfig.label4.caption
msgid "New copy"
msgstr "新建复制"

#: tfrmmanualstacktraceconfig.lblebp.caption
msgctxt "TFRMMANUALSTACKTRACECONFIG.LBLEBP.CAPTION"
msgid "EBP"
msgstr "EBP"

#: tfrmmanualstacktraceconfig.lbleip.caption
msgctxt "TFRMMANUALSTACKTRACECONFIG.LBLEIP.CAPTION"
msgid "EIP"
msgstr "EIP"

#: tfrmmanualstacktraceconfig.lblesp.caption
msgctxt "TFRMMANUALSTACKTRACECONFIG.LBLESP.CAPTION"
msgid "ESP"
msgstr "ESP"

#: tfrmmemoryallochandler.btnreload.caption
msgid "Reload"
msgstr "重新加载"

#: tfrmmemoryallochandler.button1.caption
msgid "Check Address"
msgstr "检测地址"

#: tfrmmemoryallochandler.caption
msgid "Memory Allocations"
msgstr "内存分配"

#: tfrmmemoryallochandler.cbhookallocs.caption
msgid "Hook alloc functions"
msgstr "钩住分配函数"

#: tfrmmemoryallochandler.groupbox1.caption
msgid "Data"
msgstr "数据"

#: tfrmmemoryallochandler.label1.caption
msgid "Flags:"
msgstr "标志位:"

#: tfrmmemoryallochandler.label2.caption
msgid "Base Address:"
msgstr "基址:"

#: tfrmmemoryallochandler.label3.caption
msgid "HeapHandle"
msgstr "堆句柄"

#: tfrmmemoryallochandler.label4.caption
msgid "Size:"
msgstr "长度:"

#: tfrmmemoryallochandler.lblbaseaddress.caption
msgctxt "tfrmmemoryallochandler.lblbaseaddress.caption"
msgid "xxxx"
msgstr "xxxx"

#: tfrmmemoryallochandler.lblerr.caption
msgid "Couldn't find in heap. Found using the Alloc hook. (Not very useful)"
msgstr "在堆堆栈中未找到. 查找出正在使用的分配钩子. (没什么用)"

#: tfrmmemoryallochandler.lblflags.caption
msgctxt "TFRMMEMORYALLOCHANDLER.LBLFLAGS.CAPTION"
msgid "xxxx"
msgstr "xxxx"

#: tfrmmemoryallochandler.lblheaphandle.caption
msgctxt "TFRMMEMORYALLOCHANDLER.LBLHEAPHANDLE.CAPTION"
msgid "xxxx"
msgstr "xxxx"

#: tfrmmemoryallochandler.lblsize.caption
msgctxt "TFRMMEMORYALLOCHANDLER.LBLSIZE.CAPTION"
msgid "xxxx"
msgstr "xxxx"

#: tfrmmemoryallochandler.statusbar1.panels[0].text
msgctxt "TFRMMEMORYALLOCHANDLER.STATUSBAR1.PANELS[0].TEXT"
msgid "-"
msgstr "-"

#: tfrmmemoryallochandler.statusbar1.panels[1].text
msgid "--"
msgstr "--"

#: tfrmmemoryrecorddropdownsettings.btncancel.caption
msgctxt "TFRMMEMORYRECORDDROPDOWNSETTINGS.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmmemoryrecorddropdownsettings.btnok.caption
msgctxt "TFRMMEMORYRECORDDROPDOWNSETTINGS.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmmemoryrecorddropdownsettings.caption
msgid "Dropdown options"
msgstr "下拉菜单项"

#: tfrmmemoryrecorddropdownsettings.cbdisallowuserinput.caption
msgid "Disallow manual user input"
msgstr "禁止用户手动输入"

#: tfrmmemoryrecorddropdownsettings.cbdisplayasdropdownitem.caption
msgid "Make the record display values like the dropdown list"
msgstr "以下拉列表的形式显示数值"

#: tfrmmemoryrecorddropdownsettings.cbonlyshowdescription.caption
msgid "Only show the description part"
msgstr "只显示描述部分"

#: tfrmmemoryrecorddropdownsettings.label1.caption
msgid "Leave empty for no dropdown list"
msgstr "如无下拉列表就留空"

#: tfrmmemoryrecorddropdownsettings.label2.caption
msgid "Format:  Value:Description or (memrecdescription)"
msgstr "格式. 数值:描述 或 (memrecdescription)"

#: tfrmmemoryviewex.caption
msgid "Graphical Memory View "
msgstr "图形内存视图 "

#: tfrmmemoryviewex.cbaddresslist.text
msgctxt "tfrmmemoryviewex.cbaddresslist.text"
msgid "Current Memory"
msgstr "当前内存"

#: tfrmmemoryviewex.cbaddresslistonly.caption
msgid "Only show addresslist memory"
msgstr "只显示地址列表内存"

#: tfrmmemoryviewex.cbcolor.text
msgid "RGBA (4 字节/Pixel)"
msgstr "RGBA (4 字节/像素)"

#: tfrmmemoryviewex.cbcompare.caption
msgctxt "tfrmmemoryviewex.cbcompare.caption"
msgid "Compare against"
msgstr "和...相对比"

#: tfrmmemoryviewex.cbsavedlist.text
msgctxt "TFRMMEMORYVIEWEX.CBSAVEDLIST.TEXT"
msgid "Current Memory"
msgstr "当前内存"

#: tfrmmemoryviewex.edtpitch.hint
msgid "Number of bytes a row exists out"
msgstr "一行中可存字节数"

#: tfrmmemoryviewex.edtpitch.text
msgid "32"
msgstr "32"

#: tfrmmemoryviewex.label2.caption
msgid "Pixels per line"
msgstr "每行像素"

#: tfrmmemoryviewex.label3.caption
msgid "Color"
msgstr "颜色"

#: tfrmmemoryviewex.lbladdress.caption
msgctxt "TFRMMEMORYVIEWEX.LBLADDRESS.CAPTION"
msgid "Address:"
msgstr "地址:"

#: tfrmmemoryviewex.menuitem1.caption
msgctxt "TFRMMEMORYVIEWEX.MENUITEM1.CAPTION"
msgid "Change address"
msgstr "更改地址"

#: tfrmmemoryviewex.rband.caption
msgid "And"
msgstr "And"

#: tfrmmemoryviewex.rbor.caption
msgid "Or"
msgstr "Or"

#: tfrmmemoryviewex.rbxor.caption
msgid "Xor"
msgstr "Xor"

#: tfrmmemreccombobox.btncancel.caption
msgctxt "TFRMMEMRECCOMBOBOX.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmmemreccombobox.btnok.caption
msgctxt "TFRMMEMRECCOMBOBOX.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmmemreccombobox.caption
msgctxt "tfrmmemreccombobox.caption"
msgid "Change value"
msgstr "更改数值"

#: tfrmmemreccombobox.label1.caption
msgid "Change value to:"
msgstr "更改数值为:"

#: tfrmmemviewpreferences.btnfont.caption
msgid "Change disassembler font"
msgstr "更改反汇编器字体"

#: tfrmmemviewpreferences.btnhexfont.caption
msgid "Change hexview font"
msgstr "更改十六进制字体"

#: tfrmmemviewpreferences.button2.caption
msgctxt "tfrmmemviewpreferences.button2.caption"
msgid "Apply"
msgstr "应用"

#: tfrmmemviewpreferences.button3.caption
msgctxt "TFRMMEMVIEWPREFERENCES.BUTTON3.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmmemviewpreferences.caption
msgid "Disassembler Preferences"
msgstr "反汇编器首选项"

#: tfrmmemviewpreferences.cbcolorgroup.text
msgctxt "tfrmmemviewpreferences.cbcolorgroup.text"
msgid "Normal"
msgstr "标准"

#: tfrmmemviewpreferences.cbshowstatusbar.caption
msgid "Show statusbar"
msgstr "显示状态栏"

#: tfrmmemviewpreferences.edthexspacebetweenlines.text
msgctxt "TFRMMEMVIEWPREFERENCES.EDTHEXSPACEBETWEENLINES.TEXT"
msgid "0"
msgstr "0"

#: tfrmmemviewpreferences.edtjlspacing.text
msgctxt "TFRMMEMVIEWPREFERENCES.EDTJLSPACING.TEXT"
msgid "2"
msgstr "2"

#: tfrmmemviewpreferences.edtjlthickness.text
msgctxt "TFRMMEMVIEWPREFERENCES.EDTJLTHICKNESS.TEXT"
msgid "1"
msgstr "1"

#: tfrmmemviewpreferences.edtspaceabovelines.text
msgctxt "TFRMMEMVIEWPREFERENCES.EDTSPACEABOVELINES.TEXT"
msgid "0"
msgstr "0"

#: tfrmmemviewpreferences.edtspacebelowlines.text
msgctxt "TFRMMEMVIEWPREFERENCES.EDTSPACEBELOWLINES.TEXT"
msgid "0"
msgstr "0"

#: tfrmmemviewpreferences.groupbox1.caption
msgctxt "TFRMMEMVIEWPREFERENCES.GROUPBOX1.CAPTION"
msgid "Normal"
msgstr "标准"

#: tfrmmemviewpreferences.groupbox2.caption
msgctxt "TFRMMEMVIEWPREFERENCES.GROUPBOX2.CAPTION"
msgid "Disassembler"
msgstr "反汇编器"

#: tfrmmemviewpreferences.groupbox3.caption
msgctxt "TFRMMEMVIEWPREFERENCES.GROUPBOX3.CAPTION"
msgid "Hexview"
msgstr "十六进制"

#: tfrmmemviewpreferences.groupbox4.caption
msgctxt "TFRMMEMVIEWPREFERENCES.GROUPBOX4.CAPTION"
msgid "Jumplines"
msgstr "跳转线"

#: tfrmmemviewpreferences.groupbox5.caption
msgctxt "TFRMMEMVIEWPREFERENCES.GROUPBOX5.CAPTION"
msgid "Space between lines"
msgstr "行距"

#: tfrmmemviewpreferences.label3.caption
msgctxt "tfrmmemviewpreferences.label3.caption"
msgid "Space between lines"
msgstr "行距"

#: tfrmmemviewpreferences.label4.caption
msgid "Thickness"
msgstr "粗细"

#: tfrmmemviewpreferences.label5.caption
msgid "Spacing"
msgstr "间距"

#: tfrmmemviewpreferences.label6.caption
msgctxt "tfrmmemviewpreferences.label6.caption"
msgid "Above"
msgstr "以上"

#: tfrmmemviewpreferences.label7.caption
msgctxt "tfrmmemviewpreferences.label7.caption"
msgid "Below"
msgstr "以下"

#: tfrmmemviewpreferences.lblcall.caption
msgctxt "TFRMMEMVIEWPREFERENCES.LBLCALL.CAPTION"
msgid "Call color"
msgstr "Call 颜色"

#: tfrmmemviewpreferences.lblconditionaljump.caption
msgctxt "TFRMMEMVIEWPREFERENCES.LBLCONDITIONALJUMP.CAPTION"
msgid "Conditional jump color"
msgstr "条件跳转 颜色"

#: tfrmmemviewpreferences.lblhex.caption
msgctxt "tfrmmemviewpreferences.lblhex.caption"
msgid "Hexadecimal color"
msgstr "十六进制 颜色"

#: tfrmmemviewpreferences.lblhexexample.caption
msgid "Example"
msgstr "示例"

#: tfrmmemviewpreferences.lblnormal.caption
msgid "Default color"
msgstr "默认颜色"

#: tfrmmemviewpreferences.lblregister.caption
msgctxt "tfrmmemviewpreferences.lblregister.caption"
msgid "Register color"
msgstr "寄存器 颜色"

#: tfrmmemviewpreferences.lblsymbol.caption
msgctxt "tfrmmemviewpreferences.lblsymbol.caption"
msgid "Symbol color"
msgstr "符号 颜色"

#: tfrmmemviewpreferences.lblunconditionaljump.caption
msgctxt "TFRMMEMVIEWPREFERENCES.LBLUNCONDITIONALJUMP.CAPTION"
msgid "Unconditional jump color"
msgstr "无条件跳转 颜色"

#: tfrmmemviewpreferences.mirestoretodefaults.caption
msgid "Restore to defaults"
msgstr "还原为默认值"

#: tfrmmergepointerscanresultsettings.button1.caption
msgctxt "TFRMMERGEPOINTERSCANRESULTSETTINGS.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmmergepointerscanresultsettings.button2.caption
msgctxt "TFRMMERGEPOINTERSCANRESULTSETTINGS.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmmergepointerscanresultsettings.caption
msgctxt "tfrmmergepointerscanresultsettings.caption"
msgid "Merge pointerscan results"
msgstr "合并 指针扫描 的搜索结果"

#: tfrmmergepointerscanresultsettings.groupbox1.caption
msgctxt "TFRMMERGEPOINTERSCANRESULTSETTINGS.GROUPBOX1.CAPTION"
msgid "Description"
msgstr "描述"

#: tfrmmergepointerscanresultsettings.lbldescription.caption
msgctxt "TFRMMERGEPOINTERSCANRESULTSETTINGS.LBLDESCRIPTION.CAPTION"
msgid "Label1"
msgstr "Label1"

#: tfrmmergepointerscanresultsettings.rggroupmethod.caption
msgid "Group method"
msgstr "组方式"

#: tfrmmodifyregisters.button1.caption
msgctxt "TFRMMODIFYREGISTERS.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmmodifyregisters.button2.caption
msgctxt "TFRMMODIFYREGISTERS.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmmodifyregisters.caption
msgid "Modify register(s) at xxxxxxxx"
msgstr "修改寄存器 xxxxxxxx"

#: tfrmmodifyregisters.cbaf.caption
msgid "AF"
msgstr "AF"

#: tfrmmodifyregisters.cbcf.caption
msgid "CF"
msgstr "CF"

#: tfrmmodifyregisters.cbof.caption
msgid "OF"
msgstr "OF"

#: tfrmmodifyregisters.cbpf.caption
msgid "PF"
msgstr "PF"

#: tfrmmodifyregisters.cbsf.caption
msgid "SF"
msgstr "SF"

#: tfrmmodifyregisters.cbzf.caption
msgid "ZF"
msgstr "ZF"

#: tfrmmodifyregisters.label1.caption
msgid "EAX"
msgstr "EAX"

#: tfrmmodifyregisters.label16.caption
msgctxt "tfrmmodifyregisters.label16.caption"
msgid "Flags"
msgstr "标志位"

#: tfrmmodifyregisters.label17.caption
msgid "R8"
msgstr "R8"

#: tfrmmodifyregisters.label18.caption
msgid "R9"
msgstr "R9"

#: tfrmmodifyregisters.label19.caption
msgid "R10"
msgstr "R10"

#: tfrmmodifyregisters.label2.caption
msgid "EBX"
msgstr "EBX"

#: tfrmmodifyregisters.label20.caption
msgid "R11"
msgstr "R11"

#: tfrmmodifyregisters.label21.caption
msgid "R12"
msgstr "R12"

#: tfrmmodifyregisters.label22.caption
msgid "R13"
msgstr "R13"

#: tfrmmodifyregisters.label23.caption
msgid "R14"
msgstr "R14"

#: tfrmmodifyregisters.label24.caption
msgid "R15"
msgstr "R15"

#: tfrmmodifyregisters.label25.caption
msgid "Leave blank if you don't want to edit the register or flag"
msgstr "如果你不想编辑寄存器/标志位就留空"

#: tfrmmodifyregisters.label3.caption
msgid "ECX"
msgstr "ECX"

#: tfrmmodifyregisters.label4.caption
msgid "EDX"
msgstr "EDX"

#: tfrmmodifyregisters.label5.caption
msgid "ESI"
msgstr "ESI"

#: tfrmmodifyregisters.label6.caption
msgid "EDI"
msgstr "EDI"

#: tfrmmodifyregisters.label7.caption
msgctxt "tfrmmodifyregisters.label7.caption"
msgid "EBP"
msgstr "EBP"

#: tfrmmodifyregisters.label8.caption
msgctxt "tfrmmodifyregisters.label8.caption"
msgid "ESP"
msgstr "ESP"

#: tfrmmodifyregisters.label9.caption
msgctxt "tfrmmodifyregisters.label9.caption"
msgid "EIP"
msgstr "EIP"

#: tfrmmodulesafety.button1.caption
msgctxt "TFRMMODULESAFETY.BUTTON1.CAPTION"
msgid "Add"
msgstr "添加"

#: tfrmmodulesafety.button2.caption
msgctxt "TFRMMODULESAFETY.BUTTON2.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmmodulesafety.caption
msgid "Module safety"
msgstr "安全模块"

#: tfrmmodulesafety.cbglobaldeny.caption
msgid "Global"
msgstr "全局"

#: tfrmmodulesafety.rballowlist.caption
msgid "Prevent all modules from getting loaded except:"
msgstr "禁止所有模块被加载，除了:"

#: tfrmmodulesafety.rbdenylist.caption
msgid "Allow all modules to be loaded except:"
msgstr "允许所有模块被加载，除了:"

#: tfrmmodulesafety.remove1.caption
msgctxt "TFRMMODULESAFETY.REMOVE1.CAPTION"
msgid "Remove"
msgstr "移除"

#: tfrmmultilineinputquery.button1.caption
msgctxt "TFRMMULTILINEINPUTQUERY.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmmultilineinputquery.button2.caption
msgctxt "TFRMMULTILINEINPUTQUERY.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmmultilineinputquery.lblprompt.caption
msgctxt "tfrmmultilineinputquery.lblprompt.caption"
msgid "caption"
msgstr "标题"

#: tfrmnetworkconfig.btnconnect.caption
msgctxt "TFRMNETWORKCONFIG.BTNCONNECT.CAPTION"
msgid "Connect"
msgstr "连接"

#: tfrmnetworkconfig.button2.caption
msgctxt "TFRMNETWORKCONFIG.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmnetworkconfig.caption
msgid "Connect to server"
msgstr "连接到服务器"

#: tfrmnetworkconfig.edtport.text
msgid "52736"
msgstr "52736"

#: tfrmnetworkconfig.groupbox1.caption
msgid "Discovered servers"
msgstr "已发现的服务器"

#: tfrmnetworkconfig.label1.caption
msgctxt "TFRMNETWORKCONFIG.LABEL1.CAPTION"
msgid "Host"
msgstr "主机"

#: tfrmnetworkconfig.label2.caption
msgctxt "TFRMNETWORKCONFIG.LABEL2.CAPTION"
msgid "Port"
msgstr "端口"

#: tfrmnetworkconfig.listview1.columns[0].caption
msgctxt "tfrmnetworkconfig.listview1.columns[0].caption"
msgid "IP"
msgstr "IP"

#: tfrmnetworkconfig.listview1.columns[1].caption
msgctxt "TFRMNETWORKCONFIG.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "Port"
msgstr "端口"

#: tfrmnetworkconfig.menuitem1.caption
msgid "Refresh list"
msgstr "刷新列表"

#: tfrmnetworkdatacompression.caption
msgid "Compression level"
msgstr "压缩级别"

#: tfrmnetworkdatacompression.label1.caption
msgid "Change the compression level you wish to use when transferring/receiving memory."
msgstr "当传输/接收内存时更改你希望使用的压缩级别."

#: tfrmnetworkdatacompression.lblmaxcompression.caption
msgid "Max Compression"
msgstr "最大压缩"

#: tfrmnetworkdatacompression.lblnone.caption
msgid "No Compression"
msgstr "无压缩"

#: tfrmpaging.button1.caption
msgid "Go"
msgstr "转到"

#: tfrmpaging.caption
msgctxt "tfrmpaging.caption"
msgid "Paging"
msgstr "分页"

#: tfrmpaging.cb64bit.caption
msgctxt "tfrmpaging.cb64bit.caption"
msgid "64-bit"
msgstr "64 位"

#: tfrmpaging.cb8byteentries.caption
msgid "8-byte entries"
msgstr "8 字节项目"

#: tfrmpaging.label1.caption
msgid "Fill in the physical address of the paging base (or just cr3)"
msgstr "填写物理地址的页面基址 (或者是 cr3)"

#: tfrmpaging.menuitem1.caption
msgctxt "TFRMPAGING.MENUITEM1.CAPTION"
msgid "Find"
msgstr "查找"

#: tfrmpastetableentry.button1.caption
msgctxt "TFRMPASTETABLEENTRY.BUTTON1.CAPTION"
msgid "Paste"
msgstr "粘贴"

#: tfrmpastetableentry.button2.caption
msgctxt "TFRMPASTETABLEENTRY.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmpastetableentry.caption
msgid "Paste table entries"
msgstr "粘贴表单项目"

#: tfrmpastetableentry.edtoffset.text
msgctxt "TFRMPASTETABLEENTRY.EDTOFFSET.TEXT"
msgid "0"
msgstr "0"

#: tfrmpastetableentry.groupbox1.caption
msgctxt "TFRMPASTETABLEENTRY.GROUPBOX1.CAPTION"
msgid "Description"
msgstr "描述"

#: tfrmpastetableentry.groupbox2.caption
msgctxt "TFRMPASTETABLEENTRY.GROUPBOX2.CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmpastetableentry.label1.caption
msgid "Text to find:"
msgstr "全文查找:"

#: tfrmpastetableentry.label2.caption
msgid "Replace with:"
msgstr "替换为:"

#: tfrmpastetableentry.label3.caption
msgid "Adjust address by: "
msgstr "校正地址: "

#: tfrmpeinfo.button1.caption
msgctxt "TFRMPEINFO.BUTTON1.CAPTION"
msgid "Info"
msgstr "信息"

#: tfrmpeinfo.caption
msgid "Portable Executable (PE) Info"
msgstr "可移植的可执行文件(PE)信息"

#: tfrmpeinfo.groupbox1.caption
msgid "MZ-Start"
msgstr "MZ-Start"

#: tfrmpeinfo.groupbox2.caption
msgctxt "TFRMPEINFO.GROUPBOX2.CAPTION"
msgid "Info"
msgstr "信息"

#: tfrmpeinfo.label1.caption
msgid "No file opened"
msgstr "没有文件打开"

#: tfrmpeinfo.label2.caption
msgctxt "TFRMPEINFO.LABEL2.CAPTION"
msgid "address"
msgstr "地址"

#: tfrmpeinfo.micopyeverything.caption
msgid "Copy everything to clipboard"
msgstr "全部复制到剪贴板"

#: tfrmpeinfo.micopytab.caption
msgid "Copy tab to clipboard"
msgstr "复制标签到剪贴板"

#: tfrmpeinfo.radiobutton1.caption
msgctxt "TFRMPEINFO.RADIOBUTTON1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmpeinfo.radiobutton2.caption
msgid "Memory"
msgstr "内存"

#: tfrmpeinfo.tabsheet1.caption
msgctxt "TFRMPEINFO.TABSHEET1.CAPTION"
msgid "All"
msgstr "全部"

#: tfrmpeinfo.tabsheet2.caption
msgid "Imports"
msgstr "导入表"

#: tfrmpeinfo.tabsheet3.caption
msgid "Exports"
msgstr "导出表"

#: tfrmpeinfo.tabsheet4.caption
msgid "Base Relocations"
msgstr "基址重定位"

#: tfrmpointerrescanconnectdialog.btncancel.caption
msgctxt "TFRMPOINTERRESCANCONNECTDIALOG.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmpointerrescanconnectdialog.btnok.caption
msgctxt "tfrmpointerrescanconnectdialog.btnok.caption"
msgid "Connect"
msgstr "连接"

#: tfrmpointerrescanconnectdialog.caption
msgctxt "tfrmpointerrescanconnectdialog.caption"
msgid "Join rescan"
msgstr "连接后扫描"

#: tfrmpointerrescanconnectdialog.edthost.text
msgctxt "tfrmpointerrescanconnectdialog.edthost.text"
msgid "localhost"
msgstr "本地主机"

#: tfrmpointerrescanconnectdialog.edtport.text
msgctxt "tfrmpointerrescanconnectdialog.edtport.text"
msgid "52739"
msgstr "52739"

#: tfrmpointerrescanconnectdialog.lblhost.caption
msgctxt "tfrmpointerrescanconnectdialog.lblhost.caption"
msgid "Host"
msgstr "主机"

#: tfrmpointerrescanconnectdialog.lblport.caption
msgctxt "tfrmpointerrescanconnectdialog.lblport.caption"
msgid "Port"
msgstr "端口"

#: tfrmpointerscanconnectdialog.btncancel.caption
msgctxt "TFRMPOINTERSCANCONNECTDIALOG.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmpointerscanconnectdialog.btnok.caption
msgctxt "TFRMPOINTERSCANCONNECTDIALOG.BTNOK.CAPTION"
msgid "Connect"
msgstr "连接"

#: tfrmpointerscanconnectdialog.caption
msgid "Connect to active pointerscanner"
msgstr "连接到激活的指针扫描器"

#: tfrmpointerscanconnectdialog.cbpriority.text
msgctxt "TFRMPOINTERSCANCONNECTDIALOG.CBPRIORITY.TEXT"
msgid "Normal"
msgstr "标准"

#: tfrmpointerscanconnectdialog.cbuseloadedpointermap.caption
msgctxt "tfrmpointerscanconnectdialog.cbuseloadedpointermap.caption"
msgid "Use saved pointermap"
msgstr "使用保存的\"指针映射集\""

#: tfrmpointerscanconnectdialog.cbuseloadedpointermap.hint
msgid "If you use this option you can omit the downloading of the pointermap"
msgstr "如果你使用这个选项,你可以省略下载的\"指针映射集\""

#: tfrmpointerscanconnectdialog.edthost.text
msgctxt "TFRMPOINTERSCANCONNECTDIALOG.EDTHOST.TEXT"
msgid "localhost"
msgstr "本地主机"

#: tfrmpointerscanconnectdialog.edtport.text
msgctxt "tfrmpointerscanconnectdialog.edtport.text"
msgid "52737"
msgstr "52737"

#: tfrmpointerscanconnectdialog.edtthreadcount.text
msgctxt "TFRMPOINTERSCANCONNECTDIALOG.EDTTHREADCOUNT.TEXT"
msgid "2"
msgstr "2"

#: tfrmpointerscanconnectdialog.lblhost.caption
msgctxt "TFRMPOINTERSCANCONNECTDIALOG.LBLHOST.CAPTION"
msgid "Host"
msgstr "主机"

#: tfrmpointerscanconnectdialog.lblnrofthread.caption
msgctxt "tfrmpointerscanconnectdialog.lblnrofthread.caption"
msgid "Nr of threads scanning: "
msgstr "扫描线程数: "

#: tfrmpointerscanconnectdialog.lblport.caption
msgctxt "TFRMPOINTERSCANCONNECTDIALOG.LBLPORT.CAPTION"
msgid "Port"
msgstr "端口"

#: tfrmpointerscanconnectdialog.lblpriority.caption
msgctxt "tfrmpointerscanconnectdialog.lblpriority.caption"
msgid "Priority"
msgstr "优先级"

#: tfrmpointerscanner.btnconnect.caption
msgctxt "TFRMPOINTERSCANNER.BTNCONNECT.CAPTION"
msgid "Add to connection list"
msgstr "添加到连接列表"

#: tfrmpointerscanner.btndecreasethreadcount.caption
msgctxt "TFRMPOINTERSCANNER.BTNDECREASETHREADCOUNT.CAPTION"
msgid "Decrease worker count"
msgstr "减少线程数"

#: tfrmpointerscanner.btnincreasethreadcount.caption
msgctxt "TFRMPOINTERSCANNER.BTNINCREASETHREADCOUNT.CAPTION"
msgid "Increase worker count"
msgstr "增加线程数"

#: tfrmpointerscanner.btnstoprescanloop.caption
msgid "Stop rescan loop"
msgstr "停止循环重复扫描"

#: tfrmpointerscanner.btnstopscan.caption
msgctxt "TFRMPOINTERSCANNER.BTNSTOPSCAN.CAPTION"
msgid "Stop"
msgstr "停止"

#: tfrmpointerscanner.btnstopscan.hint
msgid "This will stop the current scan and show you the results it has found"
msgstr "停止当前的扫描并显示已扫描出的结果"

#: tfrmpointerscanner.caption
msgctxt "TFRMPOINTERSCANNER.CAPTION"
msgid "Pointer scan"
msgstr "指针扫描器"

#: tfrmpointerscanner.cbnonresponsive.caption
msgid "Non responsive connection"
msgstr "无响应的连接"

#: tfrmpointerscanner.cbpriority.text
msgctxt "TFRMPOINTERSCANNER.CBPRIORITY.TEXT"
msgid "Normal"
msgstr "标准"

#: tfrmpointerscanner.cbtestcrappyconnection.caption
msgctxt "TFRMPOINTERSCANNER.CBTESTCRAPPYCONNECTION.CAPTION"
msgid "Simulate no connection"
msgstr "模拟无连接"

#: tfrmpointerscanner.cbtrusted.caption
msgid "Trust stability"
msgstr "稳定性可靠"

#: tfrmpointerscanner.cbtrusted.hint
msgid "Trust that this server will not disappear during the scan and that the children it trusts itself won't do so either"
msgstr "此服务器与子节点绝不会失联"

#: tfrmpointerscanner.edtport.text
msgctxt "TFRMPOINTERSCANNER.EDTPORT.TEXT"
msgid "52737"
msgstr "52737"

#: tfrmpointerscanner.file1.caption
msgctxt "TFRMPOINTERSCANNER.FILE1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmpointerscanner.gbnetwork.caption
msgctxt "TFRMPOINTERSCANNER.GBNETWORK.CAPTION"
msgid "Network"
msgstr "联网"

#: tfrmpointerscanner.lblip.caption
msgctxt "TFRMPOINTERSCANNER.LBLIP.CAPTION"
msgid "IP"
msgstr "IP"

#: tfrmpointerscanner.lblpassword.caption
msgctxt "TFRMPOINTERSCANNER.LBLPASSWORD.CAPTION"
msgid "Password"
msgstr "密码"

#: tfrmpointerscanner.lblport.caption
msgctxt "TFRMPOINTERSCANNER.LBLPORT.CAPTION"
msgid "Port"
msgstr "端口"

#: tfrmpointerscanner.lblprogressbar1.caption
msgctxt "tfrmpointerscanner.lblprogressbar1.caption"
msgid "Generating pointermap"
msgstr "生成指针映射集"

#: tfrmpointerscanner.lblthreadpriority.caption
msgctxt "TFRMPOINTERSCANNER.LBLTHREADPRIORITY.CAPTION"
msgid "Scanner thread priority:"
msgstr "扫描线程优先级:"

#: tfrmpointerscanner.menuitem1.caption
msgid "Show modulelist"
msgstr "显示模块列表"

#: tfrmpointerscanner.menuitem2.caption
msgid "Distributed pointer scan"
msgstr "分布式指针扫描"

#: tfrmpointerscanner.method3fastspeedandaveragememoryusage1.caption
msgid "Scan for pointer"
msgstr "扫描指针"

#: tfrmpointerscanner.micreatepsnnode.caption
msgid "Setup as PSN worker and/or node"
msgstr "设置为PSN(Packet Switching Node)/节点"

#: tfrmpointerscanner.midisconnect.caption
msgid "Disconnect"
msgstr "断开"

#: tfrmpointerscanner.miexporttosqlite.caption
msgctxt "TFRMPOINTERSCANNER.MIEXPORTTOSQLITE.CAPTION"
msgid "Export to sqlite database"
msgstr "导出到SQLite数据库"

#: tfrmpointerscanner.miforcedisconnect.caption
msgid "Unsafe disconnect"
msgstr "断开不安全"

#: tfrmpointerscanner.miimportfromsqlite.caption
msgctxt "TFRMPOINTERSCANNER.MIIMPORTFROMSQLITE.CAPTION"
msgid "Import from sqlite database"
msgstr "从SQLite数据库导入"

#: tfrmpointerscanner.miresume.caption
msgid "Resume scan"
msgstr "继续扫描"

#: tfrmpointerscanner.n1.caption
msgctxt "TFRMPOINTERSCANNER.N1.CAPTION"
msgid "-"
msgstr "-"

#: tfrmpointerscanner.n2.caption
msgctxt "TFRMPOINTERSCANNER.N2.CAPTION"
msgid "-"
msgstr "-"

#: tfrmpointerscanner.new1.caption
msgctxt "TFRMPOINTERSCANNER.NEW1.CAPTION"
msgid "New"
msgstr "新建"

#: tfrmpointerscanner.open1.caption
msgctxt "TFRMPOINTERSCANNER.OPEN1.CAPTION"
msgid "Open"
msgstr "打开"

#: tfrmpointerscanner.pnldata.caption
msgid "pnlData"
msgstr "pnlData"

#: tfrmpointerscanner.pointerscanner1.caption
msgid "Pointer scanner"
msgstr "指针扫描器"

#: tfrmpointerscanner.rescanmemory1.caption
msgid "Rescan memory - Removes pointers not pointing to the right address"
msgstr "重新扫描内存 - 移除没有指向正确地址的指针"

#: tfrmpointerscanner.resyncmodulelist1.caption
msgid "Resync modulelist"
msgstr "重新同步模块列表"

#: tfrmpointerscanner.savedialog1.title
msgid "Specify the filename you want to store the results"
msgstr "为存储的结果指定名称"

#: tfrmpointerscanner.savedialog2.title
msgid "Specify the filename for the pointermap you're about to generate"
msgstr "指定你要生成的\"指针映射集\"的文件名"

#: tfrmpointerscannersettings.btncancel.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmpointerscannersettings.btnok.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmpointerscannersettings.caption
msgid "Pointerscanner scanoptions"
msgstr "\"指针扫描器\"选项"

#: tfrmpointerscannersettings.cbacceptnonmodulevtable.caption
msgid "Also accept non-module addresses"
msgstr "同时接受非模块地址"

#: tfrmpointerscannersettings.cballigned.caption
msgid "Addresses must be 32-bit alligned"
msgstr "地址必须是32位(4字节对齐)的"

#: tfrmpointerscannersettings.cballigned.hint
msgid "When enabled, only pointers that are stored in an address dividable by 4 are looked at"
msgstr "当启用后,只检视那些存储在一个可被4整除的地址中的指针"

#: tfrmpointerscannersettings.cballowruntimeworkers.caption
msgid "Allow scanners to connect at runtime"
msgstr "允许扫描器在运行时连接"

#: tfrmpointerscannersettings.cballowruntimeworkers.hint
msgctxt "TFRMPOINTERSCANNERSETTINGS.CBALLOWRUNTIMEWORKERS.HINT"
msgid "Opens a port that other systems running the pointerscanner can connect to and help out with the scan"
msgstr "打开一个端口,以便其它运行了\"指针扫描器\"的系统可以与其进行连接并辅助扫描"

#: tfrmpointerscannersettings.cbclasspointersonly.caption
msgid "First element of pointerstruct must point to module (e.g vtable)"
msgstr "指针结构的第一个元素必须指向模块(例如虚拟表)"

#: tfrmpointerscannersettings.cbclasspointersonly.hint
msgid "Object oriented programming languages tend to implement classobjects by having a pointer in the first element to something that describes the class"
msgstr "面向对象的编程语言往往通过一些在第一个元素中描述类的指针来实现类对象"

#: tfrmpointerscannersettings.cbcomparetootherpointermaps.caption
msgid "Compare results with other saved pointermap(s)"
msgstr "与其它保存的\"指针映射集\"结果相比对"

#: tfrmpointerscannersettings.cbcomparetootherpointermaps.hint
msgctxt "TFRMPOINTERSCANNERSETTINGS.CBCOMPARETOOTHERPOINTERMAPS.HINT"
msgid "When ticked you can add other pointermaps which will be used to verify that the pointers it finds are correct. You will have to fill in the correct address for each pointermap you provide."
msgstr "当勾选后，你可以添加用于验证所找到的指针是否正确的 ，但是你必须为每个\"指针映射集\"提供正确的地址."

#: tfrmpointerscannersettings.cbcompressedpointerscanfile.caption
msgid "Compressed pointerscan file"
msgstr "压缩\"指针扫描\"(.PTR)文件"

#: tfrmpointerscannersettings.cbcompressedpointerscanfile.hint
msgid "Compresses the generated .PTR files slightly so they take less space on the disk and less time writing to disk."
msgstr "勾选后,稍微压缩生成的 .PTR 文件(这样做能减少对磁盘空间的占用并用更少的时间写入硬盘)."

#: tfrmpointerscannersettings.cbconnecttonode.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.CBCONNECTTONODE.CAPTION"
msgid "Connect to pointerscan node"
msgstr "连接到\"指针扫描器\"节点"

#: tfrmpointerscannersettings.cbconnecttonode.hint
msgctxt "TFRMPOINTERSCANNERSETTINGS.CBCONNECTTONODE.HINT"
msgid "When checked will send a broadcast message on the local network which will tell pointer scanner systems to join this scan if they are set to auto join"
msgstr "如果设置为自动连接, 本地网络将在勾选后发送广播消息通知指针扫描器连接此次的扫描"

#: tfrmpointerscannersettings.cbheaponly.caption
msgid "Only allow static and heap addresses in the path"
msgstr "仅允许静态地址/堆地址在路径中"

#: tfrmpointerscannersettings.cbheaponly.hint
msgid "If the address you search for isn't a heap address, the scan will return 0 results"
msgstr "如果你要搜索的地址并不是堆地址, 扫描后会返回0结果"

#: tfrmpointerscannersettings.cbincludesystemmodules.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.CBINCLUDESYSTEMMODULES.CAPTION"
msgid "Include system modules"
msgstr "包括系统模块"

#: tfrmpointerscannersettings.cblimitscantoregionfile.caption
msgctxt "tfrmpointerscannersettings.cblimitscantoregionfile.caption"
msgid "Limit scan to specified region file"
msgstr "限制扫描指定的内存区域的文件"

#: tfrmpointerscannersettings.cbmaxoffsetspernode.caption
msgid "Max different offsets per node:"
msgstr "每个节点的偏移最大相差:"

#: tfrmpointerscannersettings.cbmaxoffsetspernode.hint
msgid "When the pointerscan looks through the list of pointers with a specific value, it goes through every single pointer that has that value. Every time increasing the offset slightly."
msgstr "当指针搜索检视拥有特定值的指针列表时,检视每一拥有该值的单个指针. 每次增加少许偏移."

#: tfrmpointerscannersettings.cbmustendwithspecificoffset.caption
msgid "Pointers must end with specific offsets"
msgstr "指针必须以特定的偏移结束"

#: tfrmpointerscannersettings.cbmuststartwithbase.caption
msgid "Base address must be in specific range"
msgstr "基础地址必须在指定范围内"

#: tfrmpointerscannersettings.cbmuststartwithbase.hint
msgid "Will only mark the given range as valid base address (Will reduce the number of results)"
msgstr "仅视给出范围内的为有效基址 (将减少结果数量)"

#: tfrmpointerscannersettings.cbnoloop.caption
msgid "No looping pointers"
msgstr "无循环指针"

#: tfrmpointerscannersettings.cbnoloop.hint
msgid "This will filter out pointerpaths that ended up in a loop"
msgstr "这将过滤掉在一个循环中结束的指针路径"

#: tfrmpointerscannersettings.cbnoreadonly.caption
msgid "Don't include pointers with read-only nodes"
msgstr "不包括只读节点的指针"

#: tfrmpointerscannersettings.cbnoreadonly.hint
msgid "When checked the pointerscan will throw away memory that is readonly. So when it looks for paths, it won't encounter paths that pass through read only memory blocks."
msgstr "当勾选后，\"指针扫描\"将会丢弃只读内存. 所以当它查找路径时,将不会遇到通往只读内存块的路径."

#: tfrmpointerscannersettings.cbonlyonestatic.caption
msgid "Stop traversing a path when a static has been found"
msgstr "找到一个静态的就停止遍历路径"

#: tfrmpointerscannersettings.cbonlyonestatic.hint
msgid "When the pointerscanner goes through the list of pointervalues with a specific value, this will stop exploring other paths as soon as it encounters a static pointer to that value"
msgstr "当指针扫描器用一个特定的值遍历指针值列表时, 一旦它遇到静态指针的值将停止搜索其他路径"

#: tfrmpointerscannersettings.cbshowadvancedoptions.caption
msgid "Show advanced options"
msgstr "显示高级选项"

#: tfrmpointerscannersettings.cbstackonly.caption
msgid "Stack addresses as ONLY static address"
msgstr "堆栈地址视为仅有静态地址"

#: tfrmpointerscannersettings.cbstackonly.hint
msgid "Enable this if you wish to only find pointer paths with a stack address."
msgstr "如果你只想查找指针的路径与堆栈地址就启用这个."

#: tfrmpointerscannersettings.cbstaticonly.caption
msgid "Only find paths with a static address"
msgstr "只查找静态地址的路径"

#: tfrmpointerscannersettings.cbstaticonly.hint
msgid "When checked the pointerscan will only store a path when it starts with a static address. (or easily looked up address)"
msgstr "当勾选后,\"指针扫描\"(.PTR)将只存储起始于静态地址的路径. (或许容易查出地址)"

#: tfrmpointerscannersettings.cbstaticstacks.caption
msgid "Allow stack addresses of the first thread(s) to be handled as static"
msgstr "允许第一个线程的堆栈地址视为静态处理"

#: tfrmpointerscannersettings.cbstaticstacks.hint
msgid "This allows the stack of threads to be seen as static addresses by the pointerscan."
msgstr "允许线程的堆栈被视为\"指针扫描\"(.PTR)的静态地址."

#: tfrmpointerscannersettings.cbuseheapdata.caption
msgid "Improve pointerscan with gathered heap data"
msgstr "采集堆(数据)以便改善\"指针扫描\"性能"

#: tfrmpointerscannersettings.cbuseheapdata.hint
msgid "When this is checked the heap is used to figure out the offset sizes, instead of blindly guessing them."
msgstr "当勾选后，堆(数据)是用于计算偏移的大小,而不是盲目地猜测它们."

#: tfrmpointerscannersettings.cbuseloadedpointermap.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.CBUSELOADEDPOINTERMAP.CAPTION"
msgid "Use saved pointermap"
msgstr "使用保存的\"指针映射集\""

#: tfrmpointerscannersettings.cbuseloadedpointermap.hint
msgid "Use this if you have created a pointermap on a system that runs the game, but you wish to do the scan on another system (or multiple systems)."
msgstr "如果你已在已运行游戏的系统上创建了一个\"指针映射集\",而你又想用另一个系统(或多个系统)进行扫描的时候,请使用这个功能."

#: tfrmpointerscannersettings.cbvaluetype.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.CBVALUETYPE.TEXT"
msgid "4 Byte"
msgstr "4 字节"

#: tfrmpointerscannersettings.combobox1.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.COMBOBOX1.TEXT"
msgid "Normal"
msgstr "标准"

#: tfrmpointerscannersettings.editmaxlevel.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.EDITMAXLEVEL.TEXT"
msgid "5"
msgstr "5"

#: tfrmpointerscannersettings.editstructsize.text
msgid "2047"
msgstr "2047"

#: tfrmpointerscannersettings.edtdistributedport.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.EDTDISTRIBUTEDPORT.TEXT"
msgid "52737"
msgstr "52737"

#: tfrmpointerscannersettings.edtmaxoffsetspernode.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.EDTMAXOFFSETSPERNODE.TEXT"
msgid "2"
msgstr "2"

#: tfrmpointerscannersettings.edtreversestart.text
msgctxt "tfrmpointerscannersettings.edtreversestart.text"
msgid "00000000"
msgstr "00000000"

#: tfrmpointerscannersettings.edtreversestop.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.EDTREVERSESTOP.TEXT"
msgid "7FFFFFFF"
msgstr "7FFFFFFF"

#: tfrmpointerscannersettings.edtstacksize.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.EDTSTACKSIZE.TEXT"
msgid "4096"
msgstr "4096"

#: tfrmpointerscannersettings.edtthreadcount.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.EDTTHREADCOUNT.TEXT"
msgid "2"
msgstr "2"

#: tfrmpointerscannersettings.edtthreadstacks.hint
msgid "Fill in the total number of threads that should be allowed to be used as a stack lookup."
msgstr "填写的总线程数应该允许被用于堆栈的查找."

#: tfrmpointerscannersettings.edtthreadstacks.text
msgctxt "TFRMPOINTERSCANNERSETTINGS.EDTTHREADSTACKS.TEXT"
msgid "2"
msgstr "2"

#: tfrmpointerscannersettings.label1.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.LABEL1.CAPTION"
msgid "Password"
msgstr "密码"

#: tfrmpointerscannersettings.label10.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.LABEL10.CAPTION"
msgid "From"
msgstr "从"

#: tfrmpointerscannersettings.label11.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.LABEL11.CAPTION"
msgid "To:"
msgstr "至:"

#: tfrmpointerscannersettings.label12.caption
msgid "Max level"
msgstr "最大级别"

#: tfrmpointerscannersettings.label13.caption
msgid "Pointer path may only be inside this region:"
msgstr "指针的路径仅在以下区域内:"

#: tfrmpointerscannersettings.label2.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.LABEL2.CAPTION"
msgid "To"
msgstr "至"

#: tfrmpointerscannersettings.label3.caption
msgid "Maximum offset value:"
msgstr "最大允许的偏移值:"

#: tfrmpointerscannersettings.label4.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.LABEL4.CAPTION"
msgid "From"
msgstr "从"

#: tfrmpointerscannersettings.label9.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.LABEL9.CAPTION"
msgid "Nr of threads scanning:"
msgstr "扫描线程数:"

#: tfrmpointerscannersettings.lblnumberofstackthreads.caption
msgid "Number of threads from oldest to newest:"
msgstr "从最早到最新的线程数目:"

#: tfrmpointerscannersettings.lblport.caption
msgctxt "tfrmpointerscannersettings.lblport.caption"
msgid "Port:"
msgstr "端口:"

#: tfrmpointerscannersettings.lblstacksize.caption
msgid "Max stackoffset to be deemed static enough:"
msgstr "最大允许足以视为静态地址的堆栈偏移:"

#: tfrmpointerscannersettings.rbfindaddress.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.RBFINDADDRESS.CAPTION"
msgid "Scan for address"
msgstr "扫描地址"

#: tfrmpointerscannersettings.rbfindvalue.caption
msgctxt "TFRMPOINTERSCANNERSETTINGS.RBFINDVALUE.CAPTION"
msgid "Scan for addresses with value"
msgstr "扫描数值"

#: tfrmpointerscannersettings.rbgeneratepointermap.caption
msgctxt "tfrmpointerscannersettings.rbgeneratepointermap.caption"
msgid "Generate pointermap"
msgstr "生成指针映射集"

#: tfrmprocessinfo.button1.caption
msgctxt "TFRMPROCESSINFO.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmprocessinfo.button2.caption
msgid "Threads>>"
msgstr "线程>>"

#: tfrmprocessinfo.caption
msgid "Process/System Info"
msgstr "进程/系统信息"

#: tfrmprocessinfo.label1.caption
msgid "CR3:"
msgstr "CR3:"

#: tfrmprocessinfo.label2.caption
msgid "CR4:"
msgstr "CR4:"

#: tfrmprocessinfo.label3.caption
msgid "PEProcess:"
msgstr "PEProcess:"

#: tfrmprocessinfo.label4.caption
msgid "IDT:"
msgstr "IDT:"

#: tfrmprocessinfo.label5.caption
msgid "SDT:"
msgstr "SDT:"

#: tfrmprocessinfo.label6.caption
msgid "SSDT:"
msgstr "SSDT:"

#: tfrmprocessinfo.label7.caption
msgid "GDT:"
msgstr "GDT:"

#: tfrmprocessinfo.label8.caption
msgid "Valid Handle:"
msgstr "有效句柄:"

#: tfrmprocessinfo.label9.caption
msgid "CR0:"
msgstr "CR0:"

#: tfrmprocessinfo.lblcr0.caption
msgid "lblcr0"
msgstr "lblcr0"

#: tfrmprocessinfo.lblcr3.caption
msgid "lblcr3"
msgstr "lblcr3"

#: tfrmprocessinfo.lblcr4.caption
msgid "lblcr4"
msgstr "lblcr4"

#: tfrmprocessinfo.lblgdt.caption
msgid "lblGdt"
msgstr "lblGdt"

#: tfrmprocessinfo.lblisvalid.caption
msgid "lblisvalid"
msgstr "lblisvalid"

#: tfrmprocessinfo.lblpeprocess.caption
msgid "lblPEPROCESS"
msgstr "lblPEPROCESS"

#: tfrmprocessinfo.lblsdt.caption
msgid "lblSdt"
msgstr "lblSdt"

#: tfrmprocessinfo.lblssdt.caption
msgid "lblSsdt"
msgstr "lblSsdt"

#: tfrmprocesswatcher.btnattach.caption
msgid "Attach to process"
msgstr "附加到进程"

#: tfrmprocesswatcher.btnopen.caption
msgid "Open process"
msgstr "打开进程"

#: tfrmprocesswatcher.caption
msgid "Process watcher"
msgstr "监视进程"

#: tfrmprocesswatcher.showthreadids1.caption
msgid "Show ThreadID's"
msgstr "显示 ThreadID"

#: tfrmprocesswatcherextra.caption
msgid "Processwatcher Extra"
msgstr "进程监视器(扩展)"

#: tfrmreferencedfunctions.caption
msgctxt "tfrmreferencedfunctions.caption"
msgid "Referenced functions"
msgstr "引用的函数"

#: tfrmreferencedfunctions.lvcalllist.columns[0].caption
msgctxt "TFRMREFERENCEDFUNCTIONS.LVCALLLIST.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmreferencedfunctions.lvcalllist.columns[1].caption
msgctxt "tfrmreferencedfunctions.lvcalllist.columns[1].caption"
msgid "Refcount"
msgstr "引用统计"

#: tfrmreferencedfunctions.menuitem1.caption
msgid "Copy list to clipboard"
msgstr "列表复制到剪贴板"

#: tfrmreferencedstrings.caption
msgid "Referenced Strings"
msgstr "引用的字串"

#: tfrmreferencedstrings.find1.caption
msgctxt "TFRMREFERENCEDSTRINGS.FIND1.CAPTION"
msgid "Find"
msgstr "查找"

#: tfrmreferencedstrings.findnext1.caption
msgctxt "TFRMREFERENCEDSTRINGS.FINDNEXT1.CAPTION"
msgid "Find Next"
msgstr "查找下一个"

#: tfrmreferencedstrings.lvstringlist.columns[0].caption
msgctxt "TFRMREFERENCEDSTRINGS.LVSTRINGLIST.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmreferencedstrings.lvstringlist.columns[1].caption
msgctxt "TFRMREFERENCEDSTRINGS.LVSTRINGLIST.COLUMNS[1].CAPTION"
msgid "Refcount"
msgstr "引用计数"

#: tfrmreferencedstrings.lvstringlist.columns[2].caption
msgctxt "tfrmreferencedstrings.lvstringlist.columns[2].caption"
msgid "String"
msgstr "字符串"

#: tfrmreferencedstrings.search1.caption
msgctxt "tfrmreferencedstrings.search1.caption"
msgid "Search"
msgstr "搜索"

#: tfrmrescanpointer.button1.caption
msgctxt "TFRMRESCANPOINTER.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmrescanpointer.button2.caption
msgctxt "TFRMRESCANPOINTER.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmrescanpointer.caption
msgid "Rescan pointerlist"
msgstr "重新扫描指针列表"

#: tfrmrescanpointer.cbbasepointermustbeinrange.caption
msgid "Base pointer must be in range"
msgstr "基指针必须在以下范围内"

#: tfrmrescanpointer.cbchangebasepointeroffset.caption
msgctxt "TFRMRESCANPOINTER.CBCHANGEBASEPOINTEROFFSET.CAPTION"
msgid "Change base pointer address by specific offset"
msgstr "根据特定偏移量更改基指针"

#: tfrmrescanpointer.cbdelay.caption
msgid "Delay rescan for"
msgstr "延迟重新扫描"

#: tfrmrescanpointer.cbfilteroutaccessible.caption
msgid "Only filter out accessible pointers"
msgstr "仅过滤掉可访问指针"

#: tfrmrescanpointer.cbluafilter.caption
msgid "Lua filter. function"
msgstr "Lua 过滤. 函数"

#: tfrmrescanpointer.cbluafilter.hint
msgid "When checked this will call the given function for every pointer being evaluated. Return true if it's valid, false if not"
msgstr "当勾选后，将评价每一个调用了给定函数的指针. 如果它是有效的就返回真(true), 否则就返回假(false)"

#: tfrmrescanpointer.cbmustendwithspecificoffsets.caption
msgid "Must end with offsets"
msgstr "必须以以下偏移结束"

#: tfrmrescanpointer.cbmuststartwithspecificoffsets.caption
msgid "Must start with offsets"
msgstr "必须以以下偏移开始"

#: tfrmrescanpointer.cbnovaluecheck.caption
msgid "Only filter out invalid pointers"
msgstr "只过滤掉无效指针"

#: tfrmrescanpointer.cbrepeat.caption
msgid "Repeat rescan until stopped"
msgstr "重复\"重新扫描\"直到你停止它"

#: tfrmrescanpointer.cbusesavedpointermap.caption
msgctxt "TFRMRESCANPOINTER.CBUSESAVEDPOINTERMAP.CAPTION"
msgid "Use saved pointermap"
msgstr "使用保存的\"指针映射集\""

#: tfrmrescanpointer.cbusesavedpointermap.hint
msgid "Normally this should not be used. Saved pointermaps are best suited for the initial scan."
msgstr "通常不应该使用. 保存的\"指针映射集\"最适合初始扫描."

#: tfrmrescanpointer.cbvaluetype.text
msgctxt "tfrmrescanpointer.cbvaluetype.text"
msgid "4 Byte"
msgstr "4 字节"

#: tfrmrescanpointer.edtbaseend.text
msgctxt "TFRMRESCANPOINTER.EDTBASEEND.TEXT"
msgid "FFFFFFFFFFFFFFFF"
msgstr "FFFFFFFFFFFFFFFF"

#: tfrmrescanpointer.edtbasestart.text
msgctxt "TFRMRESCANPOINTER.EDTBASESTART.TEXT"
msgid "0000000000000000"
msgstr "0000000000000000"

#: tfrmrescanpointer.edtdelay.text
msgctxt "TFRMRESCANPOINTER.EDTDELAY.TEXT"
msgid "0"
msgstr "0"

#: tfrmrescanpointer.edtnewbase.text
msgctxt "TFRMRESCANPOINTER.EDTNEWBASE.TEXT"
msgid "12345678"
msgstr "12345678"

#: tfrmrescanpointer.edtrescanfunction.text
msgid "RescanFilter"
msgstr "RescanFilter"

#: tfrmrescanpointer.label1.caption
msgid "seconds"
msgstr "秒"

#: tfrmrescanpointer.label2.caption
msgid "New base address"
msgstr "新基地址"

#: tfrmrescanpointer.label3.caption
msgid "Original base address"
msgstr "旧基地址"

#: tfrmrescanpointer.lbland.caption
msgctxt "tfrmrescanpointer.lbland.caption"
msgid "and"
msgstr "至"

#: tfrmrescanpointer.lblluaparams.caption
msgid "(base, offsets, target):bool"
msgstr "(基址, 偏移, 目标):布尔值"

#: tfrmrescanpointer.lbloffset.caption
msgctxt "TFRMRESCANPOINTER.LBLOFFSET.CAPTION"
msgid "=XXXX"
msgstr "=XXXX"

#: tfrmrescanpointer.lbloriginalbase.caption
msgctxt "TFRMRESCANPOINTER.LBLORIGINALBASE.CAPTION"
msgid "<address>"
msgstr "<地址>"

#: tfrmrescanpointer.rbfindaddress.caption
msgctxt "tfrmrescanpointer.rbfindaddress.caption"
msgid "Address to find:"
msgstr "要查找的地址:"

#: tfrmrescanpointer.rbfindvalue.caption
msgctxt "tfrmrescanpointer.rbfindvalue.caption"
msgid "Value to find:"
msgstr "要查找的数值:"

#: tfrmresumepointerscan.btnnotifyspecificips.caption
msgctxt "TFRMRESUMEPOINTERSCAN.BTNNOTIFYSPECIFICIPS.CAPTION"
msgid "Setup specific IP's to notify"
msgstr "设置用于通知的特定IP"

#: tfrmresumepointerscan.button1.caption
msgctxt "tfrmresumepointerscan.button1.caption"
msgid "Resume"
msgstr "恢复"

#: tfrmresumepointerscan.button2.caption
msgctxt "TFRMRESUMEPOINTERSCAN.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmresumepointerscan.caption
msgid "Resume pointerscan"
msgstr "恢复 pointerscan"

#: tfrmresumepointerscan.cbbroadcast.caption
msgctxt "TFRMRESUMEPOINTERSCAN.CBBROADCAST.CAPTION"
msgid "Broadcast that a scan has started"
msgstr "广播一个已开始的搜索"

#: tfrmresumepointerscan.cbbroadcast.hint
msgctxt "TFRMRESUMEPOINTERSCAN.CBBROADCAST.HINT"
msgid "When checked will send a broadcast message on the local network which will tell pointer scanner systems to join this scan if they are set to auto join"
msgstr "如果设置为自动连接, 本地网络将在勾选后发送广播消息通知指针扫描器连接此次的扫描"

#: tfrmresumepointerscan.cbdistributedscanning.caption
msgctxt "TFRMRESUMEPOINTERSCAN.CBDISTRIBUTEDSCANNING.CAPTION"
msgid "Allow distributed scanning"
msgstr "允许分布式扫描"

#: tfrmresumepointerscan.cbdistributedscanning.hint
msgctxt "TFRMRESUMEPOINTERSCAN.CBDISTRIBUTEDSCANNING.HINT"
msgid "Opens a port that other systems running the pointerscanner can connect to and help out with the scan"
msgstr "打开一个端口运行 pointerscanner 时其他系统可以连接并参与扫描"

#: tfrmresumepointerscan.combobox1.text
msgctxt "TFRMRESUMEPOINTERSCAN.COMBOBOX1.TEXT"
msgid "Normal"
msgstr "标准"

#: tfrmresumepointerscan.edtdistributedport.text
msgctxt "TFRMRESUMEPOINTERSCAN.EDTDISTRIBUTEDPORT.TEXT"
msgid "52737"
msgstr "52737"

#: tfrmresumepointerscan.edtthreadcount.text
msgctxt "TFRMRESUMEPOINTERSCAN.EDTTHREADCOUNT.TEXT"
msgid "2"
msgstr "2"

#: tfrmresumepointerscan.label1.caption
msgid "Rescan pointermaps"
msgstr "重新扫描 \"指针映射集\""

#: tfrmresumepointerscan.label9.caption
msgctxt "TFRMRESUMEPOINTERSCAN.LABEL9.CAPTION"
msgid "Nr of threads scanning: "
msgstr "线程扫描数: "

#: tfrmresumepointerscan.lblport.caption
msgctxt "TFRMRESUMEPOINTERSCAN.LBLPORT.CAPTION"
msgid "Port:"
msgstr "端口:"

#: tfrmresumepointerscan.listview1.columns[0].caption
msgctxt "TFRMRESUMEPOINTERSCAN.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Filename"
msgstr "文件名"

#: tfrmresumepointerscan.listview1.columns[1].caption
msgctxt "TFRMRESUMEPOINTERSCAN.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmresumepointerscan.listview1.columns[2].caption
msgid "Found"
msgstr "找到"

#: tfrmresumepointerscan.menuitem1.caption
msgid "Add pointermap"
msgstr "添加指针映射图"

#: tfrmsavedisassembly.button1.caption
msgctxt "TFRMSAVEDISASSEMBLY.BUTTON1.CAPTION"
msgid "Save"
msgstr "保存"

#: tfrmsavedisassembly.caption
msgctxt "TFRMSAVEDISASSEMBLY.CAPTION"
msgid "Save disassembled output"
msgstr "保存反汇编输出"

#: tfrmsavedisassembly.cbaddress.caption
msgctxt "TFRMSAVEDISASSEMBLY.CBADDRESS.CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmsavedisassembly.cbbytes.caption
msgctxt "tfrmsavedisassembly.cbbytes.caption"
msgid "字节"
msgstr "字节"

#: tfrmsavedisassembly.cbopcode.caption
msgid "opcode"
msgstr "操作码"

#: tfrmsavedisassembly.label1.caption
msgctxt "TFRMSAVEDISASSEMBLY.LABEL1.CAPTION"
msgid "From"
msgstr "从"

#: tfrmsavedisassembly.label2.caption
msgctxt "TFRMSAVEDISASSEMBLY.LABEL2.CAPTION"
msgid "To"
msgstr "至"

#: tfrmsavememoryregion.button1.caption
msgctxt "TFRMSAVEMEMORYREGION.BUTTON1.CAPTION"
msgid "Save"
msgstr "保存"

#: tfrmsavememoryregion.button2.caption
msgctxt "TFRMSAVEMEMORYREGION.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmsavememoryregion.button3.caption
msgctxt "tfrmsavememoryregion.button3.caption"
msgid "Add"
msgstr "添加"

#: tfrmsavememoryregion.caption
msgid "Save memoryregion"
msgstr "保存内存区域"

#: tfrmsavememoryregion.dontinclude.caption
msgid "Don't include Cheat Engine header in file"
msgstr "不包含 Cheat Engine 头文件"

#: tfrmsavememoryregion.label1.caption
msgid "Add the region(s) of memory you want to save"
msgstr "添加你要保存的内存区域"

#: tfrmsavememoryregion.label2.caption
msgctxt "TFRMSAVEMEMORYREGION.LABEL2.CAPTION"
msgid "From"
msgstr "从"

#: tfrmsavememoryregion.label3.caption
msgctxt "TFRMSAVEMEMORYREGION.LABEL3.CAPTION"
msgid "To"
msgstr "至"

#: tfrmsavememoryregion.miclearlist.caption
msgctxt "tfrmsavememoryregion.miclearlist.caption"
msgid "Clear list"
msgstr "清除列表"

#: tfrmsavesnapshots.btncombinedselect.caption
msgid "Combined select"
msgstr "组合选择"

#: tfrmsavesnapshots.btndone.caption
msgid "Done"
msgstr "完成"

#: tfrmsavesnapshots.btnsave.caption
msgid "Save selected snapshots"
msgstr "保存选中的快照"

#: tfrmsavesnapshots.caption
msgid "Save snapshots"
msgstr "保存快照"

#: tfrmsavesnapshots.label1.caption
msgid "Select the snapshot(s) you wish to save"
msgstr "选择你想要保存的快照"

#: tfrmsavesnapshots.lbldeselectall.caption
msgid "Deselect all"
msgstr "取消全选"

#: tfrmsavesnapshots.lblselectall.caption
msgctxt "TFRMSAVESNAPSHOTS.LBLSELECTALL.CAPTION"
msgid "Select all"
msgstr "全选"

#: tfrmsavesnapshots.savedialog1.title
msgid "Give a base path and filename"
msgstr "指定基本路径和文件名"

#: tfrmselectionlist.button1.caption
msgctxt "TFRMSELECTIONLIST.BUTTON1.CAPTION"
msgid "    OK    "
msgstr "    确定    "

#: tfrmselectionlist.caption
msgid "title"
msgstr "标题"

#: tfrmselectionlist.label1.caption
msgctxt "tfrmselectionlist.label1.caption"
msgid "caption"
msgstr "标题"

#: tfrmsetcrosshair.btnapply.caption
msgid "Apply crosshair"
msgstr "应用十字线"

#: tfrmsetcrosshair.caption
msgid "D3D: Set Crosshair"
msgstr "D3D: 设置十字线"

#: tfrmsetcrosshair.label1.caption
msgid "Alphablend"
msgstr "图像透明度"

#: tfrmsetcrosshair.menuitem1.caption
msgctxt "TFRMSETCROSSHAIR.MENUITEM1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmsetcrosshair.menuitem2.caption
msgid "Load image"
msgstr "载入图像"

#: tfrmsetuppsnnode.btncancel.caption
msgctxt "TFRMSETUPPSNNODE.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmsetuppsnnode.btnok.caption
msgctxt "TFRMSETUPPSNNODE.BTNOK.CAPTION"
msgid "Launch"
msgstr "启动"

#: tfrmsetuppsnnode.button1.caption
msgid "Setup trusted IP list"
msgstr "设定可信任IP列表"

#: tfrmsetuppsnnode.caption
msgid "Setup pointerscan network node"
msgstr "设定 pointerscan 网络节点"

#: tfrmsetuppsnnode.cballowchildren.caption
msgid "Allow children to connect to me"
msgstr "允许子节点与我连接"

#: tfrmsetuppsnnode.cballowparents.caption
msgid "Allow parents to connect to me"
msgstr "允许父节点与我连接"

#: tfrmsetuppsnnode.cballowtempfiles.caption
msgid "Allow temp file usage"
msgstr "允许使用临时文件"

#: tfrmsetuppsnnode.cballowtempfiles.hint
msgid "When checked it will save the scandata files it receives into temporary files on the harddisk"
msgstr "当勾选后将会保存 scandata 文件到它接收到的临时文件的硬盘上"

#: tfrmsetuppsnnode.cbautotrustchildren.caption
msgctxt "TFRMSETUPPSNNODE.CBAUTOTRUSTCHILDREN.CAPTION"
msgid "Automatically trust stability of all new childnodes"
msgstr "自动信任所有新子节点的稳定性"

#: tfrmsetuppsnnode.cbautotrustchildren.hint
msgctxt "TFRMSETUPPSNNODE.CBAUTOTRUSTCHILDREN.HINT"
msgid "Normally when a worker terminates before the scan is done it will send all it's paths to it's connected node and then close, but if the system would crash it wouldn't be able to send it's queues to the node, causing in a loss of possible paths to examine"
msgstr "Normally when a worker terminates before the scan is done it will send all it's paths to it's connected node and then close, but if the system would crash it wouldn't be able to send it's queues to the node, causing in a loss of possible paths to examine"

#: tfrmsetuppsnnode.cbconnecttoothernode.caption
msgid "Connect to other node as"
msgstr "连接到其它节点"

#: tfrmsetuppsnnode.cbconnecttoothernode.hint
msgctxt "TFRMSETUPPSNNODE.CBCONNECTTOOTHERNODE.HINT"
msgid "Connect to another system as it's parent or child"
msgstr "连接到另一个系统的父节点或子节点"

#: tfrmsetuppsnnode.cbmaxfoundresults.caption
msgctxt "TFRMSETUPPSNNODE.CBMAXFOUNDRESULTS.CAPTION"
msgid "Stop scans after finding max amount of results"
msgstr "找到的结果数达到允许的最大数量后就停止扫描"

#: tfrmsetuppsnnode.cbmaxfoundresults.hint
msgid "This will terminate all the children and disconnect from the parent after having found at least the given amount of results"
msgstr "将在查找结果达到限定的数量后终止所有的子节点并与父节点断开"

#: tfrmsetuppsnnode.cbmaxtimetoscan.caption
msgctxt "TFRMSETUPPSNNODE.CBMAXTIMETOSCAN.CAPTION"
msgid "Stop scans after specific time"
msgstr "在特定时间后停止扫描"

#: tfrmsetuppsnnode.cbmaxtimetoscan.hint
msgid "This will terminate all the children and disconnect from the parent after scanning for the given amount of time."
msgstr "将在停止扫描后终止所有子节点并与父节点断开."

#: tfrmsetuppsnnode.cbpriority.text
msgctxt "TFRMSETUPPSNNODE.CBPRIORITY.TEXT"
msgid "Lower"
msgstr "较低"

#: tfrmsetuppsnnode.edtconnectip.text
msgctxt "tfrmsetuppsnnode.edtconnectip.text"
msgid "127.0.0.1"
msgstr "127.0.0.1"

#: tfrmsetuppsnnode.edtconnectport.text
msgctxt "TFRMSETUPPSNNODE.EDTCONNECTPORT.TEXT"
msgid "52737"
msgstr "52737"

#: tfrmsetuppsnnode.edtport.text
msgctxt "TFRMSETUPPSNNODE.EDTPORT.TEXT"
msgid "52737"
msgstr "52737"

#: tfrmsetuppsnnode.edtthreadcount.hint
msgid "The number of threads to scan."
msgstr "进行扫描的线程数."

#: tfrmsetuppsnnode.edtthreadcount.text
msgctxt "TFRMSETUPPSNNODE.EDTTHREADCOUNT.TEXT"
msgid "0"
msgstr "0"

#: tfrmsetuppsnnode.label3.caption
msgid "Found pointer paths"
msgstr "个找到的指针路径"

#: tfrmsetuppsnnode.label4.caption
msgctxt "tfrmsetuppsnnode.label4.caption"
msgid "Seconds"
msgstr "秒"

#: tfrmsetuppsnnode.lblip.caption
msgctxt "TFRMSETUPPSNNODE.LBLIP.CAPTION"
msgid "IP"
msgstr "IP"

#: tfrmsetuppsnnode.lbllistenport.caption
msgctxt "TFRMSETUPPSNNODE.LBLLISTENPORT.CAPTION"
msgid "Listen port"
msgstr "监听端口"

#: tfrmsetuppsnnode.lblpassword.caption
msgctxt "TFRMSETUPPSNNODE.LBLPASSWORD.CAPTION"
msgid "Password"
msgstr "密码"

#: tfrmsetuppsnnode.lblpasswordchild.caption
msgctxt "TFRMSETUPPSNNODE.LBLPASSWORDCHILD.CAPTION"
msgid "Password"
msgstr "密码"

#: tfrmsetuppsnnode.lblpasswordparent.caption
msgctxt "TFRMSETUPPSNNODE.LBLPASSWORDPARENT.CAPTION"
msgid "Password"
msgstr "密码"

#: tfrmsetuppsnnode.lblport.caption
msgctxt "TFRMSETUPPSNNODE.LBLPORT.CAPTION"
msgid "Port"
msgstr "端口"

#: tfrmsetuppsnnode.lblpriority.caption
msgctxt "TFRMSETUPPSNNODE.LBLPRIORITY.CAPTION"
msgid "Priority"
msgstr "优先级"

#: tfrmsetuppsnnode.lblpublicname.caption
msgid "Public name"
msgstr "计算机名"

#: tfrmsetuppsnnode.lblthreadcount.caption
msgid "Threadcount"
msgstr "线程数"

#: tfrmsetuppsnnode.rbconnectaschild.caption
msgid "child"
msgstr "子节点"

#: tfrmsetuppsnnode.rbconnectasparent.caption
msgid "parent"
msgstr "父节点"

#: tfrmsnapshothandler.btncompare.caption
msgctxt "TFRMSNAPSHOTHANDLER.BTNCOMPARE.CAPTION"
msgid "Compare"
msgstr "比较"

#: tfrmsnapshothandler.caption
msgctxt "TFRMSNAPSHOTHANDLER.CAPTION"
msgid "Snapshot handler"
msgstr "快照处理程序"

#: tfrmsnapshothandler.lblcompare.caption
msgid "Compare selected snapshots"
msgstr "比较选中快照"

#: tfrmsnapshothandler.menuitem1.caption
msgctxt "TFRMSNAPSHOTHANDLER.MENUITEM1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmsnapshothandler.menuitem2.caption
msgid "Config"
msgstr "配置"

#: tfrmsnapshothandler.menuitem4.caption
msgid "Open snapshot(s)"
msgstr "打开快照"

#: tfrmsnapshothandler.menuitem8.caption
msgctxt "tfrmsnapshothandler.menuitem8.caption"
msgid "Clear List"
msgstr "清除列表"

#: tfrmsnapshothandler.miconfig.caption
msgid "Configure snapshot settings"
msgstr "配置快照设置"

#: tfrmsnapshothandler.rbcb.caption
msgid "ConstantBuffer"
msgstr "常量缓冲"

#: tfrmsnapshothandler.rbstack.caption
msgctxt "TFRMSNAPSHOTHANDLER.RBSTACK.CAPTION"
msgid "Stack"
msgstr "堆栈"

#: tfrmsortpointerlist.button1.caption
msgctxt "TFRMSORTPOINTERLIST.BUTTON1.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmsortpointerlist.caption
msgid "Sorting"
msgstr "排序"

#: tfrmsortpointerlist.label1.caption
msgid "Sorting the pointerlist. Please wait...."
msgstr "指针列表正在排序中, 请稍候...."

#: tfrmsortpointerlist.lbltimeleft.caption
msgctxt "TFRMSORTPOINTERLIST.LBLTIMELEFT.CAPTION"
msgid "Estimated time left:"
msgstr "预计剩余时间:"

#: tfrmstacktrace.caption
msgctxt "tfrmstacktrace.caption"
msgid "Stacktrace"
msgstr "堆栈跟踪"

#: tfrmstacktrace.listview1.columns[0].caption
msgid "PC"
msgstr "PC"

#: tfrmstacktrace.listview1.columns[1].caption
msgctxt "tfrmstacktrace.listview1.columns[1].caption"
msgid "Stack"
msgstr "堆栈"

#: tfrmstacktrace.listview1.columns[2].caption
msgid "Frame"
msgstr "框架"

#: tfrmstacktrace.listview1.columns[3].caption
msgid "Return"
msgstr "返回"

#: tfrmstacktrace.listview1.columns[4].caption
msgctxt "tfrmstacktrace.listview1.columns[4].caption"
msgid "Parameters"
msgstr "参数"

#: tfrmstacktrace.mimanualstackwalk.caption
msgid "Manual input"
msgstr "手动输入"

#: tfrmstacktrace.refresh1.caption
msgctxt "tfrmstacktrace.refresh1.caption"
msgid "Refresh"
msgstr "刷新"

#: tfrmstackview.caption
msgid "Stack View"
msgstr "堆栈视图"

#: tfrmstackview.lvstack.columns[0].caption
msgctxt "TFRMSTACKVIEW.LVSTACK.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmstackview.lvstack.columns[1].caption
msgctxt "TFRMSTACKVIEW.LVSTACK.COLUMNS[1].CAPTION"
msgid "Value"
msgstr "值"

#: tfrmstackview.lvstack.columns[2].caption
msgid "Secondary"
msgstr "次要"

#: tfrmstackview.menuitem1.caption
msgctxt "TFRMSTACKVIEW.MENUITEM1.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstackview.menuitem2.caption
msgctxt "TFRMSTACKVIEW.MENUITEM2.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstackview.menuitem3.caption
msgid "Lock and add this view to a structure dissect window"
msgstr "锁定并将这个视图添加到一个结构解析窗口"

#: tfrmstackview.menuitem4.caption
msgctxt "TFRMSTACKVIEW.MENUITEM4.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstackview.menuitem5.caption
msgctxt "TFRMSTACKVIEW.MENUITEM5.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstackview.menuitem6.caption
msgid "Copy Line"
msgstr "复制行"

#: tfrmstackview.miaddebp.caption
msgctxt "tfrmstackview.miaddebp.caption"
msgid "(ebp+*)"
msgstr "(ebp+*)"

#: tfrmstackview.miaddesp.caption
msgctxt "tfrmstackview.miaddesp.caption"
msgid "(esp+*)"
msgstr "(esp+*)"

#: tfrmstackview.miaddref.caption
msgctxt "TFRMSTACKVIEW.MIADDREF.CAPTION"
msgid "(ref+*) Ref will be %x"
msgstr "(ref+*) 引用自 %x"

#: tfrmstackview.micopyaddress.caption
msgid "Copy Address"
msgstr "复制地址"

#: tfrmstackview.micopysecondary.caption
msgid "Copy Secondary"
msgstr "复制次要"

#: tfrmstackview.micopyvalue.caption
msgid "Copy Value"
msgstr "复制数值"

#: tfrmstackview.mifind.caption
msgctxt "TFRMSTACKVIEW.MIFIND.CAPTION"
msgid "Find"
msgstr "查找"

#: tfrmstackview.mifindnext.caption
msgid "Find Next..."
msgstr "查找下一个..."

#: tfrmstackview.milockandtrace.caption
msgid "Lock and do manual stacktrace"
msgstr "锁定并手动进行堆栈跟踪"

#: tfrmstackview.misetcolor.caption
msgid "Set background color to address"
msgstr "设定地址的背景色"

#: tfrmstringmap.btnfree.caption
msgid "Free the current list"
msgstr "清空当前列表"

#: tfrmstringmap.btnscan.caption
msgctxt "tfrmstringmap.btnscan.caption"
msgid "Generate string map"
msgstr "生成字符串映射"

#: tfrmstringmap.btnshowlist.caption
msgctxt "tfrmstringmap.btnshowlist.caption"
msgid "<<Show list"
msgstr "<<显示列表"

#: tfrmstringmap.caption
msgid "String map"
msgstr "字符串映射"

#: tfrmstringmap.cbcasesensitive.caption
msgctxt "tfrmstringmap.cbcasesensitive.caption"
msgid "Case sensitive"
msgstr "区分大小写"

#: tfrmstringmap.cbmustbestart.caption
msgctxt "tfrmstringmap.cbmustbestart.caption"
msgid "Must be start"
msgstr "必须是以下字符开头"

#: tfrmstringmap.cbregexp.caption
msgctxt "tfrmstringmap.cbregexp.caption"
msgid "Strings must match reg exp"
msgstr "正则表达式匹配字串"

#: tfrmstringmap.cbsavetodisk.caption
msgctxt "TFRMSTRINGMAP.CBSAVETODISK.CAPTION"
msgid "Save to disk"
msgstr "保存到磁盘"

#: tfrmstringmap.lblstringcount.caption
msgid "Stringcount: 0"
msgstr "字串总数: 0"

#: tfrmstringmap.listview1.columns[0].caption
msgctxt "TFRMSTRINGMAP.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmstringmap.listview1.columns[1].caption
msgctxt "TFRMSTRINGMAP.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "String"
msgstr "字符串"

#: tfrmstringmap.mifind.caption
msgctxt "TFRMSTRINGMAP.MIFIND.CAPTION"
msgid "Find..."
msgstr "查找..."

#: tfrmstringmap.minext.caption
msgid "Next"
msgstr "下一个"

#: tfrmstringpointerscan.btnnewscan.caption
msgctxt "tfrmstringpointerscan.btnnewscan.caption"
msgid "New Scan"
msgstr "新的扫描"

#: tfrmstringpointerscan.btnscan.caption
msgctxt "TFRMSTRINGPOINTERSCAN.BTNSCAN.CAPTION"
msgid "Scan"
msgstr "扫描"

#: tfrmstringpointerscan.caption
msgctxt "tfrmstringpointerscan.caption"
msgid "Structure spider"
msgstr "结构爬行器"

#: tfrmstringpointerscan.cbcasesensitive.caption
msgctxt "TFRMSTRINGPOINTERSCAN.CBCASESENSITIVE.CAPTION"
msgid "Case sensitive"
msgstr "区分大小写"

#: tfrmstringpointerscan.cbhasshadow.caption
msgctxt "tfrmstringpointerscan.cbhasshadow.caption"
msgid "Has Shadow"
msgstr "影子堆栈"

#: tfrmstringpointerscan.cbhasshadow.hint
msgctxt "tfrmstringpointerscan.cbhasshadow.hint"
msgid "Use this if you're spidering a previously saved memory region that currently resides in a different location."
msgstr "如果你搜索一个当前存在不同的位置而上次已保存的内存区域,请使用这个功能."

#: tfrmstringpointerscan.cbhasshadow2.caption
msgctxt "TFRMSTRINGPOINTERSCAN.CBHASSHADOW2.CAPTION"
msgid "Has Shadow"
msgstr "影子堆栈"

#: tfrmstringpointerscan.cbhasshadow2.hint
msgctxt "TFRMSTRINGPOINTERSCAN.CBHASSHADOW2.HINT"
msgid "Use this if you're spidering a previously saved memory region that currently resides in a different location."
msgstr "如果你搜索一个当前存在不同的位置而上次已保存的内存区域,请使用这个功能."

#: tfrmstringpointerscan.cbmappointervalues.caption
msgid "Store values during scan"
msgstr "在扫描时存储数值"

#: tfrmstringpointerscan.cbmappointervalues.hint
msgid "During the scan the values of pointer will be stored in memory for quick lookup when the same address is encountered again"
msgstr "在扫描过程中指针的数值将被存储在内存中, 当再次遇到相同的地址时可进行快速查找"

#: tfrmstringpointerscan.cbmustbestart.caption
msgctxt "TFRMSTRINGPOINTERSCAN.CBMUSTBESTART.CAPTION"
msgid "Must be start"
msgstr "必须是以下字符开头"

#: tfrmstringpointerscan.cbpointerinrange.caption
msgid "Pointer must be in range"
msgstr "指针必须在以下范围内"

#: tfrmstringpointerscan.cbregexp.caption
msgctxt "TFRMSTRINGPOINTERSCAN.CBREGEXP.CAPTION"
msgid "Strings must match reg exp"
msgstr "正则表达式匹配字串"

#: tfrmstringpointerscan.cbreusestringmap.caption
msgid "Use existing stringmap"
msgstr "使用目前的指针映射集"

#: tfrmstringpointerscan.combocomparetype.text
msgctxt "TFRMSTRINGPOINTERSCAN.COMBOCOMPARETYPE.TEXT"
msgid "4 字节"
msgstr "4 字节"

#: tfrmstringpointerscan.combotype.text
msgctxt "TFRMSTRINGPOINTERSCAN.COMBOTYPE.TEXT"
msgid "String"
msgstr "字符串"

#: tfrmstringpointerscan.edtalignsize.text
msgctxt "TFRMSTRINGPOINTERSCAN.EDTALIGNSIZE.TEXT"
msgid "4"
msgstr "4"

#: tfrmstringpointerscan.edtmaxlevel.text
msgctxt "TFRMSTRINGPOINTERSCAN.EDTMAXLEVEL.TEXT"
msgid "2"
msgstr "2"

#: tfrmstringpointerscan.edtstructsize.text
msgctxt "TFRMSTRINGPOINTERSCAN.EDTSTRUCTSIZE.TEXT"
msgid "4096"
msgstr "4096"

#: tfrmstringpointerscan.lblalign.caption
msgid "Alignsize"
msgstr "对齐大小"

#: tfrmstringpointerscan.lbland.caption
msgctxt "TFRMSTRINGPOINTERSCAN.LBLAND.CAPTION"
msgid "and"
msgstr "至"

#: tfrmstringpointerscan.lblbaseregion.caption
msgid "Base region"
msgstr "基址"

#: tfrmstringpointerscan.lblcompare.caption
msgid "Compare type"
msgstr "比较类型"

#: tfrmstringpointerscan.lblextra.caption
msgctxt "TFRMSTRINGPOINTERSCAN.LBLEXTRA.CAPTION"
msgid "Compare against"
msgstr "进行比较"

#: tfrmstringpointerscan.lblinfo.caption
msgctxt "tfrmstringpointerscan.lblinfo.caption"
msgid "Info"
msgstr "信息"

#: tfrmstringpointerscan.lblmaxlevel.caption
msgid "Max Level"
msgstr "最大深度"

#: tfrmstringpointerscan.lblsize.caption
msgctxt "TFRMSTRINGPOINTERSCAN.LBLSIZE.CAPTION"
msgid "Size"
msgstr "大小"

#: tfrmstringpointerscan.lblsize2.caption
msgctxt "TFRMSTRINGPOINTERSCAN.LBLSIZE2.CAPTION"
msgid "Size"
msgstr "大小"

#: tfrmstringpointerscan.lblstring.caption
msgid "String:"
msgstr "字符串:"

#: tfrmstringpointerscan.lblstructsize.caption
msgid "Structsize"
msgstr "结构大小"

#: tfrmstringpointerscan.lblvds.caption
msgid "Variable display type"
msgstr "显示变量类型"

#: tfrmstringpointerscan.menuitem1.caption
msgctxt "TFRMSTRINGPOINTERSCAN.MENUITEM1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmstringpointerscan.menuitem4.caption
msgctxt "TFRMSTRINGPOINTERSCAN.MENUITEM4.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstringpointerscan.menuitem5.caption
msgctxt "tfrmstringpointerscan.menuitem5.caption"
msgid "Rescan"
msgstr "重新扫描"

#: tfrmstringpointerscan.menuitem6.caption
msgid "Filter pointerlist"
msgstr "过滤指针列表"

#: tfrmstringpointerscan.menuitem7.caption
msgctxt "TFRMSTRINGPOINTERSCAN.MENUITEM7.CAPTION"
msgid "New Window"
msgstr "新建窗口"

#: tfrmstringpointerscan.miclearcache.caption
msgid "Clear pointer cache"
msgstr "清除指针缓存"

#: tfrmstringpointerscan.mifind.caption
msgctxt "TFRMSTRINGPOINTERSCAN.MIFIND.CAPTION"
msgid "Find..."
msgstr "查找..."

#: tfrmstringpointerscan.mifindnext.caption
msgctxt "TFRMSTRINGPOINTERSCAN.MIFINDNEXT.CAPTION"
msgid "Find Next"
msgstr "查找下一个"

#: tfrmstringpointerscan.minewscan.caption
msgid "New scan"
msgstr "新的扫描"

#: tfrmstringpointerscan.miopen.caption
msgctxt "TFRMSTRINGPOINTERSCAN.MIOPEN.CAPTION"
msgid "Open"
msgstr "打开"

#: tfrmstringpointerscan.rbdatascan.caption
msgid "Data scan"
msgstr "数据扫描"

#: tfrmstringpointerscan.rbdiffdontcare.caption
msgid "Don't care for difference"
msgstr "有差异无所谓"

#: tfrmstringpointerscan.rbmustbedifferent.caption
msgid "Must be different"
msgstr "必须是不同的"

#: tfrmstringpointerscan.rbmustbesame.caption
msgid "Must be the same"
msgstr "必须是相同的"

#: tfrmstringpointerscan.rbstringscan.caption
msgid "String scan"
msgstr "字符串扫描"

#: tfrmstructurelinker.btncancel.caption
msgctxt "TFRMSTRUCTURELINKER.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmstructurelinker.btnlink.caption
msgid "Link"
msgstr "连结"

#: tfrmstructurelinker.caption
msgid "Structure linker"
msgstr "结构连接器"

#: tfrmstructurelinker.cbfilllocal.caption
msgid "Fill in 'Local' structures"
msgstr "填写\"局部\"结构"

#: tfrmstructurelinker.cbnoexactmatches.caption
msgid "Fill in pointers that point into a structure instead of the start"
msgstr "填写成一个结构而不是一开始就指向指针"

#: tfrmstructurelinker.cboverridelocal.caption
msgid "Override 'Local' structures when needed"
msgstr "必要时覆盖\"局部\"结构"

#: tfrmstructurelinker.label1.caption
msgid "Fill in the known addresses of these structures (Can be blank if unknown or unrelated)"
msgstr "填写这些结构已知的地址(如果不知道或无关可以留空)"

#: tfrmstructures.caption
msgctxt "TFRMSTRUCTURES.CAPTION"
msgid "Memory dissect"
msgstr "内存分析"

#: tfrmstructures2.addextraaddress1.caption
msgctxt "TFRMSTRUCTURES2.ADDEXTRAADDRESS1.CAPTION"
msgid "Add extra address"
msgstr "添加额外的地址"

#: tfrmstructures2.caption
msgctxt "TFRMSTRUCTURES2.CAPTION"
msgid "Structure dissect"
msgstr "结构分析"

#: tfrmstructures2.definenewstructure1.caption
msgctxt "TFRMSTRUCTURES2.DEFINENEWSTRUCTURE1.CAPTION"
msgid "Define new structure"
msgstr "定义新的结构"

#: tfrmstructures2.deletecurrentstructure1.caption
msgctxt "TFRMSTRUCTURES2.DELETECURRENTSTRUCTURE1.CAPTION"
msgid "Delete structure"
msgstr "删除结构"

#: tfrmstructures2.file1.caption
msgctxt "TFRMSTRUCTURES2.FILE1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmstructures2.headercontrol1.sections[0].text
msgctxt "TFRMSTRUCTURES2.HEADERCONTROL1.SECTIONS[0].TEXT"
msgid "Offset-description"
msgstr "偏移-描述"

#: tfrmstructures2.menuitem1.caption
msgctxt "TFRMSTRUCTURES2.MENUITEM1.CAPTION"
msgid "View"
msgstr "视图"

#: tfrmstructures2.menuitem2.caption
msgctxt "TFRMSTRUCTURES2.MENUITEM2.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.menuitem3.caption
msgctxt "TFRMSTRUCTURES2.MENUITEM3.CAPTION"
msgid "Save values"
msgstr "保存数值"

#: tfrmstructures2.menuitem4.caption
msgctxt "TFRMSTRUCTURES2.MENUITEM4.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.menuitem5.caption
msgid "Add new group"
msgstr "添加新群组"

#: tfrmstructures2.menuitem6.caption
msgid "Find value"
msgstr "查找数值"

#: tfrmstructures2.menuitem7.caption
msgctxt "TFRMSTRUCTURES2.MENUITEM7.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.miaddchildelement.caption
msgid "Add child element"
msgstr "添加子元素"

#: tfrmstructures2.miaddelement.caption
msgid "Add element"
msgstr "添加元素"

#: tfrmstructures2.miaddtoaddresslist.caption
msgctxt "TFRMSTRUCTURES2.MIADDTOADDRESSLIST.CAPTION"
msgid "Add to address list"
msgstr "添加到地址列表"

#: tfrmstructures2.miautocreate.caption
msgctxt "TFRMSTRUCTURES2.MIAUTOCREATE.CAPTION"
msgid "Autocreate local structures when expanding undefined pointer types"
msgstr "展开未定义的指针类型时,自动创建局部结构"

#: tfrmstructures2.miautodestroylocal.caption
msgctxt "TFRMSTRUCTURES2.MIAUTODESTROYLOCAL.CAPTION"
msgid "Autodestroy local structures when closed"
msgstr "关闭时自动清除局部结构"

#: tfrmstructures2.miautofillgaps.caption
msgctxt "TFRMSTRUCTURES2.MIAUTOFILLGAPS.CAPTION"
msgid "Fill gaps automatically"
msgstr "自动填补空白"

#: tfrmstructures2.miautoguess.caption
msgid "Autoguess offset types"
msgstr "自动猜测偏移类型"

#: tfrmstructures2.miautostructsize.caption
msgctxt "TFRMSTRUCTURES2.MIAUTOSTRUCTSIZE.CAPTION"
msgid "Autocreate structure size: 4096"
msgstr "自动创建的结构大小:4096"

#: tfrmstructures2.miback.caption
msgctxt "TFRMSTRUCTURES2.MIBACK.CAPTION"
msgid "Back"
msgstr "返回"

#: tfrmstructures2.mibrowseaddress.caption
msgctxt "TFRMSTRUCTURES2.MIBROWSEADDRESS.CAPTION"
msgid "Memory browse this address"
msgstr "浏览地址"

#: tfrmstructures2.mibrowsepointer.caption
msgctxt "TFRMSTRUCTURES2.MIBROWSEPOINTER.CAPTION"
msgid "Memory browse pointer"
msgstr "浏览指针"

#: tfrmstructures2.michangecolors.caption
msgctxt "TFRMSTRUCTURES2.MICHANGECOLORS.CAPTION"
msgid "Settings"
msgstr "设置"

#: tfrmstructures2.michangeelement.caption
msgctxt "TFRMSTRUCTURES2.MICHANGEELEMENT.CAPTION"
msgid "Change element"
msgstr "更改元素"

#: tfrmstructures2.michangevalue.caption
msgctxt "TFRMSTRUCTURES2.MICHANGEVALUE.CAPTION"
msgid "Change value"
msgstr "更改数值"

#: tfrmstructures2.miclear.caption
msgid "Delete all structures"
msgstr "删除所有结构"

#: tfrmstructures2.micommands.caption
msgid "Structure Options"
msgstr "结构选项"



#: tfrmstructures2.midefaulthexadecimal.caption
msgid "New integer types default to hexadecimal"
msgstr "新的整数类型默认为十六进制"

#: tfrmstructures2.mideleteelement.caption
msgid "Delete element(s)"
msgstr "删除元素"

#: tfrmstructures2.midonotsavelocal.caption
msgctxt "TFRMSTRUCTURES2.MIDONOTSAVELOCAL.CAPTION"
msgid "Do not save autocreated local structures"
msgstr "不保存自动创建的局部结构"

#: tfrmstructures2.mieverythinghex.caption
msgid "View everything as hexadecimal"
msgstr "以十六进制显示"

#: tfrmstructures2.miexpandall.caption
msgid "Expand all defined and undefined pointers"
msgstr "展开所有已定义和未定义的指针"

#: tfrmstructures2.miexpandalldefined.caption
msgid "Expand all defined pointers"
msgstr "展开所有已定义的指针"

#: tfrmstructures2.miexportall.caption
msgid "Export all structures"
msgstr "导出所有结构"

#: tfrmstructures2.mifillgaps.caption
msgid "Fill gaps"
msgstr "填补空白"

#: tfrmstructures2.mifindrelations.caption
msgid "Find relations"
msgstr "查找关联"

#: tfrmstructures2.mifullupgrade.caption
msgid "Upgrade this child to full structure"
msgstr "将子项升级到完整结构"

#: tfrmstructures2.migenerategroupscan.caption
msgctxt "TFRMSTRUCTURES2.MIGENERATEGROUPSCAN.CAPTION"
msgid "Generate groupscan command"
msgstr "生成\"群组扫描\"参数"

#: tfrmstructures2.minewwindow.caption
msgctxt "TFRMSTRUCTURES2.MINEWWINDOW.CAPTION"
msgid "New window"
msgstr "新建窗口"

#: tfrmstructures2.mipaste.caption
msgid "Paste elements(s)"
msgstr "粘贴元素"

#: tfrmstructures2.mirecalculateaddress.caption
msgctxt "TFRMSTRUCTURES2.MIRECALCULATEADDRESS.CAPTION"
msgid "Recalculate address"
msgstr "重新计算地址"

#: tfrmstructures2.mirlecompression.caption
msgid "Enable RLE Compression"
msgstr "启用 RLE 压缩"

#: tfrmstructures2.mishowaddresses.caption
msgid "Show addresses"
msgstr "显示地址"

#: tfrmstructures2.mishowtypeforentrieswithnodescription.caption
msgid "Show type for entries with no description"
msgstr "显示无描述的条目的类型"

#: tfrmstructures2.miupdateinterval.caption
msgctxt "TFRMSTRUCTURES2.MIUPDATEINTERVAL.CAPTION"
msgid "Update interval: 500"
msgstr "更新间隔: 500"

#: tfrmstructures2.miupdateoffsets.caption
msgctxt "TFRMSTRUCTURES2.MIUPDATEOFFSETS.CAPTION"
msgid "Update this and following offsets"
msgstr "更新以下偏移"

#: tfrmstructures2.n1.caption
msgctxt "TFRMSTRUCTURES2.N1.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.n2.caption
msgctxt "TFRMSTRUCTURES2.N2.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.n3.caption
msgctxt "TFRMSTRUCTURES2.N3.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.n4.caption
msgctxt "TFRMSTRUCTURES2.N4.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.n5.caption
msgctxt "TFRMSTRUCTURES2.N5.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.n7.caption
msgctxt "TFRMSTRUCTURES2.N7.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.n8.caption
msgctxt "TFRMSTRUCTURES2.N8.CAPTION"
msgid "-"
msgstr "-"

#: tfrmstructures2.open1.caption
msgctxt "TFRMSTRUCTURES2.OPEN1.CAPTION"
msgid "Import"
msgstr "导入"

#: tfrmstructures2.renamestructure1.caption
msgctxt "TFRMSTRUCTURES2.RENAMESTRUCTURE1.CAPTION"
msgid "Rename structure"
msgstr "重命名结构"

#: tfrmstructures2.save1.caption
msgid "Export current structure"
msgstr "导出当前的结构"

#: tfrmstructures2.structures1.caption
msgctxt "TFRMSTRUCTURES2.STRUCTURES1.CAPTION"
msgid "Structures"
msgstr "结构"

#: tfrmstructures2elementinfo.button1.caption
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmstructures2elementinfo.button2.caption
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmstructures2elementinfo.caption
msgid "Structure Info"
msgstr "结构信息"

#: tfrmstructures2elementinfo.cbexpandchangesaddress.caption
msgid "Expanding this node will change the address"
msgstr "展开此节点更改地址"

#: tfrmstructures2elementinfo.cbhexadecimal.caption
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.CBHEXADECIMAL.CAPTION"
msgid "Hexadecimal"
msgstr "十六进制"

#: tfrmstructures2elementinfo.cbsigned.caption
msgctxt "tfrmstructures2elementinfo.cbsigned.caption"
msgid "Signed"
msgstr "有符号"

#: tfrmstructures2elementinfo.cbstructtype.text
msgid "Undefined"
msgstr "未定义"

#: tfrmstructures2elementinfo.cbtype.text
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.CBTYPE.TEXT"
msgid "4 字节"
msgstr "4 字节"

#: tfrmstructures2elementinfo.edtbytesize.text
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.EDTBYTESIZE.TEXT"
msgid "0"
msgstr "0"

#: tfrmstructures2elementinfo.edtchildstart.text
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.EDTCHILDSTART.TEXT"
msgid "0"
msgstr "0"

#: tfrmstructures2elementinfo.edtoffset.text
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.EDTOFFSET.TEXT"
msgid "0"
msgstr "0"

#: tfrmstructures2elementinfo.label1.caption
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.LABEL1.CAPTION"
msgid "Description"
msgstr "描述"

#: tfrmstructures2elementinfo.label2.caption
msgctxt "tfrmstructures2elementinfo.label2.caption"
msgid "Bytesize"
msgstr "数据位数"

#: tfrmstructures2elementinfo.label3.caption
msgctxt "tfrmstructures2elementinfo.label3.caption"
msgid "Offset"
msgstr "偏移"

#: tfrmstructures2elementinfo.label4.caption
msgctxt "TFRMSTRUCTURES2ELEMENTINFO.LABEL4.CAPTION"
msgid "Type"
msgstr "类型"

#: tfrmstructures2elementinfo.label5.caption
msgid "Structure pointed to"
msgstr "结构指向"

#: tfrmstructures2elementinfo.label6.caption
msgid "Background Color"
msgstr "背景色"

#: tfrmstructures2elementinfo.lbloffsetinto.caption
msgid "Offset into"
msgstr "偏移信息"

#: tfrmstructuresaddelement.button1.caption
msgctxt "TFRMSTRUCTURESADDELEMENT.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmstructuresaddelement.button2.caption
msgctxt "TFRMSTRUCTURESADDELEMENT.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmstructuresaddelement.caption
msgid "Add Element"
msgstr "添加元素"

#: tfrmstructuresaddelement.cbpointerto.caption
msgid "pointer to:"
msgstr "指针指向:"

#: tfrmstructuresaddelement.edtbytesize.text
msgctxt "TFRMSTRUCTURESADDELEMENT.EDTBYTESIZE.TEXT"
msgid "0"
msgstr "0"

#: tfrmstructuresaddelement.edtdescription.text
msgctxt "TFRMSTRUCTURESADDELEMENT.EDTDESCRIPTION.TEXT"
msgid "undefined"
msgstr "未知"

#: tfrmstructuresaddelement.edtoffset.text
msgctxt "TFRMSTRUCTURESADDELEMENT.EDTOFFSET.TEXT"
msgid "0"
msgstr "0"

#: tfrmstructuresaddelement.label1.caption
msgid "description"
msgstr "描述"

#: tfrmstructuresaddelement.label2.caption
msgctxt "TFRMSTRUCTURESADDELEMENT.LABEL2.CAPTION"
msgid "Bytesize"
msgstr "Bytesize"

#: tfrmstructuresaddelement.label3.caption
msgctxt "TFRMSTRUCTURESADDELEMENT.LABEL3.CAPTION"
msgid "Offset"
msgstr "偏移"

#: tfrmstructuresconfig.button1.caption
msgctxt "TFRMSTRUCTURESCONFIG.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmstructuresconfig.button2.caption
msgctxt "TFRMSTRUCTURESCONFIG.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmstructuresconfig.button3.caption
msgid "Font"
msgstr "字体"

#: tfrmstructuresconfig.caption
msgid "Dissect Data Config"
msgstr "分析数据配置"

#: tfrmstructuresconfig.cbautocreate.caption
msgctxt "tfrmstructuresconfig.cbautocreate.caption"
msgid "Autocreate local structures when expanding undefined pointer types"
msgstr "展开未定义的指针类型时,自动创建局部结构"

#: tfrmstructuresconfig.cbautodestroylocal.caption
msgctxt "tfrmstructuresconfig.cbautodestroylocal.caption"
msgid "Autodestroy local structures when closed"
msgstr "关闭时自动清除局部结构"

#: tfrmstructuresconfig.cbautofillgaps.caption
msgctxt "tfrmstructuresconfig.cbautofillgaps.caption"
msgid "Fill gaps automatically"
msgstr "自动填补空白"

#: tfrmstructuresconfig.cbautoguesscustomtypes.caption
msgid "Autoguess custom types"
msgstr "自动猜测自定义类型"

#: tfrmstructuresconfig.cbdefaulthex.caption
msgid "Default to hexadecimal for new integer entries"
msgstr "新的整数项默认为十六进制"

#: tfrmstructuresconfig.cbdonotsavelocal.caption
msgctxt "tfrmstructuresconfig.cbdonotsavelocal.caption"
msgid "Do not save autocreated local structures"
msgstr "不保存自动创建的局部结构"

#: tfrmstructuresconfig.combobackground.text
msgctxt "TFRMSTRUCTURESCONFIG.COMBOBACKGROUND.TEXT"
msgid "Normal"
msgstr "标准"

#: tfrmstructuresconfig.edtautostructsize.text
msgctxt "TFRMSTRUCTURESCONFIG.EDTAUTOSTRUCTSIZE.TEXT"
msgid "4096"
msgstr "4096"

#: tfrmstructuresconfig.edtmaxautoexpandlevel.text
msgctxt "TFRMSTRUCTURESCONFIG.EDTMAXAUTOEXPANDLEVEL.TEXT"
msgid "1"
msgstr "1"

#: tfrmstructuresconfig.groupbox1.caption
msgid "Colors"
msgstr "颜色"

#: tfrmstructuresconfig.groupbox2.caption
msgid "Default options for new structures"
msgstr "新结构的默认设置"

#: tfrmstructuresconfig.groupbox3.caption
msgid "Global options"
msgstr "全局设置"

#: tfrmstructuresconfig.label1.caption
msgctxt "TFRMSTRUCTURESCONFIG.LABEL1.CAPTION"
msgid "Default"
msgstr "默认"

#: tfrmstructuresconfig.label1.hint
msgid "Color used when only one column"
msgstr "只有一列时使用颜色"

#: tfrmstructuresconfig.label2.caption
msgid "Different"
msgstr "差异"

#: tfrmstructuresconfig.label2.hint
msgid "Color used when only one group and not all columns have the same value, or when multiple groups but not every group has matching columns"
msgstr "只有一组而不是所有列有相同的数值时使用的颜色时,或多个组但不是每个组符合的列"

#: tfrmstructuresconfig.label3.caption
msgid "Group Equal"
msgstr "相同的群组"

#: tfrmstructuresconfig.label3.hint
msgid "Color used when multiple groups and every member of every group has the same value, and that value matches that of each group"
msgstr "当具有多个群组且所有群组的所有成员的数值都匹配时所使用的颜色"

#: tfrmstructuresconfig.label4.caption
msgid "Autocreate structure size"
msgstr "自动创建的结构大小"

#: tfrmstructuresconfig.label5.caption
msgid "Max auto-expand level:"
msgstr "自动展开的层数:"

#: tfrmstructuresconfig.label7.caption
msgid "Equal"
msgstr "相等"

#: tfrmstructuresconfig.label7.hint
msgid "Color used when only one group and all columns have the same value"
msgstr "当一个群组和多个列都是相同数值时所使用的颜色"

#: tfrmstructuresconfig.label9.caption
msgid "Group Different"
msgstr "不同的群组"

#: tfrmstructuresconfig.label9.hint
msgid "Color used when multiple groups and every member of every group has the same value, but not each group has the same value"
msgstr "当多个群组且每个群组的所有成员都是相同的数值, 但又不是所有群组都是相同的数值时所使用的颜色"

#: tfrmsymbolhandler.button1.caption
msgid "Add symbol"
msgstr "添加符号"

#: tfrmsymbolhandler.caption
msgid "Symbol config"
msgstr "符号配置"

#: tfrmsymbolhandler.delete1.caption
msgctxt "TFRMSYMBOLHANDLER.DELETE1.CAPTION"
msgid "Delete"
msgstr "删除"

#: tfrmsymbolhandler.label1.caption
msgid "Userdefined symbols:"
msgstr "用户自定义的符号:"

#: tfrmsymbolhandler.label2.caption
msgctxt "TFRMSYMBOLHANDLER.LABEL2.CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmsymbolhandler.label3.caption
msgid "Unique symbolname"
msgstr "独特的符号名称"

#: tfrmsymbolhandler.listview1.columns[0].caption
msgid "Symbolname"
msgstr "符号名称"

#: tfrmsymbolhandler.listview1.columns[1].caption
msgctxt "TFRMSYMBOLHANDLER.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmsymbolhandler.listview1.columns[2].caption
msgid "AllocSize"
msgstr "分配大小"

#: tfrmsymbolhandler.menuitem1.caption
msgid "Delete all"
msgstr "全部删除"

#: tfrmsymbolhandler.panel3.caption
msgid "Panel3"
msgstr "Panel3"

#: tfrmthreadlist.caption
msgctxt "tfrmthreadlist.caption"
msgid "Threadlist"
msgstr "线程列表"

#: tfrmthreadlist.lbliswow64.caption
msgid "Hold ctrl when expanding to see the 32-bit context of a thread"
msgstr "当扩展到看一个线程的32位配置指令时按住Ctrl键"

#: tfrmthreadlist.menuitem1.caption
msgid "Stackview"
msgstr "堆栈视图"

#: tfrmthreadlist.mibreak.caption
msgctxt "tfrmthreadlist.mibreak.caption"
msgid "Break"
msgstr "中断"

#: tfrmthreadlist.micleardebugregisters.caption
msgid "Clear debug registers"
msgstr "清除调试寄存器"

#: tfrmthreadlist.mifreezethread.caption
msgid "Freeze thread"
msgstr "冻结线程"

#: tfrmthreadlist.miresumethread.caption
msgid "Resume thread"
msgstr "恢复线程"

#: tfrmthreadlistex.button1.caption
msgctxt "TFRMTHREADLISTEX.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmthreadlistex.caption
msgid "ThreadInfo"
msgstr "线程信息"

#: tfrmtracer.aflabel.caption
msgctxt "tfrmtracer.aflabel.caption"
msgid "AF 0"
msgstr "AF 0"

#: tfrmtracer.btnstopsearch.caption
msgid "Stop Search"
msgstr "停止搜索"

#: tfrmtracer.button1.caption
msgctxt "TFRMTRACER.BUTTON1.CAPTION"
msgid "Close"
msgstr "关闭"

#: tfrmtracer.caption
msgid "Tracer"
msgstr "跟踪器"

#: tfrmtracer.cflabel.caption
msgctxt "tfrmtracer.cflabel.caption"
msgid "CF 0"
msgstr "CF 0"

#: tfrmtracer.cslabel.caption
msgctxt "tfrmtracer.cslabel.caption"
msgid "CS 0000"
msgstr "CS 0000"

#: tfrmtracer.dflabel.caption
msgctxt "tfrmtracer.dflabel.caption"
msgid "DF 0"
msgstr "DF 0"

#: tfrmtracer.dslabel.caption
msgctxt "tfrmtracer.dslabel.caption"
msgid "DS 0000"
msgstr "DS 0000"

#: tfrmtracer.eaxlabel.caption
msgctxt "TFRMTRACER.EAXLABEL.CAPTION"
msgid "EAX 00000000"
msgstr "EAX 00000000"

#: tfrmtracer.ebplabel.caption
msgctxt "TFRMTRACER.EBPLABEL.CAPTION"
msgid "EBP 00000000"
msgstr "EBP 00000000"

#: tfrmtracer.ebxlabel.caption
msgctxt "TFRMTRACER.EBXLABEL.CAPTION"
msgid "EBX 00000000"
msgstr "EBX 00000000"

#: tfrmtracer.ecxlabel.caption
msgctxt "TFRMTRACER.ECXLABEL.CAPTION"
msgid "ECX 00000000"
msgstr "ECX 00000000"

#: tfrmtracer.edilabel.caption
msgctxt "TFRMTRACER.EDILABEL.CAPTION"
msgid "EDI 00000000"
msgstr "EDI 00000000"

#: tfrmtracer.edxlabel.caption
msgctxt "TFRMTRACER.EDXLABEL.CAPTION"
msgid "EDX 00000000"
msgstr "EDX 00000000"

#: tfrmtracer.eiplabel.caption
msgctxt "TFRMTRACER.EIPLABEL.CAPTION"
msgid "EIP 00000000"
msgstr "EIP 00000000"

#: tfrmtracer.esilabel.caption
msgctxt "TFRMTRACER.ESILABEL.CAPTION"
msgid "ESI 00000000"
msgstr "ESI 00000000"

#: tfrmtracer.eslabel.caption
msgctxt "tfrmtracer.eslabel.caption"
msgid "ES 0000"
msgstr "ES 0000"

#: tfrmtracer.esplabel.caption
msgctxt "TFRMTRACER.ESPLABEL.CAPTION"
msgid "ESP 00000000"
msgstr "ESP 00000000"

#: tfrmtracer.finddialog1.title
msgctxt "TFRMTRACER.FINDDIALOG1.TITLE"
msgid "Type the (LUA) condition you want to search for (Example: EAX==0x1234)"
msgstr "键入(LUA)要搜索的条件(如: EAX==0x1234)"

#: tfrmtracer.fslabel.caption
msgctxt "tfrmtracer.fslabel.caption"
msgid "FS 0000"
msgstr "FS 0000"

#: tfrmtracer.gslabel.caption
msgctxt "tfrmtracer.gslabel.caption"
msgid "GS 0000"
msgstr "GS 0000"

#: tfrmtracer.lbladdressed.caption
msgid "Accesses Address:"
msgstr "访问地址:"

#: tfrmtracer.lblinstruction.caption
msgctxt "TFRMTRACER.LBLINSTRUCTION.CAPTION"
msgid "Instruction"
msgstr "指令"

#: tfrmtracer.menuitem1.caption
msgctxt "TFRMTRACER.MENUITEM1.CAPTION"
msgid "File"
msgstr "文件"

#: tfrmtracer.menuitem2.caption
msgctxt "TFRMTRACER.MENUITEM2.CAPTION"
msgid "Search"
msgstr "搜索"

#: tfrmtracer.menuitem3.caption
msgctxt "TFRMTRACER.MENUITEM3.CAPTION"
msgid "-"
msgstr "-"

#: tfrmtracer.menuitem4.caption
msgctxt "tfrmtracer.menuitem4.caption"
msgid "Find..."
msgstr "查找..."

#: tfrmtracer.menuitem5.caption
msgid "Expand all"
msgstr "全部展开"

#: tfrmtracer.menuitem6.caption
msgid "Collapse all"
msgstr "全部折叠"

#: tfrmtracer.miload.caption
msgid "Open trace"
msgstr "打开跟踪"

#: tfrmtracer.misave.caption
msgid "Save trace"
msgstr "保存跟踪"

#: tfrmtracer.misavetodisk.caption
msgid "Save results to disk as textfile"
msgstr "以文本文件形式将结果保存到硬盘"

#: tfrmtracer.misearchnext.caption
msgctxt "TFRMTRACER.MISEARCHNEXT.CAPTION"
msgid "Find Next"
msgstr "查找下一个"

#: tfrmtracer.oflabel.caption
msgctxt "tfrmtracer.oflabel.caption"
msgid "OF 0"
msgstr "OF 0"

#: tfrmtracer.pflabel.caption
msgctxt "tfrmtracer.pflabel.caption"
msgid "PF 0"
msgstr "PF 0"

#: tfrmtracer.sbshowfloats.caption
msgctxt "TFRMTRACER.SBSHOWFLOATS.CAPTION"
msgid "Float"
msgstr "浮点"

#: tfrmtracer.sbshowfloats.hint
msgctxt "TFRMTRACER.SBSHOWFLOATS.HINT"
msgid "Floating point registers"
msgstr "浮点寄存器"

#: tfrmtracer.sbshowstack.caption
msgctxt "TFRMTRACER.SBSHOWSTACK.CAPTION"
msgid "Stack"
msgstr "堆栈"

#: tfrmtracer.sbshowstack.hint
msgctxt "TFRMTRACER.SBSHOWSTACK.HINT"
msgid "Stack"
msgstr "堆栈"

#: tfrmtracer.sflabel.caption
msgctxt "tfrmtracer.sflabel.caption"
msgid "SF 0"
msgstr "SF 0"

#: tfrmtracer.sslabel.caption
msgctxt "tfrmtracer.sslabel.caption"
msgid "SS 0000"
msgstr "SS 0000"

#: tfrmtracer.zflabel.caption
msgctxt "tfrmtracer.zflabel.caption"
msgid "ZF 0"
msgstr "ZF 0"

#: tfrmtracerconfig.btncancel.caption
msgctxt "TFRMTRACERCONFIG.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmtracerconfig.btnok.caption
msgctxt "TFRMTRACERCONFIG.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmtracerconfig.caption
msgid "Break and Trace config"
msgstr "中断和跟踪配置"

#: tfrmtracerconfig.cbdereferenceaddresses.caption
msgid "Dereference Addresses (Slows down)"
msgstr "解除引用的地址 (速度减缓)"

#: tfrmtracerconfig.cbsavestack.caption
msgid "Save stack snapshots (Eats memory)"
msgstr "保存堆栈的快照 (消耗内存)"

#: tfrmtracerconfig.cbskipsystemmodules.caption
msgid "Skip system modules"
msgstr "跳过系统模块"

#: tfrmtracerconfig.cbstepover.caption
msgid "Step over instead of single step"
msgstr "步过,而不是单步步进"

#: tfrmtracerconfig.edtmaxtrace.text
msgctxt "TFRMTRACERCONFIG.EDTMAXTRACE.TEXT"
msgid "1000"
msgstr "1000"

#: tfrmtracerconfig.label1.caption
msgid "Maximal trace count:"
msgstr "最大跟踪数:"

#: tfrmtracerconfig.label2.caption
msgid "Stop condition (Optional, LUA format)"
msgstr "停止条件 (可选 LUA 格式)"

#: tfrmtracerconfig.rbbreakonaccess.caption
msgctxt "tfrmtracerconfig.rbbreakonaccess.caption"
msgid "Break on Access"
msgstr "访问时中断"

#: tfrmtracerconfig.rbbreakonwrite.caption
msgctxt "tfrmtracerconfig.rbbreakonwrite.caption"
msgid "Break on Write"
msgstr "写入时中断"

#: tfrmtrainergenerator.btnaddhotkey.caption
msgid "Add Hotkey"
msgstr "添加热键"

#: tfrmtrainergenerator.btnaddsounds.caption
msgid "Add sound(s)"
msgstr "添加声音"

#: tfrmtrainergenerator.btndelete.caption
msgid "Delete Hotkey"
msgstr "删除热键"

#: tfrmtrainergenerator.btndesignform.caption
msgctxt "TFRMTRAINERGENERATOR.BTNDESIGNFORM.CAPTION"
msgid "Design userinterface manually"
msgstr "使用者手动设计界面"

#: tfrmtrainergenerator.button1.caption
msgid "Set Icon"
msgstr "设置图标"

#: tfrmtrainergenerator.button2.caption
msgid "Set Side Image"
msgstr "设置侧面图片"

#: tfrmtrainergenerator.button3.caption
msgctxt "TFRMTRAINERGENERATOR.BUTTON3.CAPTION"
msgid "Clear"
msgstr "清除"

#: tfrmtrainergenerator.button5.caption
msgid "Generate trainer"
msgstr "生成修改器"

#: tfrmtrainergenerator.button5.hint
msgid "Generates the trainer script and saves the current table as a .CETRAINER"
msgstr "制作修改器的脚本保存到当前的表单中并另存为 .CETRAINER"

#: tfrmtrainergenerator.caption
msgid "Trainer generator"
msgstr "修改器生成器"

#: tfrmtrainergenerator.cbactivatesound.text
msgctxt "tfrmtrainergenerator.cbactivatesound.text"
msgid "Activate"
msgstr "激活"

#: tfrmtrainergenerator.cbcanresize.caption
msgid "User can resize window"
msgstr "用户可以调整窗口大小"

#: tfrmtrainergenerator.cbconfigd3dhook.caption
msgid "Configure"
msgstr "配置"

#: tfrmtrainergenerator.cbdeactivatesound.text
msgctxt "tfrmtrainergenerator.cbdeactivatesound.text"
msgid "Deactivate"
msgstr "禁用"

#: tfrmtrainergenerator.cboutput.text
msgid "EXE"
msgstr "EXE"

#: tfrmtrainergenerator.cbplaysoundonaction.caption
msgid "Play sound on activate/deactivate"
msgstr "激活或禁用时播放声音"

#: tfrmtrainergenerator.cbplayxm.caption
msgid "Play mod music file"
msgstr "播放 MOD 音乐文件"

#: tfrmtrainergenerator.cbpopuponkeypress.caption
msgid "Popup trainer on keypress"
msgstr "启用快捷键弹出修改器"

#: tfrmtrainergenerator.cbprotect.caption
msgid "Protect trainer from editing/reading"
msgstr "保护修改器不被修改/提取"

#: tfrmtrainergenerator.cbprotect.hint
msgid "Will encrypt the generated cetrainer file and when Cheat Engine loads this file back it will destroy the main form preventing the user from doing anything with it."
msgstr "将生成的 cetrainer 文件加密，当CE加载这个文件后它将破坏主窗口以阻止用户对其做任何事情."

#: tfrmtrainergenerator.cbstopplaying.caption
msgid "Stop playing when"
msgstr "何时停止播放"

#: tfrmtrainergenerator.cbsupportcheatengine.caption
msgctxt "TFRMTRAINERGENERATOR.CBSUPPORTCHEATENGINE.CAPTION"
msgid "Don't support Cheat Engine (or yourself)"
msgstr "不支持 Cheat Engine (或你自己设计界面)"

#: tfrmtrainergenerator.cbused3dhook.caption
msgid "Use Direct3D Hook"
msgstr "使用 Direct3D 钩子"

#: tfrmtrainergenerator.cetrainersavedialog.title
msgctxt "tfrmtrainergenerator.cetrainersavedialog.title"
msgid "Save script as"
msgstr "脚本另存为"

#: tfrmtrainergenerator.ctsavedialog.title
msgctxt "TFRMTRAINERGENERATOR.CTSAVEDIALOG.TITLE"
msgid "Save script as"
msgstr "脚本另存为"

#: tfrmtrainergenerator.edtcaption.text
msgid "My trainer"
msgstr "我的修改器"

#: tfrmtrainergenerator.edtfreezeinterval.text
msgctxt "tfrmtrainergenerator.edtfreezeinterval.text"
msgid "250"
msgstr "250"

#: tfrmtrainergenerator.exesavedialog.title
msgctxt "TFRMTRAINERGENERATOR.EXESAVEDIALOG.TITLE"
msgid "Save script as"
msgstr "脚本另存为"

#: tfrmtrainergenerator.groupbox2.caption
msgid "Automated gui config options"
msgstr "自动化图形界面配置选项"

#: tfrmtrainergenerator.label1.caption
msgid "Freeze interval (in milliseconds)"
msgstr "锁定时间间隔 (单位:毫秒)"

#: tfrmtrainergenerator.label2.caption
msgid "Processname"
msgstr "程序名称"

#: tfrmtrainergenerator.label3.caption
msgid "About text:"
msgstr "关于文本:"

#: tfrmtrainergenerator.label4.caption
msgid "Title"
msgstr "标题"

#: tfrmtrainergenerator.label5.caption
msgctxt "TFRMTRAINERGENERATOR.LABEL5.CAPTION"
msgid "Output"
msgstr "输出"

#: tfrmtrainergenerator.label6.caption
msgid "Tip: The trainer generator is a tool for beginners. It is recommended to learn lua and the formdesigner and save as .exe manually to experience the full power of a Cheat Engine trainer"
msgstr "提示: 修改器生成器是一个适合初学者的工具. 建议学习 LUA 和 窗口设计 并另存为可执行程序来动手体验Cheat Engine修改器的全部功能"

#: tfrmtrainergenerator.lblactivatesound.caption
msgctxt "TFRMTRAINERGENERATOR.LBLACTIVATESOUND.CAPTION"
msgid "Activate"
msgstr "激活"

#: tfrmtrainergenerator.lbldeactivatesound.caption
msgctxt "TFRMTRAINERGENERATOR.LBLDEACTIVATESOUND.CAPTION"
msgid "Deactivate"
msgstr "禁用"

#: tfrmtrainergenerator.lvcheats.columns[0].caption
msgctxt "TFRMTRAINERGENERATOR.LVCHEATS.COLUMNS[0].CAPTION"
msgid "Hotkey"
msgstr "热键"

#: tfrmtrainergenerator.lvcheats.columns[1].caption
msgctxt "TFRMTRAINERGENERATOR.LVCHEATS.COLUMNS[1].CAPTION"
msgid "Description"
msgstr "描述"

#: tfrmtrainergenerator.menuitem1.caption
msgid "Rebuild Trainer Cheatlist"
msgstr "重建修改器金手指列表"

#: tfrmtrainergenerator.menuitem2.caption
msgid "Only generate the script"
msgstr "仅生成脚本"

#: tfrmtrainergenerator.miedithotkey.caption
msgctxt "TFRMTRAINERGENERATOR.MIEDITHOTKEY.CAPTION"
msgid "Edit"
msgstr "编辑"

#: tfrmtrainergenerator.opendialog2.title
msgid "Open icon file"
msgstr "打开图标文件"

#: tfrmtrainergenerator.rbstopwhenattached.caption
msgid "Game has been attached"
msgstr "已附加游戏"

#: tfrmtrainergenerator.rbstopwhenfocuslost.caption
msgid "Trainer loses focus"
msgstr "修改器失去焦点"

#: tfrmultimap.btnexecuted.caption
msgctxt "tfrmultimap.btnexecuted.caption"
msgid "Code has been executed"
msgstr "代码已经运行"

#: tfrmultimap.btnfiltercallcount.caption
msgctxt "tfrmultimap.btnfiltercallcount.caption"
msgid "Filter out routine(s) where callcount is NOT"
msgstr "代码没有调用计数"

#: tfrmultimap.btnfiltermodule.caption
msgctxt "tfrmultimap.btnfiltermodule.caption"
msgid "Filter out routines not in a specific module"
msgstr "代码没有在指定的模块"

#: tfrmultimap.btnnotcalled.caption
msgid "Filter out routine(s) that where not CALL'ed"
msgstr "代码没有调用 CALL"

#: tfrmultimap.btnnotexecuted.caption
msgctxt "tfrmultimap.btnnotexecuted.caption"
msgid "Code did not get executed"
msgstr "代码没有被运行"

#: tfrmultimap.btnpause.caption
msgid "Pause/Resume"
msgstr "暂停/恢复"

#: tfrmultimap.btnresetcount.caption
msgctxt "tfrmultimap.btnresetcount.caption"
msgid "Reset Count"
msgstr "重置计数"

#: tfrmultimap.btnret.caption
msgid "Filter out return from RET's"
msgstr "代码返回一次 RET"

#: tfrmultimap.btnstart.caption
msgctxt "TFRMULTIMAP.BTNSTART.CAPTION"
msgid "Start"
msgstr "起始"

#: tfrmultimap.btnstop.caption
msgctxt "TFRMULTIMAP.BTNSTOP.CAPTION"
msgid "Stop"
msgstr "停止"

#: tfrmultimap.button5.caption
msgctxt "tfrmultimap.button5.caption"
msgid "Show matching routines"
msgstr "显示匹配的进程"

#: tfrmultimap.button6.caption
msgctxt "tfrmultimap.button6.caption"
msgid "Reset"
msgstr "重置"

#: tfrmultimap.button6.hint
msgctxt "tfrmultimap.button6.hint"
msgid "If you lost track of what happened, use this. It will set execution count back to 0 of all the branches it has found"
msgstr "如果你不知道发生了什么, 就使用这个. 它将设置所发现的所有分支并将执行计数返回为0"

#: tfrmultimap.caption
msgctxt "tfrmultimap.caption"
msgid "Ultimap"
msgstr "Ultimap"

#: tfrmultimap.cbfilterfuturepaths.caption
msgid "Filter future branches"
msgstr "过滤掉未来的分支"

#: tfrmultimap.cbfilterfuturepaths.hint
msgctxt "tfrmultimap.cbfilterfuturepaths.hint"
msgid "When checked (default) will mark all subsequent paths after this as filtered out"
msgstr "勾选后(默认)将会视为过滤掉后面的所有路径"

#: tfrmultimap.cbfilteroutnewentries.caption
msgid "Mark all new branches as filtered out"
msgstr "标记所有新分支为过滤掉"

#: tfrmultimap.cbfilteroutnewentries.hint
msgctxt "tfrmultimap.cbfilteroutnewentries.hint"
msgid "When this checkbox is ticked all newly added code entry points will be marked as entries that do not match the filters"
msgstr "勾选后所有新添加的代码的入口点将会视为与过滤器不匹配的条目"

#: tfrmultimap.cblogtofile.caption
msgid "Log directly to file"
msgstr "将日志记录到文件"

#: tfrmultimap.cbparsedata.caption
msgid "Parse data"
msgstr "解析数据"

#: tfrmultimap.cbpreemptiveflush.caption
msgid "Preemptive flushing"
msgstr "优先刷新"

#: tfrmultimap.cbpreemptiveflush.hint
msgid "When enabled Cheat Engine will flush the data for each cpu instead of only when the buffer is full"
msgstr "启用后,Cheat Engine 将为每个cpu刷新数据,而不是仅在缓冲区满了才刷新数据"

#: tfrmultimap.edit1.text
msgctxt "TFRMULTIMAP.EDIT1.TEXT"
msgid "1"
msgstr "1"

#: tfrmultimap.edtbufsize.text
msgid "4194304"
msgstr "4194304"

#: tfrmultimap.edtfilename.text
msgid "c:\\log.dat"
msgstr "c:\\log.dat"

#: tfrmultimap.edtworkercount.text
msgctxt "TFRMULTIMAP.EDTWORKERCOUNT.TEXT"
msgid "1"
msgstr "1"

#: tfrmultimap.label1.caption
msgid "Worker threads"
msgstr "工作线程"

#: tfrmultimap.label2.caption
msgid "Event buffer size"
msgstr "事件缓冲区大小"

#: tfrmultimap.label3.caption
msgctxt "TFRMULTIMAP.LABEL3.CAPTION"
msgid "0"
msgstr "0"

#: tfrmultimap.label4.caption
msgctxt "TFRMULTIMAP.LABEL4.CAPTION"
msgid "Label4"
msgstr "Label4"

#: tfrmultimap.label5.caption
msgid "Banch targets:"
msgstr "Banch 目标:"

#: tfrmultimap.label6.caption
msgid "General tip: \"Pause/Resume\", \"Code did not get executed\" and \"Code has been executed\" can be rightclicked and assigned a hotkey for use inside games so you don't have to alt tab out"
msgstr "友情提示: \"暂停/恢复\", \"代码没有被运行\" 和 \"代码已经运行\" 可以按鼠标右键并分配一个游戏内使用的热键,所以你不必用Alt+Tab切换"

#: tfrmultimap.lbllastfilterresult.caption
msgctxt "tfrmultimap.lbllastfilterresult.caption"
msgid "Last filter results:"
msgstr "最后过滤的结果:"

#: tfrmultimap.listview1.columns[0].caption
msgctxt "tfrmultimap.listview1.columns[0].caption"
msgid "Address To"
msgstr "地址到"

#: tfrmultimap.listview1.columns[1].caption
msgctxt "tfrmultimap.listview1.columns[1].caption"
msgid "Last origin"
msgstr "最后的原始地址"

#: tfrmultimap.listview1.columns[2].caption
msgctxt "TFRMULTIMAP.LISTVIEW1.COLUMNS[2].CAPTION"
msgid "Count"
msgstr "计数"

#: tfrmultimap.menuitem1.caption
msgid "Add selection to code list"
msgstr "选择的代码添加到列表"

#: tfrmultimap.menuitem2.caption
msgid "Flush buffers"
msgstr "刷新缓冲区"

#: tfrmultimap.miremovehotkey.caption
msgid "Remove hotkey"
msgstr "移除热键"

#: tfrmultimap.misethotkey.caption
msgid "Set hotkey"
msgstr "设置热键"

#: tfrmultimap.panel2.caption
msgctxt "tfrmultimap.panel2.caption"
msgid "Panel2"
msgstr "Panel2"

#: tfrmultimap2.btnaddrange.caption
msgctxt "TFRMULTIMAP2.BTNADDRANGE.CAPTION"
msgid "Add range"
msgstr "添加范围"

#: tfrmultimap2.btncancelfilter.caption
msgid "Cancel filter operation"
msgstr "取消过滤操作"

#: tfrmultimap2.btnexecuted.caption
msgctxt "TFRMULTIMAP2.BTNEXECUTED.CAPTION"
msgid "Code has been executed"
msgstr "代码已被执行"

#: tfrmultimap2.btnfiltercallcount.caption
msgctxt "TFRMULTIMAP2.BTNFILTERCALLCOUNT.CAPTION"
msgid "Filter out instructions where callcount is NOT"
msgstr "在调用计数是NOT的时候过滤掉指令"

#: tfrmultimap2.btnfiltermodule.caption
msgctxt "TFRMULTIMAP2.BTNFILTERMODULE.CAPTION"
msgid "Filter out instructions not in a specific range"
msgstr "在特定范围内不使用过滤指令"

#: tfrmultimap2.btnnotcalled.caption
msgid "Filter out everything except CALL"
msgstr "过滤掉除了CALL以外的一切"

#: tfrmultimap2.btnnotexecuted.caption
msgctxt "TFRMULTIMAP2.BTNNOTEXECUTED.CAPTION"
msgid "Code did not get executed"
msgstr "代码没有被执行"

#: tfrmultimap2.btnrecordpause.caption
msgid "Record / Pause"
msgstr "记录 / 暂停"

#: tfrmultimap2.btnreset.caption
msgctxt "TFRMULTIMAP2.BTNRESET.CAPTION"
msgid "Reset"
msgstr "重置"

#: tfrmultimap2.btnreset.hint
msgctxt "TFRMULTIMAP2.BTNRESET.HINT"
msgid "If you lost track of what happened, use this. It will set execution count back to 0 of all the branches it has found"
msgstr "如果你不知道发生了什么, 就使用这个. 它将会设置所发现的所有分支并将执行计数返回为0"

#: tfrmultimap2.btnresetcount.caption
msgctxt "TFRMULTIMAP2.BTNRESETCOUNT.CAPTION"
msgid "Reset Count"
msgstr "重置计数"

#: tfrmultimap2.btnshowresults.caption
msgctxt "TFRMULTIMAP2.BTNSHOWRESULTS.CAPTION"
msgid "Show the current valid instruction list"
msgstr "显示当前有效的指令"

#: tfrmultimap2.caption
msgctxt "tfrmultimap2.caption"
msgid "Ultimap 2"
msgstr "Ultimap 2"

#: tfrmultimap2.cbautoprocess.caption
msgctxt "TFRMULTIMAP2.CBAUTOPROCESS.CAPTION"
msgid "Autoprocess tracefiles"
msgstr "自动处理 tracefiles"

#: tfrmultimap2.cbautoprocess.hint
msgid "When enabled this will check the filesize every few seconds, and will process the files when they have grown beyond the given size."
msgstr "当启用后每隔几秒就会检测一次文件, 当文件超出指定大小后就会处理文件."

#: tfrmultimap2.cbdontdeletetracefiles.caption
msgid "Don't delete trace files"
msgstr "不删除跟踪文件"

#: tfrmultimap2.cbdontdeletetracefiles.hint
msgid "Normally Cheat Engine will delete the trace files after they have been processed. But if you wish to save them for your own use, check this box"
msgstr "通常 Cheat Engine 会在处理之后删除跟踪文件. 但如果你想保存起来以便下次使用, 请勾选此项"

#: tfrmultimap2.cbfilterfuturepaths.caption
msgid "Exclude new addresses"
msgstr "排除新地址"

#: tfrmultimap2.cbfilterfuturepaths.hint
msgctxt "TFRMULTIMAP2.CBFILTERFUTUREPATHS.HINT"
msgid "When checked (default) will mark all subsequent paths after this as filtered out"
msgstr "勾选后(默认)将会视为过滤掉后续的所有路径"

#: tfrmultimap2.cbfilteroutnewentries.caption
msgid "Mark all new addresses as filtered out"
msgstr "将所有新地址标记为过滤掉"

#: tfrmultimap2.cbfilteroutnewentries.hint
msgctxt "TFRMULTIMAP2.CBFILTEROUTNEWENTRIES.HINT"
msgid "When this checkbox is ticked all newly added code entry points will be marked as entries that do not match the filters"
msgstr "勾选后所有新添加的代码的入口点将会视为与过滤器不匹配的条目"

#: tfrmultimap2.cbparsetotextfile.caption
msgid "Parse trace to textfile"
msgstr "解析跟踪文本文件"

#: tfrmultimap2.cbparsetotextfile.hint
msgid "When enabled will write the sequence of events as they happen as plain text to a file called cpu#trace.txt."
msgstr "启用后会将调用CPU事件顺序写入trace.txt"

#: tfrmultimap2.cbpausetargetwhileprocessing.caption
msgid "Pause target while processing"
msgstr "暂停正在处理的进程"

#: tfrmultimap2.cbtraceinterval.caption
msgctxt "TFRMULTIMAP2.CBTRACEINTERVAL.CAPTION"
msgid "every "
msgstr "每一 "

#: tfrmultimap2.cbwhenfilesizeabove.caption
msgctxt "TFRMULTIMAP2.CBWHENFILESIZEABOVE.CAPTION"
msgid "when filesize above"
msgstr "当文件大小超过"

#: tfrmultimap2.detargetfolder.hint
msgid "The folder to store the processor trace files. "
msgstr "进程的跟踪文件保存到. "

#: tfrmultimap2.detargetfolder.text
msgctxt "tfrmultimap2.detargetfolder.text"
msgid "d:\\lotsofspace"
msgstr "d:\\lotsofspace"

#: tfrmultimap2.detextout.hint
msgid "The folder to store the trace output to"
msgstr "进程的跟踪文件保存到"

#: tfrmultimap2.detextout.text
msgctxt "TFRMULTIMAP2.DETEXTOUT.TEXT"
msgid "d:\\lotsofspace"
msgstr "d:\\lotsofspace"

#: tfrmultimap2.edtbufsize.text
msgid "16384"
msgstr "16384"

#: tfrmultimap2.edtcallcount.text
msgctxt "TFRMULTIMAP2.EDTCALLCOUNT.TEXT"
msgid "1"
msgstr "1"

#: tfrmultimap2.edtflushinterval.text
msgctxt "TFRMULTIMAP2.EDTFLUSHINTERVAL.TEXT"
msgid "10"
msgstr "10"

#: tfrmultimap2.edtmaxfilesize.text
msgid "2048"
msgstr "2048"

#: tfrmultimap2.gbrange.caption
msgid "Ranges: (Empty for all) (Max 0) "
msgstr "范围: (全部为空) (最大 0) "

#: tfrmultimap2.label1.caption
msgctxt "tfrmultimap2.label1.caption"
msgid "Paused"
msgstr "已暂停"

#: tfrmultimap2.label2.caption
msgid "MB"
msgstr "MB"

#: tfrmultimap2.label3.caption
msgctxt "TFRMULTIMAP2.LABEL3.CAPTION"
msgid "0"
msgstr "0"

#: tfrmultimap2.label4.caption
msgctxt "TFRMULTIMAP2.LABEL4.CAPTION"
msgid "Seconds"
msgstr "秒"

#: tfrmultimap2.lblbufferspercpu.caption
msgid "Buffer per CPU"
msgstr "CPU 缓冲"

#: tfrmultimap2.lblipcount.caption
msgctxt "tfrmultimap2.lblipcount.caption"
msgid "Instruction Pointer List Size:"
msgstr "指令/指针列表大小:"

#: tfrmultimap2.lblkb.caption
msgctxt "TFRMULTIMAP2.LBLKB.CAPTION"
msgid "KB"
msgstr "KB"

#: tfrmultimap2.lbllastfilterresult.caption
msgctxt "TFRMULTIMAP2.LBLLASTFILTERRESULT.CAPTION"
msgid "Last filter results:"
msgstr "最后过滤的结果:"

#: tfrmultimap2.listview1.columns[0].caption
msgctxt "TFRMULTIMAP2.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tfrmultimap2.listview1.columns[1].caption
msgctxt "TFRMULTIMAP2.LISTVIEW1.COLUMNS[1].CAPTION"
msgid "Count"
msgstr "计数"

#: tfrmultimap2.listview1.columns[2].caption
msgctxt "TFRMULTIMAP2.LISTVIEW1.COLUMNS[2].CAPTION"
msgid "Invalidated"
msgstr "Invalidated"

#: tfrmultimap2.menuitem1.caption
msgctxt "TFRMULTIMAP2.MENUITEM1.CAPTION"
msgid "Copy selection to clipboard"
msgstr "复制选中到剪贴板"

#: tfrmultimap2.mirangedeleteall.caption
msgctxt "TFRMULTIMAP2.MIRANGEDELETEALL.CAPTION"
msgid "Clear List"
msgstr "清除列表"

#: tfrmultimap2.mirangedeleteselected.caption
msgctxt "TFRMULTIMAP2.MIRANGEDELETESELECTED.CAPTION"
msgid "Delete Selected"
msgstr "删除选中"

#: tfrmultimap2.panel2.caption
msgctxt "TFRMULTIMAP2.PANEL2.CAPTION"
msgid "Panel2"
msgstr "Panel2"

#: tfrmultimap2.rblogtofolder.caption
msgid "Process data later"
msgstr "以后再处理数据"

#: tfrmultimap2.rblogtofolder.hint
msgid "CE will write the results to disk as quickly as possible but won't process it until the recording is stopped, or a filter function is being used."
msgstr "CE会尽快将结果写入磁盘，但不会去处理它. 除非停止记录或者使用过滤器功能."

#: tfrmultimap2.rbruntimeparsing.caption
msgid "Process data while recording"
msgstr "正在记录进程数据"

#: tfrmultimap2.rbruntimeparsing.hint
msgid "Constantly process the data instead of waiting for the user's input"
msgstr "不断地处理数据，而不是等待用户的输入"

#: tfrmwatchlist.caption
msgid "Watch list"
msgstr "监视列表"

#: tfrmwatchlist.lvwatchlist.columns[0].caption
msgid "Address/Expression"
msgstr "地址/表达式"

#: tfrmwatchlist.lvwatchlist.columns[1].caption
msgctxt "TFRMWATCHLIST.LVWATCHLIST.COLUMNS[1].CAPTION"
msgid "Value"
msgstr "数值"

#: tfrmwatchlist.menuitem1.caption
msgctxt "TFRMWATCHLIST.MENUITEM1.CAPTION"
msgid "-"
msgstr "-"

#: tfrmwatchlist.menuitem2.caption
msgctxt "TFRMWATCHLIST.MENUITEM2.CAPTION"
msgid "-"
msgstr "-"

#: tfrmwatchlist.menuitem3.caption
msgid "Refresh values"
msgstr "刷新数值"

#: tfrmwatchlist.menuitem4.caption
msgid "Browse memory region"
msgstr "浏览内存区域"

#: tfrmwatchlist.miadditem.caption
msgctxt "TFRMWATCHLIST.MIADDITEM.CAPTION"
msgid "Add Item"
msgstr "添加项目"

#: tfrmwatchlist.micopytoclipboard.caption
msgid "Copy expression(s) to clipboard"
msgstr "表达式复制到剪贴板"

#: tfrmwatchlist.micopytoclipboardex.caption
msgid "Copy expression(s) and value(s) to clipboard"
msgstr "表达式/数值复制到剪贴板"

#: tfrmwatchlist.mideleteitems.caption
msgid "Delete Item(s)"
msgstr "删除项目"

#: tfrmwatchlist.miedititem.caption
msgid "Edit Item"
msgstr "编辑项目"

#: tfrmwatchlist.mipastefromclipboard.caption
msgid "Paste expression(s) from clipboard"
msgstr "从剪贴板粘贴表达式"

#: tfrmwatchlist.mirefresh.caption
msgctxt "TFRMWATCHLIST.MIREFRESH.CAPTION"
msgid "-"
msgstr "-"

#: tfrmwatchlistaddentry.btncancel.caption
msgctxt "TFRMWATCHLISTADDENTRY.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tfrmwatchlistaddentry.btnok.caption
msgctxt "TFRMWATCHLISTADDENTRY.BTNOK.CAPTION"
msgid "OK"
msgstr "确定"

#: tfrmwatchlistaddentry.caption
msgid "Add watcher entry"
msgstr "添加监视项目"

#: tfrmwatchlistaddentry.edtexpression.texthint
msgid "Expression"
msgstr "表达式"

#: tfrmwatchlistaddentry.rgtype.caption
msgctxt "TFRMWATCHLISTADDENTRY.RGTYPE.CAPTION"
msgid "Type"
msgstr "类型"

#: thotkeyform.bitbtn1.caption
msgctxt "THOTKEYFORM.BITBTN1.CAPTION"
msgid "OK"
msgstr "确定"

#: thotkeyform.btnapply.caption
msgctxt "THOTKEYFORM.BTNAPPLY.CAPTION"
msgid "Apply"
msgstr "应用"

#: thotkeyform.btncancel.caption
msgctxt "THOTKEYFORM.BTNCANCEL.CAPTION"
msgid "Cancel"
msgstr "取消"

#: thotkeyform.btncreatehotkey.caption
msgid "Create hotkey"
msgstr "创建热键"

#: thotkeyform.btnedithotkey.caption
msgid "Edit Hotkey"
msgstr "编辑热键"

#: thotkeyform.button2.caption
msgctxt "THOTKEYFORM.BUTTON2.CAPTION"
msgid "Clear"
msgstr "清除"

#: thotkeyform.caption
msgid "Set/Change hotkey"
msgstr "设置/更改热键"

#: thotkeyform.cbforceenglishactivate.caption
msgctxt "thotkeyform.cbforceenglishactivate.caption"
msgid "Force English"
msgstr "强制为英语"

#: thotkeyform.cbforceenglishactivate.hint
msgctxt "thotkeyform.cbforceenglishactivate.hint"
msgid "Tries to set the language of the windows speech synthesizer to English when speaking this. Won't speak if there is no English voice installed"
msgstr "Tries to set the language of the windows speech synthesizer to English when speaking this. Won't speak if there is no English voice installed"

#: thotkeyform.cbforceenglishdeactivate.caption
msgctxt "THOTKEYFORM.CBFORCEENGLISHDEACTIVATE.CAPTION"
msgid "Force English"
msgstr "强制为英语"

#: thotkeyform.cbforceenglishdeactivate.hint
msgctxt "THOTKEYFORM.CBFORCEENGLISHDEACTIVATE.HINT"
msgid "Tries to set the language of the windows speech synthesizer to English when speaking this. Won't speak if there is no English voice installed"
msgstr "Tries to set the language of the windows speech synthesizer to English when speaking this. Won't speak if there is no English voice installed"

#: thotkeyform.cbfreezedirection.text
msgctxt "thotkeyform.cbfreezedirection.text"
msgid "Toggle freeze"
msgstr "切换锁定/解锁"

#: thotkeyform.edtactivatetext.text
msgid "{MRDescription} Activated"
msgstr "{MRDescription} 已激活"

#: thotkeyform.edtdeactivatetext.text
msgid "{MRDescription} Deactivated"
msgstr "{MRDescription} 已禁用"

#: thotkeyform.edtdescription.hint
msgid "When autogenerating a trainer this field will be used as description"
msgstr "当修改器自动制作时此栏位将会用作描述"

#: thotkeyform.label1.caption
msgid "Type the keys you want to set the hotkey to"
msgstr "键入要设置的热键"

#: thotkeyform.label2.caption
msgid "Description (Optional)"
msgstr "描述(可选)"

#: thotkeyform.lblactivatesound.caption
msgctxt "THOTKEYFORM.LBLACTIVATESOUND.CAPTION"
msgid "Activate sound"
msgstr "激活声音"

#: thotkeyform.lbldeactivatesound.caption
msgctxt "THOTKEYFORM.LBLDEACTIVATESOUND.CAPTION"
msgid "Deactivate sound"
msgstr "禁用声音"

#: thotkeyform.listview1.columns[0].caption
msgctxt "THOTKEYFORM.LISTVIEW1.COLUMNS[0].CAPTION"
msgid "Hotkey"
msgstr "热键"

#: thotkeyform.listview1.columns[1].caption
msgid "On Hotkey"
msgstr "热键用于"

#: thotkeyform.listview1.columns[2].caption
msgctxt "THOTKEYFORM.LISTVIEW1.COLUMNS[2].CAPTION"
msgid "Value"
msgstr "数值"

#: thotkeyform.listview1.columns[3].caption
msgctxt "THOTKEYFORM.LISTVIEW1.COLUMNS[3].CAPTION"
msgid "Description"
msgstr "描述"

#: thotkeyform.miaddsound.caption
msgid "Add sounds"
msgstr "添加声音"

#: thotkeyform.midelete.caption
msgctxt "THOTKEYFORM.MIDELETE.CAPTION"
msgid "Delete"
msgstr "删除"

#: thotkeyform.tabsheet1.caption
msgid "TabSheet1"
msgstr "TabSheet1"

#: thotkeyform.tabsheet2.caption
msgid "TabSheet2"
msgstr "TabSheet2"

#: tinputboxtop.button1.caption
msgctxt "TINPUTBOXTOP.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tinputboxtop.button2.caption
msgctxt "TINPUTBOXTOP.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tinputboxtop.edit1.text
msgid "edit1"
msgstr "edit1"

#: tinputboxtop.label1.caption
msgctxt "TINPUTBOXTOP.LABEL1.CAPTION"
msgid "Label1"
msgstr "Label1"

#: tlgunit.rsyouwin
msgid "OMG, You must have cheated or don't have a life. (Although some people claim that thats the same)"
msgstr "天啊, 你不要在欺骗自已了. (尽管有些人声称这是一样的)"

#: tmainform.a1.caption
msgid "a"
msgstr "a"

#: tmainform.actautoassemble.caption
msgid "actAutoAssemble"
msgstr "actAutoAssemble"

#: tmainform.actmemoryview.caption
msgid "actMemoryView"
msgstr "actMemoryView"

#: tmainform.actopen.hint
msgid "Open a cheat table or unprotected cetrainer"
msgstr "打开CT表或未受保护的cetrainer"

#: tmainform.actopendissectstructure.caption
msgid "actOpenDissectStructure"
msgstr "actOpenDissectStructure"

#: tmainform.actopenluaengine.caption
msgid "actOpenLuaEngine"
msgstr "actOpenLuaEngine"

#: tmainform.actsave.hint
msgid "Save your current data. Save as .exe to create a standalone trainer"
msgstr "保存你目前的数据.另存为可执行程序以便创建一个独立的修改器"

#: tmainform.address1.caption
msgctxt "TMAINFORM.ADDRESS1.CAPTION"
msgid "Address"
msgstr "地址"

#: tmainform.advancedbutton.caption
msgctxt "TMAINFORM.ADVANCEDBUTTON.CAPTION"
msgid "Advanced Options"
msgstr "高级选项"

#: tmainform.advancedbutton.hint
msgid "Codelist and pause"
msgstr "代码表和暂停"

#: tmainform.andlabel.caption
msgctxt "TMAINFORM.ANDLABEL.CAPTION"
msgid "and"
msgstr "和"

#: tmainform.b1.caption
msgid "b"
msgstr "b"

#: tmainform.browsethismemoryarrea1.caption
msgctxt "TMAINFORM.BROWSETHISMEMORYARREA1.CAPTION"
msgid "Browse this memory region"
msgstr "浏览相关内存区域"

#: tmainform.browsethismemoryregion1.caption
msgctxt "TMAINFORM.BROWSETHISMEMORYREGION1.CAPTION"
msgid "Browse this memory region"
msgstr "浏览相关内存区域"

#: tmainform.browsethismemoryregioninthedisassembler1.caption
msgctxt "tmainform.browsethismemoryregioninthedisassembler1.caption"
msgid "Disassemble this memory region"
msgstr "反汇编此内存区域"

#: tmainform.btnaddaddressmanually.caption
msgid "Add Address Manually"
msgstr "手动添加地址"

#: tmainform.btnmemoryview.caption
msgid "Memory view"
msgstr "查看内存"

#: tmainform.btnnewscan.caption
msgctxt "TMAINFORM.BTNNEWSCAN.CAPTION"
msgid "First Scan"
msgstr "首次扫描"

#: tmainform.btnnextscan.caption
msgctxt "TMAINFORM.BTNNEXTSCAN.CAPTION"
msgid "Next Scan"
msgstr "再次扫描"

#: tmainform.btnsetspeedhack2.caption
msgctxt "TMAINFORM.BTNSETSPEEDHACK2.CAPTION"
msgid "Apply"
msgstr "应用"

#: tmainform.button2.caption
msgid "First Scan2"
msgstr "首次扫描 2"

#: tmainform.button4.caption
msgid "Next scan 2"
msgstr "再次扫描 2"

#: tmainform.c1.caption
msgid "c"
msgstr "c"

#: tmainform.calculatenewvaluepart21.caption
msgid "Recalculate new addresses"
msgstr "重新计算新的地址"

#: tmainform.caption
msgctxt "TMAINFORM.CAPTION"
msgid "Cheat Engine 7.0"
msgstr "Cheat Engine 7.0"

#: tmainform.cbcasesensitive.caption
msgctxt "TMAINFORM.CBCASESENSITIVE.CAPTION"
msgid "Case sensitive"
msgstr "区分大小写"

#: tmainform.cbcodepage.caption
msgctxt "tmainform.cbcodepage.caption"
msgid "Codepage"
msgstr "代码页"

#: tmainform.cbcopyonwrite.caption
msgid "CopyOnWrite"
msgstr "写时拷贝"

#: tmainform.cbcopyonwrite.hint
msgctxt "tmainform.cbcopyonwrite.hint"
msgid "Unchecked:"
msgstr "选中:同时扫描可写入时拷贝的内存. 取消选中:不扫描可写入时拷贝的内存"

#: tmainform.cbexecutable.caption
msgid "Executable"
msgstr "可执行"

#: tmainform.cbexecutable.hint
msgctxt "TMAINFORM.CBEXECUTABLE.HINT"
msgid "Unchecked:"
msgstr "选中:同时扫描可执行内存. 取消选中:不扫描可执行内存"

#: tmainform.cbfastscan.caption
msgid "Fast Scan"
msgstr "快速扫描"

#: tmainform.cbfastscan.hint
msgid "Speeds up the scan and decreases useless results by skipping unaligned memory, or only scans addresses ending with specific digits"
msgstr "通过跳过未对齐的内存以加快扫描并减少无用的结果,或只扫描以特定位数结束的地址"

#: tmainform.cbfloatsimple.caption
msgid "Simple values only"
msgstr "仅简单值"

#: tmainform.cbhexadecimal.caption
msgctxt "TMAINFORM.CBHEXADECIMAL.CAPTION"
msgid "Hex"
msgstr "Hex"

#: tmainform.cbhexadecimal.hint
msgid "When checked the value you type in is hexadecimal"
msgstr "勾选后输入值转换为十六进制"

#: tmainform.cbnot.caption
msgid "Not"
msgstr "逻辑"非"运算"

#: tmainform.cbnot.hint
msgid "When checked CE will find everything EXCEPT the value/range you've given"
msgstr "勾选后CE将会在查找时排除你给出的数值/范围"

#: tmainform.cbpausewhilescanning.caption
msgid "Pause the game while scanning"
msgstr "扫描时暂停游戏"

#: tmainform.cbpercentage.caption
msgid "Percent"
msgstr "百分比"

#: tmainform.cbspeedhack.caption
msgid "Enable Speedhack"
msgstr "开启变速精灵"

#: tmainform.cbspeedhack.hint
msgid "Enable speedhack"
msgstr "开启变速精灵"

#: tmainform.cbunicode.caption
msgctxt "TMAINFORM.CBUNICODE.CAPTION"
msgid "UTF-16"
msgstr "UTF-16"

#: tmainform.cbunrandomizer.caption
msgctxt "TMAINFORM.CBUNRANDOMIZER.CAPTION"
msgid "Unrandomizer"
msgstr "禁止随机"

#: tmainform.cbwritable.caption
msgid "Writable"
msgstr "可写"

#: tmainform.cbwritable.hint
msgctxt "TMAINFORM.CBWRITABLE.HINT"
msgid "Unchecked:"
msgstr "选中:同时扫描可写入的内存. 取消选中:不扫描可写入的内存"

#: tmainform.change1.caption
msgid "Change record"
msgstr "更改记录"

#: tmainform.changescript1.caption
msgctxt "TMAINFORM.CHANGESCRIPT1.CAPTION"
msgid "Change script"
msgstr "更改脚本"

#: tmainform.commentbutton.caption
msgid "Table Extras"
msgstr "附加注释"

#: tmainform.copy1.caption
msgctxt "TMAINFORM.COPY1.CAPTION"
msgid "Copy"
msgstr "复制"

#: tmainform.copy2.caption
msgctxt "TMAINFORM.COPY2.CAPTION"
msgid "Copy"
msgstr "复制"

#: tmainform.creategroup.caption
msgid "Create Header"
msgstr "创建表头"

#: tmainform.createprocess1.caption
msgctxt "TMAINFORM.CREATEPROCESS1.CAPTION"
msgid "Create Process"
msgstr "创建进程"

#: tmainform.cut1.caption
msgctxt "TMAINFORM.CUT1.CAPTION"
msgid "Cut"
msgstr "剪切"

#: tmainform.cut2.caption
msgctxt "TMAINFORM.CUT2.CAPTION"
msgid "Cut"
msgstr "剪切"

#: tmainform.d1.caption
msgid "d"
msgstr "d"

#: tmainform.deletethisrecord1.caption
msgid "Delete this record"
msgstr "删除记录"

#: tmainform.description1.caption
msgctxt "TMAINFORM.DESCRIPTION1.CAPTION"
msgid "Description"
msgstr "描述"

#: tmainform.e1.caption
msgid "e"
msgstr "e"

#: tmainform.edit3.caption
msgid "&Edit"
msgstr "编辑(&E)"

#: tmainform.editsh2.text
msgctxt "TMAINFORM.EDITSH2.TEXT"
msgid "1"
msgstr "1"

#: tmainform.edtalignment.text
msgctxt "TMAINFORM.EDTALIGNMENT.TEXT"
msgid "4"
msgstr "4"

#: tmainform.file1.caption
msgid "&File"
msgstr "文件(&F)"

#: tmainform.findoutwhataccessesthisaddress1.caption
msgctxt "tmainform.findoutwhataccessesthisaddress1.caption"
msgid "Find out what accesses this address"
msgstr "找出是什么访问了这个地址"

#: tmainform.forcerechecksymbols1.caption
msgctxt "TMAINFORM.FORCERECHECKSYMBOLS1.CAPTION"
msgid "Force recheck symbols"
msgstr "强制复查符号表"

#: tmainform.foundcountlabel.caption
msgctxt "TMAINFORM.FOUNDCOUNTLABEL.CAPTION"
msgid "0"
msgstr "0"

#: tmainform.foundcountlabel.hint
msgid "This shows the amount of addresses found"
msgstr "这里显示找到的地址总数"

#: tmainform.foundlist3.columns[0].caption
msgctxt "TMAINFORM.FOUNDLIST3.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tmainform.foundlist3.columns[1].caption
msgctxt "TMAINFORM.FOUNDLIST3.COLUMNS[1].CAPTION"
msgid "Value"
msgstr "当前值"

#: tmainform.foundlist3.columns[2].caption
msgctxt "TMAINFORM.FOUNDLIST3.COLUMNS[2].CAPTION"
msgid "Previous"
msgstr "先前值"

#: tmainform.foundlist3.hint
msgid "This list shows all the found addresses that matched your last scan"
msgstr "列表显示最后一次扫描匹配的所有地址"

#: tmainform.freezealladdresses2.caption
msgid "Toggle Selected Records"
msgstr "锁定/解锁选中的地址"

#: tmainform.fromaddress.text
msgctxt "TMAINFORM.FROMADDRESS.TEXT"
msgid "00000000"
msgstr "00000000"

#: tmainform.gbscanoptions.caption
msgid "Memory Scan Options"
msgstr "内存扫描选项"

#: tmainform.help1.caption
msgid "&Help"
msgstr "帮助(&H)"

#: tmainform.helpindex1.caption
msgid "Cheat Engine Help"
msgstr "Cheat Engine 帮助"

#: tmainform.hint
msgid " "
msgstr " "

#: tmainform.label1.caption
msgctxt "TMAINFORM.LABEL1.CAPTION"
msgid "Start"
msgstr "起始"

#: tmainform.label2.caption
msgctxt "TMAINFORM.LABEL2.CAPTION"
msgid "Stop"
msgstr "停止"

#: tmainform.label54.caption
msgctxt "TMAINFORM.LABEL54.CAPTION"
msgid "Speed"
msgstr "速度"

#: tmainform.label6.caption
msgctxt "tmainform.label6.caption"
msgid "Found:"
msgstr "结果:"

#: tmainform.lblcomparetosavedscan.caption
msgid "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
msgstr "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

#: tmainform.lblscantype.caption
msgid "Scan Type"
msgstr "扫描类型"

#: tmainform.lblsh0.caption
msgctxt "TMAINFORM.LBLSH0.CAPTION"
msgid "0"
msgstr "0"

#: tmainform.lblsh20.caption
msgctxt "tmainform.lblsh20.caption"
msgid "500"
msgstr "500"

#: tmainform.lblsigned.caption
msgctxt "TMAINFORM.LBLSIGNED.CAPTION"
msgid "This table has been signed by"
msgstr "此表单签署者是"

#: tmainform.lblvaluetype.caption
msgid "Value Type"
msgstr "数值类型"

#: tmainform.load1.caption
msgctxt "TMAINFORM.LOAD1.CAPTION"
msgid "Load"
msgstr "读取"

#: tmainform.menuitem1.caption
msgid "Select All"
msgstr "全选"

#: tmainform.menuitem10.caption
msgctxt "TMAINFORM.MENUITEM10.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem11.caption
msgctxt "TMAINFORM.MENUITEM11.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem12.caption
msgid "Cheat Engine Tutorial (64-Bit)"
msgstr "Cheat Engine 教程 (64位)"

#: tmainform.menuitem13.caption
msgctxt "TMAINFORM.MENUITEM13.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem19.caption
msgctxt "TMAINFORM.MENUITEM19.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem2.caption
msgid "New Item1"
msgstr "新建项目1"

#: tmainform.menuitem3.caption
msgctxt "TMAINFORM.MENUITEM3.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem4.caption
msgctxt "TMAINFORM.MENUITEM4.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem5.caption
msgctxt "TMAINFORM.MENUITEM5.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem6.caption
msgctxt "TMAINFORM.MENUITEM6.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.menuitem7.caption
msgid "Quit"
msgstr "退出"

#: tmainform.menuitem8.caption
msgctxt "TMAINFORM.MENUITEM8.CAPTION"
msgid "Open Process"
msgstr "打开进程"

#: tmainform.menuitem9.caption
msgid "Generate generic trainer lua script from table"
msgstr "从表单中生成通用修改器Lua脚本"

#: tmainform.mi3d.caption
msgid "D3D"
msgstr "D3D"

#: tmainform.miabout.caption
msgctxt "TMAINFORM.MIABOUT.CAPTION"
msgid "About"
msgstr "Cheat Engine 关于"

#: tmainform.miaddaddress.caption
msgid "Add selected addresses to the addresslist"
msgstr "将选中的地址添加到地址列表"

#: tmainform.miaddfile.caption
msgid "Add file"
msgstr "添加文件"

#: tmainform.miaddtab.caption
msgid "Add scan tab"
msgstr "新建扫描标签"

#: tmainform.miallowcollapse.caption
msgid "Allow left and right arrow keys to collapse and expand"
msgstr "允许以左/右箭头键折叠和展开项目"

#: tmainform.miasyncscript.caption
msgid "Execute asynchronous"
msgstr "异步执行"

#: tmainform.mibindactivation.caption
msgid "Activating this entry activates it's children"
msgstr "此项目激活后同时激活它的子项"

#: tmainform.mibinddeactivation.caption
msgid "Deactivating this entry deactivates it's children"
msgstr "此项目禁用后同时禁用它的子项"

#: tmainform.michangecolor.caption
msgid "Change Color"
msgstr "更改颜色"

#: tmainform.michangevalue.caption
msgid "Change value of selected addresses"
msgstr "改变已选中地址的数值"

#: tmainform.miclosetab.caption
msgid "Close tab"
msgstr "关闭标签"

#: tmainform.micompression.caption
msgctxt "TMAINFORM.MICOMPRESSION.CAPTION"
msgid "Compression"
msgstr "压缩率"

#: tmainform.micreateluaform.caption
msgid "Create form"
msgstr "创建窗体"

#: tmainform.midefinenewcustomtype.caption
msgid "Define new custom type (Auto Assembler)"
msgstr "定义新的\"自定义类型\" (自动汇编)"

#: tmainform.midefinenewcustomtypelua.caption
msgid "Define new custom type (LUA)"
msgstr "定义新的\"自定义类型\" (LUA)"

#: tmainform.mideletecustomtype.caption
msgid "Delete selected custom type"
msgstr "删除所选的\"自定义类型\""

#: tmainform.midisassemble.caption
msgctxt "TMAINFORM.MIDISASSEMBLE.CAPTION"
msgid "Disassemble this memory region"
msgstr "反汇编此内存区域"

#: tmainform.midisplay2byte.caption
msgctxt "TMAINFORM.MIDISPLAY2BYTE.CAPTION"
msgid "2 字节"
msgstr "2 字节"

#: tmainform.midisplay4byte.caption
msgctxt "TMAINFORM.MIDISPLAY4BYTE.CAPTION"
msgid "4 字节"
msgstr "4 字节"

#: tmainform.midisplay8byte.caption
msgctxt "TMAINFORM.MIDISPLAY8BYTE.CAPTION"
msgid "8 字节"
msgstr "8 字节"

#: tmainform.midisplaybyte.caption
msgctxt "TMAINFORM.MIDISPLAYBYTE.CAPTION"
msgid "Byte"
msgstr "字节"

#: tmainform.midisplaydefault.caption
msgctxt "TMAINFORM.MIDISPLAYDEFAULT.CAPTION"
msgid "Default"
msgstr "默认"

#: tmainform.midisplaydouble.caption
msgctxt "TMAINFORM.MIDISPLAYDOUBLE.CAPTION"
msgid "Double"
msgstr "双浮点"

#: tmainform.midisplayfloat.caption
msgctxt "TMAINFORM.MIDISPLAYFLOAT.CAPTION"
msgid "Float"
msgstr "单浮点"

#: tmainform.midisplayhex.caption
msgctxt "TMAINFORM.MIDISPLAYHEX.CAPTION"
msgid "Hexadecimal"
msgstr "十六进制"

#: tmainform.mieditcustomtype.caption
msgid "Edit selected custom type"
msgstr "编辑所选的自定义类型"

#: tmainform.mienablelcldebug.caption
msgid "Enable LCL Debug"
msgstr "启用 LCL 调试"

#: tmainform.miflfindwhataccesses.caption
msgctxt "TMAINFORM.MIFLFINDWHATACCESSES.CAPTION"
msgid "Find out what accesses this address"
msgstr "找出是什么访问了这个地址"

#: tmainform.miflfindwhatwrites.caption
msgctxt "TMAINFORM.MIFLFINDWHATWRITES.CAPTION"
msgid "Find out what writes to this address"
msgstr "找出是什么改写了这个地址"

#: tmainform.mifreezenegative.caption
msgid "Freeze Negative"
msgstr "锁定为负"

#: tmainform.mifreezepositive.caption
msgid "Freeze Positive"
msgstr "锁定为正"

#: tmainform.migeneratepointermap.caption
msgctxt "tmainform.migeneratepointermap.caption"
msgid "Generate pointermap"
msgstr "生成 指针映射集"

#: tmainform.migroupconfig.caption
msgid "Group config"
msgstr "群组配置"

#: tmainform.mihelp.caption
msgctxt "TMAINFORM.MIHELP.CAPTION"
msgid "&Help"
msgstr "帮助(&H)"

#: tmainform.mihidechildren.caption
msgid "Hide children when deactivated"
msgstr "此项目未激活时隐藏子项"

#: tmainform.mihookd3d.caption
msgid "Hook Direct3D"
msgstr "钩住 Direct3D"

#: tmainform.milanguages.caption
msgctxt "TMAINFORM.MILANGUAGES.CAPTION"
msgid "Languages"
msgstr "语言"

#: tmainform.milockmouseingame.caption
msgid "Lock mouse in game window"
msgstr "在游戏窗口中锁定鼠标"

#: tmainform.miluaformsseperator.caption
msgctxt "TMAINFORM.MILUAFORMSSEPERATOR.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.mimanualexpandcollapse.caption
msgid "Manual expand/collapse"
msgstr "手动展开/折叠"

#: tmainform.minetwork.caption
msgctxt "tmainform.minetwork.caption"
msgid "Network"
msgstr "联网"

#: tmainform.miopenfile.caption
msgctxt "tmainform.miopenfile.caption"
msgid "Open File"
msgstr "打开文件"

#: tmainform.mipresetall.caption
msgid "Preset: Scan all memory"
msgstr "预设: 扫描所有内存"

#: tmainform.mipresetwritable.caption
msgid "Preset: Scan writable memory"
msgstr "预设: 扫描可写内存"

#: tmainform.mirecursivesetvalue.caption
msgid "Setting a value to this entry sets same value to children"
msgstr "设置了一个数值后自动将其子项也设为相同的数值"

#: tmainform.mirenametab.caption
msgctxt "TMAINFORM.MIRENAMETAB.CAPTION"
msgid "Rename"
msgstr "重命名"

#: tmainform.miresetrange.caption
msgid "Reset range"
msgstr "重置扫描范围"

#: tmainform.miresyncformswithlua.caption
msgid "Resynchronize forms with Lua"
msgstr "重新同步Lua"

#: tmainform.misave.caption
msgctxt "TMAINFORM.MISAVE.CAPTION"
msgid "Save"
msgstr "保存"

#: tmainform.misavefile.caption
msgid "Save File"
msgstr "保存文件"

#: tmainform.misavescanresults.caption
msgid "Save current scanresults"
msgstr "保存当前扫描结果"

#: tmainform.miscandirtyonly.caption
msgid "Scan changed regions only"
msgstr "仅扫描变动的区域"

#: tmainform.miscanpagedonly.caption
msgid "Scan paged (physical) memory only"
msgstr "仅扫描分页(物理)内存"

#: tmainform.misetcrosshair.caption
msgid "Set custom crosshair"
msgstr "设置自定义十字线"

#: tmainform.misetdropdownoptions.caption
msgid "Set/Change dropdown selection options"
msgstr "设置/更改下拉列表项"

#: tmainform.misetupsnapshotkeys.caption
msgid "Start and configure snapshot recording"
msgstr "启动和配置快照记录"

#: tmainform.mishowasbinary.caption
msgctxt "TMAINFORM.MISHOWASBINARY.CAPTION"
msgid "Show as binary"
msgstr "以二进制显示"

#: tmainform.mishowassigned.caption
msgid "Show as signed"
msgstr "显示带符号的"

#: tmainform.mishowcustomtypedebug.caption
msgid "Show custom type debug info"
msgstr "显示自定义类型的调试信息"

#: tmainform.mishowluascript.caption
msgid "Show Cheat Table Lua Script"
msgstr "显示CT表的Lua脚本"

#: tmainform.mishowpreviousvalue.caption
msgid "Show previous value column"
msgstr "显示先前的数值列"

#: tmainform.misigntable.caption
msgid "Sign table"
msgstr "符号表"

#: tmainform.misnapshothandler.caption
msgctxt "tmainform.misnapshothandler.caption"
msgid "Snapshot handler"
msgstr "快照处理程序"

#: tmainform.mitable.caption
msgid "Table"
msgstr "表单"

#: tmainform.mitablistseperator.caption
msgctxt "TMAINFORM.MITABLISTSEPERATOR.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.mitutorial.caption
msgid "Cheat Engine Tutorial"
msgstr "Cheat Engine 教程"

#: tmainform.miundovalue.caption
msgid "Undo last edit"
msgstr "撤消上一次的编辑"

#: tmainform.miwireframe.caption
msgid "Toggle wireframe mode"
msgstr "切换线框模式"

#: tmainform.mizbuffer.caption
msgid "Toggle disabled zbuffer"
msgstr "切换禁用缓冲"

#: tmainform.mizeroterminate.caption
msgid "Zero-Terminate string"
msgstr "零终止符字符串"

#: tmainform.n1.caption
msgctxt "TMAINFORM.N1.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.n4.caption
msgctxt "TMAINFORM.N4.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.n5.caption
msgctxt "TMAINFORM.N5.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.n6.caption
msgctxt "TMAINFORM.N6.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.n7.caption
msgctxt "TMAINFORM.N7.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.n8.caption
msgctxt "TMAINFORM.N8.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.new1.caption
msgctxt "TMAINFORM.NEW1.CAPTION"
msgid "Clear list"
msgstr "清空列表"

#: tmainform.ools1.caption
msgid "&Tools"
msgstr "工具(&T)"

#: tmainform.openprocess1.caption
msgid "Open Process window"
msgstr "打开进程窗口"

#: tmainform.paste1.caption
msgctxt "TMAINFORM.PASTE1.CAPTION"
msgid "Paste"
msgstr "粘贴"

#: tmainform.paste2.caption
msgctxt "TMAINFORM.PASTE2.CAPTION"
msgid "Paste"
msgstr "粘贴"

#: tmainform.plugins1.caption
msgctxt "TMAINFORM.PLUGINS1.CAPTION"
msgid "Plugins"
msgstr "插件"

#: tmainform.plugins2.caption
msgid "P&lugins"
msgstr "插件(&L)"

#: tmainform.pointerscanforthisaddress1.caption
msgid "Pointer scan for this address"
msgstr "对这个地址进行指针扫描"

#: tmainform.process1.caption
msgid "&Process"
msgstr "进程列表(&P)"

#: tmainform.processlabel.caption
msgid "No Process Selected"
msgstr "未选择进程"

#: tmainform.progressbar.hint
msgid "This shows how far Cheat Engine is with searching"
msgstr "这里显示 Cheat Engine 搜索的进度"

#: tmainform.rbbit.caption
msgid "Bits"
msgstr "二进制"

#: tmainform.rbdec.caption
msgctxt "TMAINFORM.RBDEC.CAPTION"
msgid "Decimal"
msgstr "十进制"

#: tmainform.rbfsmaligned.caption
msgid "Alignment"
msgstr "对齐"

#: tmainform.rbfsmaligned.hint
msgid "When set and fastscan is enabled will scan the memory address if it is dividable by the given value"
msgstr "当设定并且开启了快速扫描后将扫描内存地址(如果其给出的值是可分割的)"

#: tmainform.rbfsmlastdigts.caption
msgid "Last Digits"
msgstr "最后位数"

#: tmainform.rbfsmlastdigts.hint
msgid "When set and fastscan is enabled will only scan and return addresses that end with the given digits"
msgstr "当设定并且开启了快速扫描后将只扫描和返回以特定位数结束的地址"

#: tmainform.removeselectedaddresses1.caption
msgctxt "TMAINFORM.REMOVESELECTEDADDRESSES1.CAPTION"
msgid "Remove selected addresses"
msgstr "移除选中的地址"

#: tmainform.rt1.caption
msgid "Rounded (default)"
msgstr "舍入(默认)"

#: tmainform.rt2.caption
msgid "Rounded (extreme)"
msgstr "舍入(取极值)"

#: tmainform.rt3.caption
msgid "Truncated"
msgstr "舍入(截断)"

#: tmainform.save1.caption
msgctxt "TMAINFORM.SAVE1.CAPTION"
msgid "Save As..."
msgstr "另存为..."

#: tmainform.sbopenprocess.hint
msgid "Select a process to open"
msgstr "请先选择一个进程打开"

#: tmainform.scantext.caption
msgctxt "tmainform.scantext.caption"
msgid "Scan Value"
msgstr "扫描数值"

#: tmainform.scantext2.caption
msgctxt "TMAINFORM.SCANTEXT2.CAPTION"
msgid "Scan Value"
msgstr "扫描数值"

#: tmainform.selectallitems1.caption
msgid "Select all items"
msgstr "选择所有项目"

#: tmainform.sep1.caption
msgctxt "TMAINFORM.SEP1.CAPTION"
msgid "-"
msgstr "-"

#: tmainform.setbreakpoint1.caption
msgctxt "tmainform.setbreakpoint1.caption"
msgid "Find out what writes to this address"
msgstr "找出是什么改写了这个地址"

#: tmainform.sethotkey1.caption
msgid "Assign Hotkey"
msgstr "指定热键"

#: tmainform.settings1.caption
msgctxt "TMAINFORM.SETTINGS1.CAPTION"
msgid "Settings"
msgstr "设置"

#: tmainform.settingsbutton.caption
msgctxt "tmainform.settingsbutton.caption"
msgid "Settings"
msgstr "设置"

#: tmainform.showashexadecimal1.caption
msgctxt "TMAINFORM.SHOWASHEXADECIMAL1.CAPTION"
msgid "Show as hexadecimal"
msgstr "以十六进制显示"

#: tmainform.smarteditaddresses1.caption
msgid "Smart edit address(es)"
msgstr "智能编辑地址"

#: tmainform.speedbutton2.hint
msgid "Delete all addresses from the list"
msgstr "删除列表中所有地址"

#: tmainform.speedbutton3.hint
msgid "Copy all selected items to the address list"
msgstr "复制所有选中项目到地址列表"

#: tmainform.toaddress.text
msgid "ffffffff"
msgstr "ffffffff"

#: tmainform.type1.caption
msgctxt "TMAINFORM.TYPE1.CAPTION"
msgid "Type"
msgstr "类型"

#: tmainform.undoscan.caption
msgid "Undo Scan"
msgstr "撤销扫描"

#: tmainform.value1.caption
msgctxt "TMAINFORM.VALUE1.CAPTION"
msgid "Value"
msgstr "数值"

#: tmemorybrowser.addthisaddresstothelist1.caption
msgid "Add this address to the list"
msgstr "将此地址添加到列表中"

#: tmemorybrowser.aflabel.caption
msgctxt "TMEMORYBROWSER.AFLABEL.CAPTION"
msgid "AF 0"
msgstr "AF 0"

#: tmemorybrowser.all1.caption
msgid "Full stack"
msgstr "全栈"

#: tmemorybrowser.allocatenonpagedmemory1.caption
msgid "Allocate nonpaged memory"
msgstr "分配未分页内存"

#: tmemorybrowser.assemble1.caption
msgid "Assemble"
msgstr "汇编"

#: tmemorybrowser.assemblycode1.caption
msgid "Find assembly code"
msgstr "查找汇编码"

#: tmemorybrowser.autoinject1.caption
msgid "Auto Assemble"
msgstr "自动汇编"

#: tmemorybrowser.back1.caption
msgctxt "tmemorybrowser.back1.caption"
msgid "Back"
msgstr "返回"

#: tmemorybrowser.break1.caption
msgctxt "TMEMORYBROWSER.BREAK1.CAPTION"
msgid "Break"
msgstr "中断"

#: tmemorybrowser.breakandtraceinstructions1.caption
msgid "Break and trace instructions"
msgstr "在此中断并跟踪"

#: tmemorybrowser.breakpointlist1.caption
msgid "Breakpointlist"
msgstr "断点列表"

#: tmemorybrowser.caption
msgid "Memory Viewer"
msgstr "内存浏览器"

#: tmemorybrowser.cflabel.caption
msgctxt "TMEMORYBROWSER.CFLABEL.CAPTION"
msgid "CF 0"
msgstr "CF 0"

#: tmemorybrowser.change1.caption
msgctxt "TMEMORYBROWSER.CHANGE1.CAPTION"
msgid "Edit"
msgstr "编辑"

#: tmemorybrowser.changestateofregisteratthislocation1.caption
msgid "Change register at this location"
msgstr "更改当前位置的寄存器"

#: tmemorybrowser.continueanddetachdebugger1.caption
msgid "Continue and  detach debugger"
msgstr "继续并分离调试器"

#: tmemorybrowser.copybytes.caption
msgctxt "TMEMORYBROWSER.COPYBYTES.CAPTION"
msgid "字节"
msgstr "字节"

#: tmemorybrowser.copybytesandopcodes.caption
msgid "Bytes+Opcodes"
msgstr "字节+操作码"

#: tmemorybrowser.copyopcodes.caption
msgid "Opcodes"
msgstr "操作码"

#: tmemorybrowser.copytoclipboard1.caption
msgctxt "TMEMORYBROWSER.COPYTOCLIPBOARD1.CAPTION"
msgid "Copy to clipboard"
msgstr "复制到剪贴板"

#: tmemorybrowser.createthread1.caption
msgid "Create Thread"
msgstr "创建线程"

#: tmemorybrowser.cslabel.caption
msgctxt "TMEMORYBROWSER.CSLABEL.CAPTION"
msgid "CS 0000"
msgstr "CS 0000"

#: tmemorybrowser.cut1.caption
msgctxt "tmemorybrowser.cut1.caption"
msgid "Copy to clipboard"
msgstr "复制到剪贴板"

#: tmemorybrowser.debug1.caption
msgctxt "TMEMORYBROWSER.DEBUG1.CAPTION"
msgid "Debug"
msgstr "调试"

#: tmemorybrowser.debugstrings1.caption
msgctxt "TMEMORYBROWSER.DEBUGSTRINGS1.CAPTION"
msgid "Debug strings"
msgstr "调试字符串"

#: tmemorybrowser.dflabel.caption
msgctxt "TMEMORYBROWSER.DFLABEL.CAPTION"
msgid "DF 0"
msgstr "DF 0"

#: tmemorybrowser.disectwindow1.caption
msgid "Dissect window(s)"
msgstr "分析窗口"

#: tmemorybrowser.dispbytes.caption
msgid "Byte hex"
msgstr "字节 (HEX)"

#: tmemorybrowser.dispchar.caption
msgid "Byte decimal"
msgstr "字节 (DEC)"

#: tmemorybrowser.dispdouble.caption
msgctxt "TMEMORYBROWSER.DISPDOUBLE.CAPTION"
msgid "Double"
msgstr "双浮点"

#: tmemorybrowser.dispdwords.caption
msgid "4 Byte hex"
msgstr "4 字节 (HEX)"

#: tmemorybrowser.dispfloat.caption
msgctxt "TMEMORYBROWSER.DISPFLOAT.CAPTION"
msgid "Float"
msgstr "单浮点"

#: tmemorybrowser.dispints.caption
msgid "4 Byte decimal"
msgstr "4 字节 (DEC)"

#: tmemorybrowser.displaytype1.caption
msgid "Display Type"
msgstr "显示类型"

#: tmemorybrowser.displongs.caption
msgid "8 Byte decimal"
msgstr "8 字节 (DEC)"

#: tmemorybrowser.dispqwords.caption
msgid "8 Byte hex"
msgstr "8 字节 (HEX)"

#: tmemorybrowser.dispshorts.caption
msgid "2 Byte decimal"
msgstr "2 字节 (DEC)"

#: tmemorybrowser.dispwords.caption
msgid "2 Byte hex"
msgstr "2 字节 (HEX)"

#: tmemorybrowser.dissectcode1.caption
msgid "Dissect code"
msgstr "分析代码"

#: tmemorybrowser.dissectdata1.caption
msgctxt "TMEMORYBROWSER.DISSECTDATA1.CAPTION"
msgid "Pointer scan"
msgstr "指针扫描器"

#: tmemorybrowser.dissectpeheaders1.caption
msgid "Dissect PE headers"
msgstr "分析PE文件头"

#: tmemorybrowser.driverlist1.caption
msgctxt "TMEMORYBROWSER.DRIVERLIST1.CAPTION"
msgid "Driver list"
msgstr "驱动列表"

#: tmemorybrowser.dslabel.caption
msgctxt "TMEMORYBROWSER.DSLABEL.CAPTION"
msgid "DS 0000"
msgstr "DS 0000"

#: tmemorybrowser.eaxlabel.caption
msgctxt "TMEMORYBROWSER.EAXLABEL.CAPTION"
msgid "EAX 00000000"
msgstr "EAX 00000000"

#: tmemorybrowser.ebplabel.caption
msgctxt "TMEMORYBROWSER.EBPLABEL.CAPTION"
msgid "EBP 00000000"
msgstr "EBP 00000000"

#: tmemorybrowser.ebxlabel.caption
msgctxt "TMEMORYBROWSER.EBXLABEL.CAPTION"
msgid "EBX 00000000"
msgstr "EBX 00000000"

#: tmemorybrowser.ecxlabel.caption
msgctxt "TMEMORYBROWSER.ECXLABEL.CAPTION"
msgid "ECX 00000000"
msgstr "ECX 00000000"

#: tmemorybrowser.edilabel.caption
msgctxt "TMEMORYBROWSER.EDILABEL.CAPTION"
msgid "EDI 00000000"
msgstr "EDI 00000000"

#: tmemorybrowser.edxlabel.caption
msgctxt "TMEMORYBROWSER.EDXLABEL.CAPTION"
msgid "EDX 00000000"
msgstr "EDX 00000000"

#: tmemorybrowser.eiplabel.caption
msgctxt "TMEMORYBROWSER.EIPLABEL.CAPTION"
msgid "EIP 00000000"
msgstr "EIP 00000000"

#: tmemorybrowser.enumeratedllsandsymbols1.caption
msgid "Enumerate DLL's and Symbols"
msgstr "枚举DLL和符号表"

#: tmemorybrowser.esilabel.caption
msgctxt "TMEMORYBROWSER.ESILABEL.CAPTION"
msgid "ESI 00000000"
msgstr "ESI 00000000"

#: tmemorybrowser.eslabel.caption
msgctxt "TMEMORYBROWSER.ESLABEL.CAPTION"
msgid "ES 0000"
msgstr "ES 0000"

#: tmemorybrowser.esplabel.caption
msgctxt "TMEMORYBROWSER.ESPLABEL.CAPTION"
msgid "ESP 00000000"
msgstr "ESP 00000000"

#: tmemorybrowser.executetillreturn1.caption
msgid "Execute till return"
msgstr "执行到返回"

#: tmemorybrowser.extra1.caption
msgctxt "TMEMORYBROWSER.EXTRA1.CAPTION"
msgid "Tools"
msgstr "工具"

#: tmemorybrowser.file1.caption
msgctxt "TMEMORYBROWSER.FILE1.CAPTION"
msgid "File"
msgstr "文件"

#: tmemorybrowser.fillmemory1.caption
msgid "Fill Memory"
msgstr "填充内存"

#: tmemorybrowser.findmemory1.caption
msgid "Find memory"
msgstr "查找内存"

#: tmemorybrowser.findoutwhataddressesthisinstructionaccesses1.caption
msgid "Find out what addresses this instruction accesses"
msgstr "找出指令访问的地址"

#: tmemorybrowser.findstaticpointers1.caption
msgctxt "TMEMORYBROWSER.FINDSTATICPOINTERS1.CAPTION"
msgid "Find static addresses"
msgstr "查找静态地址"

#: tmemorybrowser.follow1.caption
msgctxt "tmemorybrowser.follow1.caption"
msgid "Follow"
msgstr "前进"

#: tmemorybrowser.fslabel.caption
msgctxt "TMEMORYBROWSER.FSLABEL.CAPTION"
msgid "FS 0000"
msgstr "FS 0000"

#: tmemorybrowser.gdtlist1.caption
msgid "GDT list"
msgstr "GDT 列表"

#: tmemorybrowser.getaddress1.caption
msgid "Get address"
msgstr "获取地址"

#: tmemorybrowser.goto1.caption
msgid "Goto address"
msgstr "转到地址"

#: tmemorybrowser.gotoaddress1.caption
msgid "Go to address"
msgstr "转到地址"

#: tmemorybrowser.gslabel.caption
msgctxt "TMEMORYBROWSER.GSLABEL.CAPTION"
msgid "GS 0000"
msgstr "GS 0000"

#: tmemorybrowser.heaps1.caption
msgid "Heaplist"
msgstr "堆列表"

#: tmemorybrowser.idtlist1.caption
msgid "IDT list"
msgstr "IDT 列表"

#: tmemorybrowser.injectdll1.caption
msgid "Inject DLL"
msgstr "注入 Dll"

#: tmemorybrowser.jumplines1.caption
msgctxt "tmemorybrowser.jumplines1.caption"
msgid "Jumplines"
msgstr "跳转线"

#: tmemorybrowser.kernelmodesymbols1.caption
msgid "Kernelmode symbols"
msgstr "内核模块符号表"

#: tmemorybrowser.kerneltools1.caption
msgid "Kernel tools"
msgstr "内核工具"

#: tmemorybrowser.label14.caption
msgctxt "TMEMORYBROWSER.LABEL14.CAPTION"
msgid "Registers:"
msgstr "寄存器:"

#: tmemorybrowser.label15.caption
msgctxt "TMEMORYBROWSER.LABEL15.CAPTION"
msgid "Flags"
msgstr "标志位"

#: tmemorybrowser.label16.caption
msgid "Segment Registers"
msgstr "段寄存器"

#: tmemorybrowser.loadmemolryregion1.caption
msgid "Load memory region"
msgstr "载入内存区域"

#: tmemorybrowser.loadsymbolfile1.caption
msgid "Load symbol file"
msgstr "载入符号文件"

#: tmemorybrowser.lvstacktracedata.columns[0].caption
msgctxt "TMEMORYBROWSER.LVSTACKTRACEDATA.COLUMNS[0].CAPTION"
msgid "Address"
msgstr "地址"

#: tmemorybrowser.lvstacktracedata.columns[1].caption
msgid "DWORD"
msgstr "双字"

#: tmemorybrowser.lvstacktracedata.columns[2].caption
msgctxt "TMEMORYBROWSER.LVSTACKTRACEDATA.COLUMNS[2].CAPTION"
msgid "Value"
msgstr "数值"

#: tmemorybrowser.lvstacktracedata.hint
msgid "Shows the current stack."
msgstr "显示当前的堆栈."

#: tmemorybrowser.makepagewritable1.caption
msgid "Make page writable"
msgstr "使分页内存可写"

#: tmemorybrowser.maxstacktracesize1.caption
msgid "Max stack: 4096"
msgstr "最大堆栈: 4096"

#: tmemorybrowser.memoryregions1.caption
msgid "Memory Regions"
msgstr "内存区域"

#: tmemorybrowser.menuitem1.caption
msgctxt "TMEMORYBROWSER.MENUITEM1.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.menuitem10.caption
msgid "All strings"
msgstr "所有字符串"

#: tmemorybrowser.menuitem11.caption
msgid "Search for accessible strings"
msgstr "搜索可访问字串"

#: tmemorybrowser.menuitem12.caption
msgid "Apply changes to file"
msgstr "将变更处应用到文件"

#: tmemorybrowser.menuitem13.caption
msgctxt "TMEMORYBROWSER.MENUITEM13.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.menuitem14.caption
msgctxt "TMEMORYBROWSER.MENUITEM14.CAPTION"
msgid "Ultimap"
msgstr "Ultimap"

#: tmemorybrowser.menuitem15.caption
msgid "Overlay with structure"
msgstr "覆盖结构"

#: tmemorybrowser.menuitem16.caption
msgctxt "TMEMORYBROWSER.MENUITEM16.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.menuitem17.caption
msgid "Generate structure from datastream and template"
msgstr "从数据流和模版中生成结构"

#: tmemorybrowser.menuitem18.caption
msgid "Graphical memory view"
msgstr "图形内存视图"

#: tmemorybrowser.menuitem19.caption
msgctxt "TMEMORYBROWSER.MENUITEM19.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.menuitem2.caption
msgid "Select current function"
msgstr "选择当前函数"

#: tmemorybrowser.menuitem20.caption
msgid "Load trace"
msgstr "载入跟踪"

#: tmemorybrowser.menuitem21.caption
msgid "Text Encoding"
msgstr "文本编码"

#: tmemorybrowser.menuitem22.caption
msgid "Watch memory page access"
msgstr "监视内存页的访问"

#: tmemorybrowser.menuitem23.caption
msgid "Set/Unset bookmark"
msgstr "设置/撤销 书签"

#: tmemorybrowser.menuitem24.caption
msgid "Goto bookmark"
msgstr "转到书签"

#: tmemorybrowser.menuitem25.caption
msgid "User write history"
msgstr "用户的写入历史"

#: tmemorybrowser.menuitem26.caption
msgctxt "TMEMORYBROWSER.MENUITEM26.CAPTION"
msgid "Ultimap 2"
msgstr "Ultimap 2"

#: tmemorybrowser.menuitem27.caption
msgid "Watchlist"
msgstr "写入列表"

#: tmemorybrowser.menuitem28.caption
msgctxt "TMEMORYBROWSER.MENUITEM28.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.menuitem3.caption
msgctxt "TMEMORYBROWSER.MENUITEM3.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.menuitem4.caption
msgid "Data Breakpoint"
msgstr "数据断点"

#: tmemorybrowser.menuitem5.caption
msgctxt "TMEMORYBROWSER.MENUITEM5.CAPTION"
msgid "Break on Access"
msgstr "访问时中断"

#: tmemorybrowser.menuitem6.caption
msgctxt "TMEMORYBROWSER.MENUITEM6.CAPTION"
msgid "Break on Write"
msgstr "写入时中断"

#: tmemorybrowser.menuitem7.caption
msgctxt "TMEMORYBROWSER.MENUITEM7.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.menuitem8.caption
msgctxt "TMEMORYBROWSER.MENUITEM8.CAPTION"
msgid "Break and trace"
msgstr "中断和跟踪"

#: tmemorybrowser.menuitem9.caption
msgid "Change fade timer"
msgstr "更改淡入淡出时间"

#: tmemorybrowser.miaddebp.caption
msgctxt "TMEMORYBROWSER.MIADDEBP.CAPTION"
msgid "(ebp+*)"
msgstr "(ebp+*)"

#: tmemorybrowser.miaddesp.caption
msgctxt "TMEMORYBROWSER.MIADDESP.CAPTION"
msgid "(esp+*)"
msgstr "(esp+*)"

#: tmemorybrowser.miaddref.caption
msgctxt "tmemorybrowser.miaddref.caption"
msgid "(ref+*) Ref will be %x"
msgstr "(ref+*) 引用自 %x"

#: tmemorybrowser.miaddtothecodelist.caption
msgid "Add to the code list"
msgstr "添加到代码列表"

#: tmemorybrowser.mibinutils.caption
msgid "BinUtils"
msgstr "BinUtils"

#: tmemorybrowser.mibinutilsselect.caption
msgctxt "TMEMORYBROWSER.MIBINUTILSSELECT.CAPTION"
msgid "None"
msgstr "不可用"

#: tmemorybrowser.miconditionalbreak.caption
msgid "Set/Change break condition"
msgstr "设置/更改中断条件"

#: tmemorybrowser.micopybytesonly.caption
msgid "Bytes only (no address)"
msgstr "仅字节(无地址)"

#: tmemorybrowser.midebugevents.caption
msgid "Debug events"
msgstr "调试事件"

#: tmemorybrowser.mideletebp.caption
msgid "Delete Breakpoint"
msgstr "删除断点"

#: tmemorybrowser.midisassemblertype.caption
msgid "Disassembly output"
msgstr "反汇编输出"

#: tmemorybrowser.midisassembly32.caption
msgid "32-bit"
msgstr "32 位"

#: tmemorybrowser.midisassembly64.caption
msgctxt "TMEMORYBROWSER.MIDISASSEMBLY64.CAPTION"
msgid "64-bit"
msgstr "64 位"

#: tmemorybrowser.midisassemblyautodetect.caption
msgid "Autodetect"
msgstr "自动检测"

#: tmemorybrowser.midissectdata.caption
msgid "Dissect data/structures old"
msgstr "分析(旧)数据/结构"

#: tmemorybrowser.midissectdata2.caption
msgid "Dissect data/structures"
msgstr "分析数据/遍历"

#: tmemorybrowser.mifindwhataccesses.caption
msgctxt "TMEMORYBROWSER.MIFINDWHATACCESSES.CAPTION"
msgid "Find out what accesses this address"
msgstr "找出是什么访问了这个地址"

#: tmemorybrowser.mifindwhatwrites.caption
msgid "Find out what writes this address"
msgstr "找出是什么改写了这个地址"

#: tmemorybrowser.mignuassembler.caption
msgid "GNU Assembler"
msgstr "GNU 汇编器"

#: tmemorybrowser.migotobookmark0.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK0.CAPTION"
msgid "Bookmark 0"
msgstr "书签 0"

#: tmemorybrowser.migotobookmark1.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK1.CAPTION"
msgid "Bookmark 1"
msgstr "书签 1"

#: tmemorybrowser.migotobookmark2.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK2.CAPTION"
msgid "Bookmark 2"
msgstr "书签 2"

#: tmemorybrowser.migotobookmark3.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK3.CAPTION"
msgid "Bookmark 3"
msgstr "书签 3"

#: tmemorybrowser.migotobookmark4.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK4.CAPTION"
msgid "Bookmark 4"
msgstr "书签 4"

#: tmemorybrowser.migotobookmark5.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK5.CAPTION"
msgid "Bookmark 5"
msgstr "书签 5"

#: tmemorybrowser.migotobookmark6.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK6.CAPTION"
msgid "Bookmark 6"
msgstr "书签 6"

#: tmemorybrowser.migotobookmark7.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK7.CAPTION"
msgid "Bookmark 7"
msgstr "书签 7"

#: tmemorybrowser.migotobookmark8.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK8.CAPTION"
msgid "Bookmark 8"
msgstr "书签 8"

#: tmemorybrowser.migotobookmark9.caption
msgctxt "TMEMORYBROWSER.MIGOTOBOOKMARK9.CAPTION"
msgid "Bookmark 9"
msgstr "书签 9"

#: tmemorybrowser.mihvback.caption
msgctxt "TMEMORYBROWSER.MIHVBACK.CAPTION"
msgid "Back"
msgstr "后退"

#: tmemorybrowser.mihvfollow.caption
msgctxt "TMEMORYBROWSER.MIHVFOLLOW.CAPTION"
msgid "Follow"
msgstr "前进"

#: tmemorybrowser.milock.caption
msgctxt "TMEMORYBROWSER.MILOCK.CAPTION"
msgid "Link with other hexview"
msgstr "与其它的十六进制视图链接"

#: tmemorybrowser.milockrowsize.caption
msgid "Lock current rowsize"
msgstr "锁定当前行的大小"

#: tmemorybrowser.miluaengine.caption
msgctxt "TMEMORYBROWSER.MILUAENGINE.CAPTION"
msgid "Lua Engine"
msgstr "Lua 引擎"

#: tmemorybrowser.mipaging.caption
msgctxt "TMEMORYBROWSER.MIPAGING.CAPTION"
msgid "Paging"
msgstr "分页"

#: tmemorybrowser.mipointerspider.caption
msgctxt "TMEMORYBROWSER.MIPOINTERSPIDER.CAPTION"
msgid "Structure spider"
msgstr "结构爬行器"

#: tmemorybrowser.mireferencedfunctions.caption
msgctxt "TMEMORYBROWSER.MIREFERENCEDFUNCTIONS.CAPTION"
msgid "Referenced functions"
msgstr "引用的函数"

#: tmemorybrowser.mireplacewithnops.caption
msgctxt "TMEMORYBROWSER.MIREPLACEWITHNOPS.CAPTION"
msgid "Replace with code that does nothing"
msgstr "使用空指令替换"

#: tmemorybrowser.miseperators.caption
msgid "Seperators"
msgstr "分隔符"

#: tmemorybrowser.misepevery2bytes.caption
msgctxt "TMEMORYBROWSER.MISEPEVERY2BYTES.CAPTION"
msgid "2 字节"
msgstr "2 字节"

#: tmemorybrowser.misepevery4bytes.caption
msgctxt "TMEMORYBROWSER.MISEPEVERY4BYTES.CAPTION"
msgid "4 字节"
msgstr "4 字节"

#: tmemorybrowser.misepevery8bytes.caption
msgctxt "TMEMORYBROWSER.MISEPEVERY8BYTES.CAPTION"
msgid "8 字节"
msgstr "8 字节"

#: tmemorybrowser.misetaddress.caption
msgid "Set Address"
msgstr "设置地址"

#: tmemorybrowser.misetbookmark0.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK0.CAPTION"
msgid "Bookmark 0"
msgstr "书签 0"

#: tmemorybrowser.misetbookmark1.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK1.CAPTION"
msgid "Bookmark 1"
msgstr "书签 1"

#: tmemorybrowser.misetbookmark2.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK2.CAPTION"
msgid "Bookmark 2"
msgstr "书签 2"

#: tmemorybrowser.misetbookmark3.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK3.CAPTION"
msgid "Bookmark 3"
msgstr "书签 3"

#: tmemorybrowser.misetbookmark4.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK4.CAPTION"
msgid "Bookmark 4"
msgstr "书签 4"

#: tmemorybrowser.misetbookmark5.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK5.CAPTION"
msgid "Bookmark 5"
msgstr "书签 5"

#: tmemorybrowser.misetbookmark6.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK6.CAPTION"
msgid "Bookmark 6"
msgstr "书签 6"

#: tmemorybrowser.misetbookmark7.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK7.CAPTION"
msgid "Bookmark 7"
msgstr "书签 7"

#: tmemorybrowser.misetbookmark8.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK8.CAPTION"
msgid "Bookmark 8"
msgstr "书签 8"

#: tmemorybrowser.misetbookmark9.caption
msgctxt "TMEMORYBROWSER.MISETBOOKMARK9.CAPTION"
msgid "Bookmark 9"
msgstr "书签 9"

#: tmemorybrowser.mishowdifference.caption
msgctxt "TMEMORYBROWSER.MISHOWDIFFERENCE.CAPTION"
msgid "Show difference"
msgstr "显示差异"

#: tmemorybrowser.mishowindisassembler.caption
msgid "Show in disassembler"
msgstr "以反汇编显示"

#: tmemorybrowser.mishowinhexview.caption
msgid "Show in hexview"
msgstr "以十六进制显示"

#: tmemorybrowser.mishowrelative.caption
msgid "Show relative addresses"
msgstr "显示相对地址"

#: tmemorybrowser.misvcopy.caption
msgctxt "TMEMORYBROWSER.MISVCOPY.CAPTION"
msgid "Copy"
msgstr "复制"

#: tmemorybrowser.mitextencoding16.caption
msgctxt "tmemorybrowser.mitextencoding16.caption"
msgid "UTF-16"
msgstr "UTF-16"

#: tmemorybrowser.mitextencodingascii.caption
msgid "Ascii"
msgstr "Ascii"

#: tmemorybrowser.mitextencodingutf8.caption
msgid "UTF-8"
msgstr "UTF-8"

#: tmemorybrowser.mitextpreferences.caption
msgctxt "tmemorybrowser.mitextpreferences.caption"
msgid "Preferences"
msgstr "首选项"

#: tmemorybrowser.mitogglebreakpoint.caption
msgctxt "tmemorybrowser.mitogglebreakpoint.caption"
msgid "Toggle breakpoint"
msgstr "设置/取消断点"

#: tmemorybrowser.miuserdefinedcomment.caption
msgid "Set/Change comment"
msgstr "设置/更改注释"

#: tmemorybrowser.miuserdefinedheader.caption
msgid "Set/Change header"
msgstr "设置/更改标题"

#: tmemorybrowser.modulesonly1.caption
msgid "Modules only"
msgstr "仅模块"

#: tmemorybrowser.n1.caption
msgctxt "TMEMORYBROWSER.N1.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n10.caption
msgctxt "TMEMORYBROWSER.N10.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n11.caption
msgctxt "TMEMORYBROWSER.N11.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n12.caption
msgctxt "TMEMORYBROWSER.N12.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n13.caption
msgctxt "TMEMORYBROWSER.N13.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n14.caption
msgctxt "TMEMORYBROWSER.N14.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n15.caption
msgctxt "TMEMORYBROWSER.N15.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n16.caption
msgctxt "TMEMORYBROWSER.N16.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n17.caption
msgctxt "TMEMORYBROWSER.N17.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n18.caption
msgctxt "TMEMORYBROWSER.N18.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n2.caption
msgctxt "TMEMORYBROWSER.N2.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n3.caption
msgctxt "TMEMORYBROWSER.N3.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n4.caption
msgctxt "TMEMORYBROWSER.N4.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n5.caption
msgctxt "TMEMORYBROWSER.N5.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n6.caption
msgctxt "TMEMORYBROWSER.N6.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n7.caption
msgctxt "TMEMORYBROWSER.N7.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.n8.caption
msgctxt "TMEMORYBROWSER.N8.CAPTION"
msgid "-"
msgstr "-"

#: tmemorybrowser.newwindow1.caption
msgctxt "TMEMORYBROWSER.NEWWINDOW1.CAPTION"
msgid "New window"
msgstr "新建窗口"

#: tmemorybrowser.nonsystemmodulesonly1.caption
msgid "Non system modules only"
msgstr "仅非系统模块"

#: tmemorybrowser.oflabel.caption
msgctxt "TMEMORYBROWSER.OFLABEL.CAPTION"
msgid "OF 0"
msgstr "OF 0"

#: tmemorybrowser.onlyshowjumplineswithinrange1.caption
msgid "Only show jumplines within range"
msgstr "仅显示范围内的跳转线"

#: tmemorybrowser.opendlldialog.title
msgid "Select the module you want to inject"
msgstr "选择你要注入的模块"

#: tmemorybrowser.pastefromclipboard1.caption
msgid "Paste from clipboard"
msgstr "从剪贴板粘贴"

#: tmemorybrowser.pflabel.caption
msgctxt "TMEMORYBROWSER.PFLABEL.CAPTION"
msgid "PF 0"
msgstr "PF 0"

#: tmemorybrowser.plugins1.caption
msgctxt "TMEMORYBROWSER.PLUGINS1.CAPTION"
msgid "Plugins"
msgstr "插件"

#: tmemorybrowser.referencedstrings1.caption
msgid "Referenced strings"
msgstr "引用的字符串"

#: tmemorybrowser.reservememory1.caption
msgid "Allocate Memory"
msgstr "分配内存"

#: tmemorybrowser.run1.caption
msgctxt "TMEMORYBROWSER.RUN1.CAPTION"
msgid "Run"
msgstr "运行"

#: tmemorybrowser.runtill1.caption
msgid "Run till..."
msgstr "运行到..."

#: tmemorybrowser.savedisassemledoutput1.caption
msgctxt "TMEMORYBROWSER.SAVEDISASSEMLEDOUTPUT1.CAPTION"
msgid "Save disassembled output"
msgstr "保存反汇编输出"

#: tmemorybrowser.savememoryregion1.caption
msgid "Save memory region"
msgstr "保存内存区域"

#: tmemorybrowser.sbshowfloats.caption
msgid ">"
msgstr ">"

#: tmemorybrowser.scanforcodecaves1.caption
msgid "Scan for code caves"
msgstr "扫描 Code Caves"

#: tmemorybrowser.search1.caption
msgid "Search memory..."
msgstr "搜索内存..."

#: tmemorybrowser.search2.caption
msgctxt "TMEMORYBROWSER.SEARCH2.CAPTION"
msgid "Search"
msgstr "搜索"

#: tmemorybrowser.sericedescriptortable1.caption
msgid "Service Descriptor Table"
msgstr "服务描述信息表"

#: tmemorybrowser.setbreakpoint1.caption
msgctxt "TMEMORYBROWSER.SETBREAKPOINT1.CAPTION"
msgid "Toggle breakpoint"
msgstr "设置/取消断点"

#: tmemorybrowser.setsymbolsearchpath1.caption
msgid "Set symbol searchpath"
msgstr "设置符号搜索路径"

#: tmemorybrowser.sflabel.caption
msgctxt "TMEMORYBROWSER.SFLABEL.CAPTION"
msgid "SF 0"
msgstr "SF 0"

#: tmemorybrowser.showjumplines1.caption
msgid "Show jumplines"
msgstr "显示跳转线"

#: tmemorybrowser.showmoduleaddresses1.caption
msgid "Show module addresses"
msgstr "显示模块地址"

#: tmemorybrowser.showsymbols1.caption
msgid "Show symbols"
msgstr "显示符号"

#: tmemorybrowser.showvaluesofstaticaddresses1.caption
msgid "Show 'Comment' row"
msgstr "显示\"注释\"列"

#: tmemorybrowser.sslabel.caption
msgctxt "TMEMORYBROWSER.SSLABEL.CAPTION"
msgid "SS 0000"
msgstr "SS 0000"

#: tmemorybrowser.stacktrace1.caption
msgctxt "TMEMORYBROWSER.STACKTRACE1.CAPTION"
msgid "Stacktrace"
msgstr "堆栈跟踪"

#: tmemorybrowser.stacktrace2.caption
msgctxt "TMEMORYBROWSER.STACKTRACE2.CAPTION"
msgid "Stacktrace"
msgstr "堆栈跟踪"

#: tmemorybrowser.step1.caption
msgid "Step"
msgstr "步入"

#: tmemorybrowser.stepover1.caption
msgid "Step Over"
msgstr "步过"

#: tmemorybrowser.symbolhandler1.caption
msgid "Userdefined symbols"
msgstr "用户自定义的符号表"

#: tmemorybrowser.threadlist1.caption
msgctxt "TMEMORYBROWSER.THREADLIST1.CAPTION"
msgid "Threadlist"
msgstr "线程列表"

#: tmemorybrowser.view1.caption
msgctxt "TMEMORYBROWSER.VIEW1.CAPTION"
msgid "View"
msgstr "视图"

#: tmemorybrowser.watchmemoryallocations1.caption
msgid "Watch memory allocations"
msgstr "监视内存分配"

#: tmemorybrowser.zflabel.caption
msgctxt "TMEMORYBROWSER.ZFLABEL.CAPTION"
msgid "ZF 0"
msgstr "ZF 0"

#: tprocesswindow.btnnetwork.caption
msgctxt "TPROCESSWINDOW.BTNNETWORK.CAPTION"
msgid "Network"
msgstr "网络"

#: tprocesswindow.button4.caption
msgid "Attach debugger to process"
msgstr "调试器附加到进程"

#: tprocesswindow.cancelbutton.caption
msgctxt "TPROCESSWINDOW.CANCELBUTTON.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tprocesswindow.caption
msgctxt "TPROCESSWINDOW.CAPTION"
msgid "Process List"
msgstr "进程列表"

#: tprocesswindow.filter1.caption
msgctxt "TPROCESSWINDOW.FILTER1.CAPTION"
msgid "Filter"
msgstr "过滤器"

#: tprocesswindow.inputpidmanually1.caption
msgid "Input PID manually"
msgstr "手动输入 PID"

#: tprocesswindow.menuitem1.caption
msgctxt "TPROCESSWINDOW.MENUITEM1.CAPTION"
msgid "File"
msgstr "文件"

#: tprocesswindow.menuitem4.caption
msgctxt "TPROCESSWINDOW.MENUITEM4.CAPTION"
msgid "View"
msgstr "查看"

#: tprocesswindow.menuitem5.caption
msgctxt "TPROCESSWINDOW.MENUITEM5.CAPTION"
msgid "Preferences"
msgstr "首选项"

#: tprocesswindow.michangefont.caption
msgid "Change font"
msgstr "更改字体"

#: tprocesswindow.micreateprocess.caption
msgctxt "TPROCESSWINDOW.MICREATEPROCESS.CAPTION"
msgid "Create Process"
msgstr "创建进程"

#: tprocesswindow.miopenfile.caption
msgctxt "TPROCESSWINDOW.MIOPENFILE.CAPTION"
msgid "Open File"
msgstr "打开文件"

#: tprocesswindow.miownprocessesonly.caption
msgid "Only show processes of the current user"
msgstr "只显示当前用户进程"

#: tprocesswindow.miprocesslistlong.caption
msgid "Process List (Long)"
msgstr "进程列表 (详细)"

#: tprocesswindow.mishowinvisibleitems.caption
msgctxt "TPROCESSWINDOW.MISHOWINVISIBLEITEMS.CAPTION"
msgid "Show invisible windows"
msgstr "显示不可见的窗口"

#: tprocesswindow.miskipsystemprocesses.caption
msgctxt "TPROCESSWINDOW.MISKIPSYSTEMPROCESSES.CAPTION"
msgid "Only show non-system processes"
msgstr "不显示系统进程"

#: tprocesswindow.n1.caption
msgctxt "TPROCESSWINDOW.N1.CAPTION"
msgid "-"
msgstr "-"

#: tprocesswindow.n2.caption
msgctxt "TPROCESSWINDOW.N2.CAPTION"
msgid "-"
msgstr "-"

#: tprocesswindow.okbutton.caption
msgctxt "TPROCESSWINDOW.OKBUTTON.CAPTION"
msgid "Open"
msgstr "打开"

#: trainergenerator.rsaaaaw
msgid "aaaaw :("
msgstr "aaaaw :("

#: trainergenerator.rsabout
msgctxt "trainergenerator.rsabout"
msgid "About"
msgstr "关于"

#: trainergenerator.rsalreadyatrainerformdefined
msgid "There is already a trainer form defined. Continuing will erase the current trainerscript and cheats in the trainer and replace them with the current hotkeys defined in your current cheat table (Layout and images will remain unchanged). Continue ?"
msgstr "已经定义了一个修改器窗体. 如果继续将清除修改器中的脚本并替换为当前金手指列表中所定义的当前热键(布局和图像保持不变). 继续吗?"

#: trainergenerator.rsareyousure
msgid "Are you sure?"
msgstr "你确定吗?"

#: trainergenerator.rsautogenwarningpart1
msgid "This is autogenerated code. Changing code in this block will"
msgstr "这是自动生成的代码. 请在此区域内个修改代码"

#: trainergenerator.rsautogenwarningpart2
msgid "get erased and rewritten if you regenerate the trainer code"
msgstr "如果你更新了修改器代码，将被删除并重新写入"

#: trainergenerator.rsbutallowdecrease
msgid "but allow decrease"
msgstr "允许减少"

#: trainergenerator.rsbutallowincrease
msgid "but allow increase"
msgstr "允许增加"

#: trainergenerator.rsby
msgid "by"
msgstr "由"

#: trainergenerator.rscheatentries
msgid "Cheat Entries"
msgstr "金手指列表"

#: trainergenerator.rsclose
msgctxt "trainergenerator.rsclose"
msgid "Close"
msgstr "关闭"

#: trainergenerator.rsdeactive
msgid "(De)active"
msgstr "(未)激活"

#: trainergenerator.rsdecrease
msgid "Decrease"
msgstr "减少"

#: trainergenerator.rsdesignuserinterfacemanually
msgctxt "trainergenerator.rsdesignuserinterfacemanually"
msgid "Design userinterface manually"
msgstr "用户自行设计界面"

#: trainergenerator.rsdontsupportcheatengineoryourself
msgctxt "trainergenerator.rsdontsupportcheatengineoryourself"
msgid "Don't support Cheat Engine (or yourself)"
msgstr "不支持 Cheat Engine (可自行设计界面)"

#: trainergenerator.rsdosomethingwith
msgid "Do something with"
msgstr "要做什么"

#: trainergenerator.rseffect
msgid "Effect"
msgstr "功能"

#: trainergenerator.rsgobacktogenerateddesigner
msgid "Go back to generated designer"
msgstr "返回到制作设计器中"

#: trainergenerator.rshotkey
msgctxt "trainergenerator.rshotkey"
msgid "Hotkey"
msgstr "热键"

#: trainergenerator.rsincrease
msgid "Increase"
msgstr "增加"

#: trainergenerator.rsinvalidtrainerscript
msgid "The current lua script only has a half TRAINERGENERATORSTART/TRAINERGENERATORSTOP block. Please fix this first (Removing is the easiest option)"
msgstr "检查到目前 Lua 脚本中的 TRAINERGENERATORSTART/TRAINERGENERATORSTOP 不完整. 请解决这个问题(删除是最简单的办法)"

#: trainergenerator.rsnocheatpanel
msgid "The current trainer form does not have a panel named 'CHEATPANEL' so can not be reused by the automated trainer generator.%sDo you want to start from scratch? (If you want to create a trainer from your current script you can just save your table as .EXE instead of using the automated trainer generator)"
msgstr "当前修改器中没有一个名为 'CHEATPANEL' 的层, 所以无法自动制作修改器. %s你想要从头开始吗? (如果你想重新创建一个脚本, 你可以保存当前脚本到 .EXE 中, 而不是使用修改器自动生成器)"

#: trainergenerator.rsonclosewarning
msgid "This form had an onClose event. Good thing this was only a stub, else Cheat Engine would have terminated"
msgstr "这窗口有onClose事件,幸好这只是一个存根(stub),否则CE将会终止"

#: trainergenerator.rspleaseselecttypeinaprocessname
msgid "Please select/type in a processname"
msgstr "请选择或输入一个进程名"

#: trainergenerator.rsselectthecheatentryyouwanttosetthehotkeyfor
msgid "Select the cheat entry you want to set the hotkey for"
msgstr "选择你想要设置热键的金手指项"

#: trainergenerator.rsset
msgid "Set"
msgstr "设置"

#: trainergenerator.rsthankyou
msgid "Thank you! :)"
msgstr "感谢你!:)"

#: trainergenerator.rstipyoudonthavetousethetrainergeneratorifyoudontwantto
msgid "Tip: You don't have to use the trainer generator if you don't want to. You can just save your table as .EXE or CETRAINER"
msgstr "提示: 如果你不愿意你不必使用修改器生成器. 你可以只保存你的表单为 EXE 或 CETRAINER"

#: trainergenerator.rsto
msgid "to"
msgstr "到"

#: trainergenerator.rsunfreeze
msgid "(Un)Freeze"
msgstr "(未)锁定"

#: trainergenerator.rsyouneedacheattablewithcheatentries
msgid "You need a cheat table with cheat entries"
msgstr "你需要一个CT表中的金手指项"

#: tregisters.caption
msgid "Registers"
msgstr "寄存器"

#: tregisters.eaxlabel.caption
msgctxt "tregisters.eaxlabel.caption"
msgid "EAX 00000000FFFFFFFF"
msgstr "EAX 00000000FFFFFFFF"

#: tregisters.ebplabel.caption
msgctxt "tregisters.ebplabel.caption"
msgid "EBP 00000000"
msgstr "EBP 00000000"

#: tregisters.ebxlabel.caption
msgctxt "tregisters.ebxlabel.caption"
msgid "EBX 00000000"
msgstr "EBX 00000000"

#: tregisters.ecxlabel.caption
msgctxt "tregisters.ecxlabel.caption"
msgid "ECX 00000000"
msgstr "ECX 00000000"

#: tregisters.edilabel.caption
msgctxt "tregisters.edilabel.caption"
msgid "EDI 00000000"
msgstr "EDI 00000000"

#: tregisters.edxlabel.caption
msgctxt "tregisters.edxlabel.caption"
msgid "EDX 00000000"
msgstr "EDX 00000000"

#: tregisters.eiplabel.caption
msgctxt "tregisters.eiplabel.caption"
msgid "EIP 00000000"
msgstr "EIP 00000000"

#: tregisters.esilabel.caption
msgctxt "tregisters.esilabel.caption"
msgid "ESI 00000000"
msgstr "ESI 00000000"

#: tregisters.esplabel.caption
msgctxt "tregisters.esplabel.caption"
msgid "ESP 00000000"
msgstr "ESP 00000000"

#: tregisters.label14.caption
msgctxt "tregisters.label14.caption"
msgid "Registers:"
msgstr "寄存器:"

#: tregisters.menuitem1.caption
msgid "Copy registers to clipboard"
msgstr "寄存器复制到剪贴板"

#: tregisters.menuitem2.caption
msgid "Copy full context block to clipboard"
msgstr "复制完整的模块属性到剪贴板"

#: tregisters.sbshowfloats.caption
msgctxt "TREGISTERS.SBSHOWFLOATS.CAPTION"
msgid "F"
msgstr "F"

#: tregisters.sbshowfloats.hint
msgctxt "TREGISTERS.SBSHOWFLOATS.HINT"
msgid "Floating point registers"
msgstr "浮点寄存器"

#: tregisters.sbshowstack.caption
msgctxt "TREGISTERS.SBSHOWSTACK.CAPTION"
msgid "S"
msgstr "S"

#: tregisters.sbshowstack.hint
msgctxt "TREGISTERS.SBSHOWSTACK.HINT"
msgid "Stack view"
msgstr "堆栈视图"

#: ttlg.caption
msgid "TLG:The Lame Game"
msgstr "TLG:蹩脚的游戏"

#: ttlg.label1.caption
msgid "Score:"
msgstr "得分:"

#: ttlg.label2.caption
msgctxt "TTLG.LABEL2.CAPTION"
msgid "0"
msgstr "0"

#: ttypeform.button1.caption
msgctxt "TTYPEFORM.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: ttypeform.button2.caption
msgctxt "TTYPEFORM.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: ttypeform.caption
msgctxt "TTYPEFORM.CAPTION"
msgid "Type"
msgstr "类型"

#: ttypeform.cbcodepage.caption
msgctxt "TTYPEFORM.CBCODEPAGE.CAPTION"
msgid "Codepage"
msgstr "代码页"

#: ttypeform.cbunicode.caption
msgctxt "TTYPEFORM.CBUNICODE.CAPTION"
msgid "Unicode"
msgstr "Unicode"

#: ttypeform.edit1.text
msgctxt "TTYPEFORM.EDIT1.TEXT"
msgid "10"
msgstr "10"

#: ttypeform.edit2.text
msgctxt "TTYPEFORM.EDIT2.TEXT"
msgid "1"
msgstr "1"

#: ttypeform.label1.caption
msgid "Select the new type:"
msgstr "选择新类型:"

#: ttypeform.label10.caption
msgctxt "TTYPEFORM.LABEL10.CAPTION"
msgid "6"
msgstr "6"

#: ttypeform.label11.caption
msgctxt "TTYPEFORM.LABEL11.CAPTION"
msgid "7"
msgstr "7"

#: ttypeform.label2.caption
msgctxt "TTYPEFORM.LABEL2.CAPTION"
msgid "Length"
msgstr "长度"

#: ttypeform.label4.caption
msgctxt "TTYPEFORM.LABEL4.CAPTION"
msgid "0"
msgstr "0"

#: ttypeform.label5.caption
msgctxt "TTYPEFORM.LABEL5.CAPTION"
msgid "1"
msgstr "1"

#: ttypeform.label6.caption
msgctxt "TTYPEFORM.LABEL6.CAPTION"
msgid "2"
msgstr "2"

#: ttypeform.label7.caption
msgctxt "TTYPEFORM.LABEL7.CAPTION"
msgid "3"
msgstr "3"

#: ttypeform.label8.caption
msgctxt "TTYPEFORM.LABEL8.CAPTION"
msgid "4"
msgstr "4"

#: ttypeform.label9.caption
msgctxt "TTYPEFORM.LABEL9.CAPTION"
msgid "5"
msgstr "5"

#: ttypeform.lengthlabel.caption
msgctxt "TTYPEFORM.LENGTHLABEL.CAPTION"
msgid "Length"
msgstr "长度"

#: tvaluechangeform.button1.caption
msgctxt "TVALUECHANGEFORM.BUTTON1.CAPTION"
msgid "OK"
msgstr "确定"

#: tvaluechangeform.button2.caption
msgctxt "TVALUECHANGEFORM.BUTTON2.CAPTION"
msgid "Cancel"
msgstr "取消"

#: tvaluechangeform.caption
msgid "Change Offset: ########"
msgstr "更改偏移: ########"

#: tvaluechangeform.cbunicode.caption
msgctxt "TVALUECHANGEFORM.CBUNICODE.CAPTION"
msgid "Unicode"
msgstr "Unicode"

#: tvaluechangeform.cbvartype.text
msgid "1 Byte"
msgstr "1 字节"

#: tvaluechangeform.valuetext.text
msgid "ValueText"
msgstr "ValueText"

#: typepopup.rsinvalidlength
msgid "Invalid length"
msgstr "无效的长度"

#: unrandomizer.rsthefollowingaddressesgotchanged
msgid "The following addresses got changed"
msgstr "以下地址已改变"

#: unrandomizer.rstheunrandomizerwillcurrentlynotworkon64bitapplicat
msgid "The unrandomizer will currently not work on 64-bit applications"
msgstr "目前无法在 64位应用程序中运行"

#: valuechange.rschangeoffset
msgid "Change offset %s"
msgstr "更改偏移 %s"

#: valuechange.rspartofthestringisunreadable
msgid "Part of the string is unreadable!"
msgstr "一部分字符串不可读取!"

#: vartypestrings.rs_vtall
msgctxt "vartypestrings.rs_vtall"
msgid "All"
msgstr "所有类型"

#: vartypestrings.rs_vtautoassembler
msgctxt "vartypestrings.rs_vtautoassembler"
msgid "Auto Assembler Script"
msgstr "自动汇编脚本"

#: vartypestrings.rs_vtbinary
msgctxt "vartypestrings.rs_vtbinary"
msgid "二进制"
msgstr "二进制"

#: vartypestrings.rs_vtbyte
msgctxt "vartypestrings.rs_vtbyte"
msgid "Byte"
msgstr "字节"

#: vartypestrings.rs_vtbytearray
msgctxt "vartypestrings.rs_vtbytearray"
msgid "Array of byte"
msgstr "字节数组"

#: vartypestrings.rs_vtcodepagestring
msgid "CodePage String"
msgstr "代码页字符串"

#: vartypestrings.rs_vtcustom
msgctxt "vartypestrings.rs_vtcustom"
msgid "Custom"
msgstr "自定义"

#: vartypestrings.rs_vtdouble
msgctxt "vartypestrings.rs_vtdouble"
msgid "Double"
msgstr "双浮点"

#: vartypestrings.rs_vtdword
msgctxt "vartypestrings.rs_vtdword"
msgid "4 字节"
msgstr "4 字节"

#: vartypestrings.rs_vtgrouped
msgid "Grouped"
msgstr "群组"

#: vartypestrings.rs_vtpointer
msgctxt "vartypestrings.rs_vtpointer"
msgid "Pointer"
msgstr "指针"

#: vartypestrings.rs_vtqword
msgctxt "vartypestrings.rs_vtqword"
msgid "8 字节"
msgstr "8 字节"

#: vartypestrings.rs_vtsingle
msgctxt "vartypestrings.rs_vtsingle"
msgid "浮点数"
msgstr "单浮点"

#: vartypestrings.rs_vtstring
msgctxt "vartypestrings.rs_vtstring"
msgid "字符串"
msgstr "字符串"

#: vartypestrings.rs_vtunicodestring
msgctxt "vartypestrings.rs_vtunicodestring"
msgid "Unicode字符串"
msgstr "Unicode字符串"

#: vartypestrings.rs_vtword
msgctxt "vartypestrings.rs_vtword"
msgid "2 字节"
msgstr "2 字节"

#: vehdebugger.rscheatenginefailedtogetintotheconfig
msgid "Cheat Engine failed to get into the config of the selected program. (Error=%s)"
msgstr "Cheat Engine 无法获取所选择的配置方案. (错误=%s)"

#: vehdebugger.rserrorwhiletryingtocreatetheconfigurationstructure
msgid "Error while trying to create the configuration structure! (Which effectively renders this whole feature useless) Errorcode=%s"
msgstr "尝试创建结构配置时出错! (将会使这一功能无法使用) 错误码=%s"

#: vehdebugger.rsfailureduplicatingtheeventhandlestotheotherprocess
msgid "Failure duplicating the event handles to the other process"
msgstr "失败的事件句柄复制到其他进程中"

#: vehdebugger.rsfailureduplicatingthefilemapping
msgid "Failure duplicating the filemapping"
msgstr "拷贝内存映射文件失败"

#: vehdebugger.rsvehdebugerror
msgid "VEH Debug error"
msgstr "VEH 调试器错误"

#: virtualmemory.rsnomemoryfoundinthespecifiedregion
msgctxt "virtualmemory.rsnomemoryfoundinthespecifiedregion"
msgid "No memory found in the specified region"
msgstr "指定区域中没有找到内存"

#: virtualmemory.rsnotenoughmemoryfreetoscan
msgctxt "virtualmemory.rsnotenoughmemoryfreetoscan"
msgid "Not enough memory free to scan"
msgstr "没有足够的内存进行扫描"

#: vmxfunctions.rsbigerror
msgctxt "vmxfunctions.rsbigerror"
msgid "Error"
msgstr "错误"

#: vmxfunctions.rsinvalidinstruction
msgid "Invalid instruction"
msgstr "无效的指令"

#: vmxfunctions.rssmallerror
msgctxt "vmxfunctions.rssmallerror"
msgid "error"
msgstr "错误"

#: windowsdebugger.rserrorattachingthewindowsdebugger
msgid "Error attaching the windows debugger: %s"
msgstr "附加windows调试器错误: %s"

#: xmplayer_server.rsfailedcreatingtheaudiopipe
msgid "Failed creating the audio pipe"
msgstr "无法创建音频通道"

#: tmainform.menuitem15.caption
msgid "Cheat Engine Tutorial Games"
msgstr "Cheat Engine 作弊教学"

#: xmplayer_server.rsxmplayerexeismissing
msgid "xmplayer.exe is missing"
msgstr "未找到xmplayer.exe"

#: zstreamext.rsaseekwasdoneforanotherpurpose
msgid "A seek was done for another purpose than getting the position"
msgstr "A seek was done for another purpose than getting the position"

#: {description} 
msgid " The description of the hotkey"
msgstr " 热键的描述"

#: {mrdescription} 
msgid " The description field of the memory record"
msgstr " 内存段记录的描述"

#: tformsettings.cbneverchangeprotection.caption
msgid "Never change memory protection when editing"
msgstr "编辑时永远不要更改内存保护"

#: tformsettings.cbalwaysaskforpassword.caption
msgid "Always ask for password when signing"
msgstr "签名时一定要询问密码"

#: tformsettings.cbalwaysforceload.caption
msgid "Always force load modules"
msgstr "总是强制加载模块"

#: formsettingsunit.rsallcustomtypes
msgid "All Custom Types"
msgstr "所有自定义类型"

#: tmemorybrowser.mibreakonexceptions.caption
msgid "Break on unexpected exceptions"
msgstr "意外异常中断"
