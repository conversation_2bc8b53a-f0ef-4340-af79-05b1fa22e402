msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: lclstrconsts.hhshelpbrowsernotexecutable
msgid "Browser %s%s%s not executable."
msgstr ""

#: lclstrconsts.hhshelpbrowsernotfound
msgid "Browser %s%s%s not found."
msgstr ""

#: lclstrconsts.hhshelperrorwhileexecuting
msgid "Error while executing %s%s%s:%s%s"
msgstr ""

#: lclstrconsts.hhshelpnohtmlbrowserfound
msgid "Unable to find a HTML browser."
msgstr ""

#: lclstrconsts.hhshelpnohtmlbrowserfoundpleasedefineoneinhelpconfigurehe
msgid "No HTML Browser found.%sPlease define one in Environment -> Options -> Help -> Help Options"
msgstr ""

#: lclstrconsts.hhshelpthehelpdatabasewasunabletofindfile
msgid "The help database %s%s%s was unable to find file %s%s%s."
msgstr ""

#: lclstrconsts.hhshelpthemacrosinbrowserparamswillbereplacedbytheurl
msgid "The macro %s in BrowserParams will be replaced by the URL."
msgstr ""

#: lclstrconsts.ifsalt
msgid "Alt"
msgstr ""

#: lclstrconsts.ifsctrl
msgid "Ctrl"
msgstr ""

#: lclstrconsts.ifsvk_accept
msgid "Accept"
msgstr ""

#: lclstrconsts.ifsvk_apps
msgid "application key"
msgstr ""

#: lclstrconsts.ifsvk_back
msgid "Backspace"
msgstr ""

#: lclstrconsts.ifsvk_cancel
msgctxt "lclstrconsts.ifsvk_cancel"
msgid "Cancel"
msgstr ""

#: lclstrconsts.ifsvk_capital
msgid "Capital"
msgstr ""

#: lclstrconsts.ifsvk_clear
msgid "Clear"
msgstr ""

#: lclstrconsts.ifsvk_control
msgid "Control"
msgstr ""

#: lclstrconsts.ifsvk_convert
msgid "Convert"
msgstr ""

#: lclstrconsts.ifsvk_delete
msgctxt "lclstrconsts.ifsvk_delete"
msgid "Delete"
msgstr ""

#: lclstrconsts.ifsvk_down
msgctxt "lclstrconsts.ifsvk_down"
msgid "Down"
msgstr ""

#: lclstrconsts.ifsvk_end
msgctxt "lclstrconsts.ifsvk_end"
msgid "End"
msgstr ""

#: lclstrconsts.ifsvk_escape
msgid "Escape"
msgstr ""

#: lclstrconsts.ifsvk_execute
msgid "Execute"
msgstr ""

#: lclstrconsts.ifsvk_final
msgid "Final"
msgstr ""

#: lclstrconsts.ifsvk_hanja
msgid "Hanja"
msgstr ""

#: lclstrconsts.ifsvk_help
msgid "Help"
msgstr ""

#: lclstrconsts.ifsvk_home
msgctxt "lclstrconsts.ifsvk_home"
msgid "Home"
msgstr ""

#: lclstrconsts.ifsvk_insert
msgctxt "lclstrconsts.ifsvk_insert"
msgid "Insert"
msgstr ""

#: lclstrconsts.ifsvk_junja
msgid "Junja"
msgstr ""

#: lclstrconsts.ifsvk_kana
msgid "Kana"
msgstr ""

#: lclstrconsts.ifsvk_lbutton
msgid "Mouse Button Left"
msgstr ""

#: lclstrconsts.ifsvk_left
msgctxt "lclstrconsts.ifsvk_left"
msgid "Left"
msgstr ""

#: lclstrconsts.ifsvk_lwin
msgid "left windows key"
msgstr ""

#: lclstrconsts.ifsvk_mbutton
msgid "Mouse Button Middle"
msgstr ""

#: lclstrconsts.ifsvk_menu
msgctxt "lclstrconsts.ifsvk_menu"
msgid "Menu"
msgstr ""

#: lclstrconsts.ifsvk_modechange
msgid "Mode Change"
msgstr ""

#: lclstrconsts.ifsvk_next
msgctxt "lclstrconsts.ifsvk_next"
msgid "Next"
msgstr ""

#: lclstrconsts.ifsvk_nonconvert
msgid "Nonconvert"
msgstr ""

#: lclstrconsts.ifsvk_numlock
msgid "Numlock"
msgstr ""

#: lclstrconsts.ifsvk_numpad
msgid "Numpad %d"
msgstr ""

#: lclstrconsts.ifsvk_pause
msgid "Pause key"
msgstr ""

#: lclstrconsts.ifsvk_print
msgid "Print"
msgstr ""

#: lclstrconsts.ifsvk_prior
msgctxt "lclstrconsts.ifsvk_prior"
msgid "Prior"
msgstr ""

#: lclstrconsts.ifsvk_rbutton
msgid "Mouse Button Right"
msgstr ""

#: lclstrconsts.ifsvk_return
msgid "Return"
msgstr ""

#: lclstrconsts.ifsvk_right
msgctxt "lclstrconsts.ifsvk_right"
msgid "Right"
msgstr ""

#: lclstrconsts.ifsvk_rwin
msgid "right windows key"
msgstr ""

#: lclstrconsts.ifsvk_scroll
msgid "Scroll"
msgstr ""

#: lclstrconsts.ifsvk_select
msgid "Select"
msgstr ""

#: lclstrconsts.ifsvk_shift
msgid "Shift"
msgstr ""

#: lclstrconsts.ifsvk_snapshot
msgid "Snapshot"
msgstr ""

#: lclstrconsts.ifsvk_space
msgid "Space key"
msgstr ""

#: lclstrconsts.ifsvk_tab
msgctxt "lclstrconsts.ifsvk_tab"
msgid "Tab"
msgstr ""

#: lclstrconsts.ifsvk_unknown
msgid "Unknown"
msgstr ""

#: lclstrconsts.ifsvk_up
msgctxt "lclstrconsts.ifsvk_up"
msgid "Up"
msgstr ""

#: lclstrconsts.liscannotexecute
msgid "can not execute %s"
msgstr ""

#: lclstrconsts.lislclresourcesnotfound
msgctxt "lclstrconsts.lislclresourcesnotfound"
msgid "Resource %s not found"
msgstr ""

#: lclstrconsts.lisprogramfilenotfound
msgid "program file not found %s"
msgstr ""

#: lclstrconsts.rs3ddkshadowcolorcaption
msgid "3D Dark Shadow"
msgstr ""

#: lclstrconsts.rs3dlightcolorcaption
msgid "3D Light"
msgstr ""

#: lclstrconsts.rsacontrolcannothaveitselfasparent
msgid "A control can't have itself as a parent"
msgstr ""

#: lclstrconsts.rsactivebordercolorcaption
msgid "Active Border"
msgstr ""

#: lclstrconsts.rsactivecaptioncolorcaption
msgid "Active Caption"
msgstr ""

#: lclstrconsts.rsallfiles
msgid "All files (%s)|%s|%s"
msgstr ""

#: lclstrconsts.rsappworkspacecolorcaption
msgid "Application Workspace"
msgstr ""

#: lclstrconsts.rsaquacolorcaption
msgid "Aqua"
msgstr ""

#: lclstrconsts.rsbackgroundcolorcaption
msgid "Desktop"
msgstr ""

#: lclstrconsts.rsbackward
msgid "Backward"
msgstr ""

#: lclstrconsts.rsbitmaps
msgid "Bitmaps"
msgstr ""

#: lclstrconsts.rsblackcolorcaption
msgid "Black"
msgstr ""

#: lclstrconsts.rsblank
msgid "Blank"
msgstr ""

#: lclstrconsts.rsbluecolorcaption
msgid "Blue"
msgstr ""

#: lclstrconsts.rsbtnfacecolorcaption
msgid "Button Face"
msgstr ""

#: lclstrconsts.rsbtnhighlightcolorcaption
msgid "Button Highlight"
msgstr ""

#: lclstrconsts.rsbtnshadowcolorcaption
msgid "Button Shadow"
msgstr ""

#: lclstrconsts.rsbtntextcolorcaption
msgid "Button Text"
msgstr ""

#: lclstrconsts.rscalculator
msgid "Calculator"
msgstr ""

#: lclstrconsts.rscancelrecordhint
msgctxt "lclstrconsts.rscancelrecordhint"
msgid "Cancel"
msgstr ""

#: lclstrconsts.rscannotfocus
msgid "Can not focus"
msgstr ""

#: lclstrconsts.rscanvasdoesnotallowdrawing
msgid "Canvas does not allow drawing"
msgstr ""

#: lclstrconsts.rscaptiontextcolorcaption
msgid "Caption Text"
msgstr ""

#: lclstrconsts.rscasesensitive
msgid "Case sensitive"
msgstr ""

#: lclstrconsts.rscontrolclasscantcontainchildclass
msgid "Control of class '%s' can't have control of class '%s' as a child"
msgstr ""

#: lclstrconsts.rscontrolhasnoparentwindow
msgid "Control '%s' has no parent window"
msgstr ""

#: lclstrconsts.rscreamcolorcaption
msgid "Cream"
msgstr ""

#: lclstrconsts.rscreatinggdbcatchableerror
msgid "Creating gdb catchable error:"
msgstr ""

#: lclstrconsts.rscursor
msgid "Cursor"
msgstr ""

#: lclstrconsts.rscustomcolorcaption
msgid "Custom ..."
msgstr ""

#: lclstrconsts.rsdefaultcolorcaption
msgid "Default"
msgstr ""

#: lclstrconsts.rsdefaultfileinfovalue
msgid "permissions user group size date time"
msgstr ""

#: lclstrconsts.rsdeleterecord
msgid "Delete record?"
msgstr ""

#: lclstrconsts.rsdeleterecordhint
msgctxt "lclstrconsts.rsdeleterecordhint"
msgid "Delete"
msgstr ""

#: lclstrconsts.rsdirection
msgid "Direction"
msgstr ""

#: lclstrconsts.rsdirectory
msgid "&Directory"
msgstr ""

#: lclstrconsts.rsdocking
msgid "Docking"
msgstr ""

#: lclstrconsts.rsduplicateiconformat
msgid "Duplicate icon format."
msgstr ""

#: lclstrconsts.rseditrecordhint
msgid "Edit"
msgstr ""

#: lclstrconsts.rsentirescope
msgid "Search entire file"
msgstr ""

#: lclstrconsts.rserror
msgctxt "lclstrconsts.rserror"
msgid "Error"
msgstr ""

#: lclstrconsts.rserrorcreatingdevicecontext
msgid "Error creating device context for %s.%s"
msgstr ""

#: lclstrconsts.rserrorinlcl
msgid "ERROR in LCL: "
msgstr ""

#: lclstrconsts.rserroroccurredinataddressframe
msgid "Error occurred in %s at %sAddress %s%s Frame %s"
msgstr ""

#: lclstrconsts.rserrorreadingproperty
msgid "Error reading %s%s%s: %s"
msgstr ""

#: lclstrconsts.rserrorwhilesavingbitmap
msgid "Error while saving bitmap."
msgstr ""

#: lclstrconsts.rsexception
msgid "Exception"
msgstr ""

#: lclstrconsts.rsfddirectorymustexist
msgid "Directory must exist"
msgstr ""

#: lclstrconsts.rsfddirectorynotexist
msgid "The directory \"%s\" does not exist."
msgstr ""

#: lclstrconsts.rsfdfilealreadyexists
msgid "The file \"%s\" already exists. Overwrite ?"
msgstr ""

#: lclstrconsts.rsfdfilemustexist
msgid "File must exist"
msgstr ""

#: lclstrconsts.rsfdfilenotexist
msgid "The file \"%s\" does not exist."
msgstr ""

#: lclstrconsts.rsfdfilereadonly
msgid "The file \"%s\" is not writable."
msgstr ""

#: lclstrconsts.rsfdfilereadonlytitle
msgid "File is not writable"
msgstr ""

#: lclstrconsts.rsfdfilesaveas
msgid "Save file as"
msgstr ""

#: lclstrconsts.rsfdopenfile
msgid "Open existing file"
msgstr ""

#: lclstrconsts.rsfdoverwritefile
msgid "Overwrite file ?"
msgstr ""

#: lclstrconsts.rsfdpathmustexist
msgid "Path must exist"
msgstr ""

#: lclstrconsts.rsfdpathnoexist
msgid "The path \"%s\" does not exist."
msgstr ""

#: lclstrconsts.rsfdselectdirectory
msgid "Select Directory"
msgstr ""

#: lclstrconsts.rsfileinfofilenotfound
msgid "(file not found: \"%s\")"
msgstr ""

#: lclstrconsts.rsfileinformation
msgid "File information"
msgstr ""

#: lclstrconsts.rsfind
msgid "Find"
msgstr ""

#: lclstrconsts.rsfindmore
msgid "Find more"
msgstr ""

#: lclstrconsts.rsfirstrecordhint
msgid "First"
msgstr ""

#: lclstrconsts.rsfixedcolstoobig
msgid "FixedCols can't be >= ColCount"
msgstr ""

#: lclstrconsts.rsfixedrowstoobig
msgid "FixedRows can't be >= RowCount"
msgstr ""

#: lclstrconsts.rsformcolorcaption
msgid "Form"
msgstr ""

#: lclstrconsts.rsformstreamingerror
msgid "Form streaming \"%s\" error: %s"
msgstr ""

#: lclstrconsts.rsforward
msgid "Forward"
msgstr ""

#: lclstrconsts.rsfuchsiacolorcaption
msgid "Fuchsia"
msgstr ""

#: lclstrconsts.rsgdkoptiondebug
msgid "--gdk-debug flags     Turn on specific GDK trace/debug messages."
msgstr ""

#: lclstrconsts.rsgdkoptionnodebug
msgid "--gdk-no-debug flags  Turn off specific GDK trace/debug messages."
msgstr ""

#: lclstrconsts.rsgif
msgid "Graphics Interchange Format"
msgstr ""

#: lclstrconsts.rsgoptionfatalwarnings
msgid "--g-fatal-warnings    Warnings and errors generated by Gtk+/GDK will halt the application."
msgstr ""

#: lclstrconsts.rsgradientactivecaptioncolorcaption
msgid "Gradient Active Caption"
msgstr ""

#: lclstrconsts.rsgradientinactivecaptioncolorcaption
msgid "Gradient Inactive Caption"
msgstr ""

#: lclstrconsts.rsgraphic
msgid "Graphic"
msgstr ""

#: lclstrconsts.rsgraycolorcaption
msgid "Gray"
msgstr ""

#: lclstrconsts.rsgraytextcolorcaption
msgid "Gray Text"
msgstr ""

#: lclstrconsts.rsgreencolorcaption
msgid "Green"
msgstr ""

#: lclstrconsts.rsgridfiledoesnotexists
msgid "Grid file doesn't exists"
msgstr ""

#: lclstrconsts.rsgridindexoutofrange
msgid "Grid index out of range."
msgstr ""

#: lclstrconsts.rsgroupindexcannotbelessthanprevious
msgid "GroupIndex cannot be less than a previous menu item's GroupIndex"
msgstr ""

#: lclstrconsts.rsgtkfilter
msgid "Filter:"
msgstr ""

#: lclstrconsts.rsgtkhistory
msgid "History:"
msgstr ""

#: lclstrconsts.rsgtkoptionclass
msgid "--class classname     Following Xt conventions, the class of a program is the program name with the initial character capitalized. For example, the classname for gimp is \"Gimp\". If --class is specified, the class of the program will be set to \"classname\"."
msgstr ""

#: lclstrconsts.rsgtkoptiondebug
msgid "--gtk-debug flags     Turn on specific Gtk+ trace/debug messages."
msgstr ""

#: lclstrconsts.rsgtkoptiondisplay
msgid "--display h:s:d       Connect to the specified X server, where \"h\" is the hostname, \"s\" is the server number (usually 0), and \"d\" is the display number (typically omitted). If --display is not specified, the DISPLAY environment variable is used."
msgstr ""

#: lclstrconsts.rsgtkoptionmodule
msgid "--gtk-module module   Load the specified module at startup."
msgstr ""

#: lclstrconsts.rsgtkoptionname
msgid "--name programe       Set program name to \"progname\". If not specified, program name will be set to ParamStrUTF8(0)."
msgstr ""

#: lclstrconsts.rsgtkoptionnodebug
msgid "--gtk-no-debug flags  Turn off specific Gtk+ trace/debug messages."
msgstr ""

#: lclstrconsts.rsgtkoptionnotransient
msgid "--lcl-no-transient    Do not set transient order for modal forms"
msgstr ""

#: lclstrconsts.rsgtkoptionnoxshm
msgid "--no-xshm             Disable use of the X Shared Memory Extension."
msgstr ""

#: lclstrconsts.rsgtkoptionsync
msgid "--sync                Call XSynchronize (display, True) after the Xserver connection has been established. This makes debugging X protocol errors easier, because X request buffering will be disabled and X errors will be received immediately after the protocol request that generated the error has been processed by the X server."
msgstr ""

#: lclstrconsts.rshelpalreadyregistered
msgid "%s: Already registered"
msgstr ""

#: lclstrconsts.rshelpcontextnotfound
msgid "Help Context not found"
msgstr ""

#: lclstrconsts.rshelpdatabasenotfound
msgid "Help Database not found"
msgstr ""

#: lclstrconsts.rshelperror
msgid "Help Error"
msgstr ""

#: lclstrconsts.rshelphelpcontextnotfound
msgid "Help context %s not found."
msgstr ""

#: lclstrconsts.rshelphelpcontextnotfoundindatabase
msgid "Help context %s not found in Database %s%s%s."
msgstr ""

#: lclstrconsts.rshelphelpdatabasedidnotfoundaviewerforahelppageoftype
msgid "Help Database %s%s%s did not found a viewer for a help page of type %s"
msgstr ""

#: lclstrconsts.rshelphelpdatabasenotfound
msgid "Help Database %s%s%s not found"
msgstr ""

#: lclstrconsts.rshelphelpkeywordnotfound
msgid "Help keyword %s%s%s not found."
msgstr ""

#: lclstrconsts.rshelphelpkeywordnotfoundindatabase
msgid "Help keyword %s%s%s not found in Database %s%s%s."
msgstr ""

#: lclstrconsts.rshelphelpnodehasnohelpdatabase
msgid "Help node %s%s%s has no Help Database"
msgstr ""

#: lclstrconsts.rshelpnohelpfoundforsource
msgid "No help found for line %d, column %d of %s."
msgstr ""

#: lclstrconsts.rshelpnohelpnodesavailable
msgid "No help nodes available"
msgstr ""

#: lclstrconsts.rshelpnotfound
msgid "Help not found"
msgstr ""

#: lclstrconsts.rshelpnotregistered
msgid "%s: Not registered"
msgstr ""

#: lclstrconsts.rshelpselectorerror
msgid "Help Selector Error"
msgstr ""

#: lclstrconsts.rshelpthereisnoviewerforhelptype
msgid "There is no viewer for help type %s%s%s"
msgstr ""

#: lclstrconsts.rshelpviewererror
msgid "Help Viewer Error"
msgstr ""

#: lclstrconsts.rshelpviewernotfound
msgid "Help Viewer not found"
msgstr ""

#: lclstrconsts.rshighlightcolorcaption
msgid "Highlight"
msgstr ""

#: lclstrconsts.rshighlighttextcolorcaption
msgid "Highlight Text"
msgstr ""

#: lclstrconsts.rshotlightcolorcaption
msgid "Hot Light"
msgstr ""

#: lclstrconsts.rsicns
msgid "Mac OS X Icon"
msgstr ""

#: lclstrconsts.rsicon
msgid "Icon"
msgstr ""

#: lclstrconsts.rsiconimageempty
msgid "Icon image cannot be empty"
msgstr ""

#: lclstrconsts.rsiconimageformat
msgid "Icon image must have the same format"
msgstr ""

#: lclstrconsts.rsiconimageformatchange
msgid "Cannot change format of icon image"
msgstr ""

#: lclstrconsts.rsiconimagesize
msgid "Icon image must have the same size"
msgstr ""

#: lclstrconsts.rsiconimagesizechange
msgid "Cannot change size of icon image"
msgstr ""

#: lclstrconsts.rsiconnocurrent
msgid "Icon has no current image"
msgstr ""

#: lclstrconsts.rsinactivebordercolorcaption
msgid "Inactive Border"
msgstr ""

#: lclstrconsts.rsinactivecaptioncolorcaption
msgctxt "lclstrconsts.rsinactivecaptioncolorcaption"
msgid "Inactive Caption"
msgstr ""

#: lclstrconsts.rsinactivecaptiontext
msgctxt "lclstrconsts.rsinactivecaptiontext"
msgid "Inactive Caption"
msgstr ""

#: lclstrconsts.rsindexoutofbounds
msgid "%s Index %d out of bounds 0 .. %d"
msgstr ""

#: lclstrconsts.rsindexoutofrange
msgid "Index Out of range Cell[Col=%d Row=%d]"
msgstr ""

#: lclstrconsts.rsinfobkcolorcaption
msgid "Info Background"
msgstr ""

#: lclstrconsts.rsinfotextcolorcaption
msgid "Info Text"
msgstr ""

#: lclstrconsts.rsinsertrecordhint
msgctxt "lclstrconsts.rsinsertrecordhint"
msgid "Insert"
msgstr ""

#: lclstrconsts.rsinvaliddate
msgid "Invalid Date : %s"
msgstr ""

#: lclstrconsts.rsinvaliddaterangehint
msgid "Invalid Date: %s. Must be between %s and %s"
msgstr ""

#: lclstrconsts.rsinvalidformobjectstream
msgid "invalid Form object stream"
msgstr ""

#: lclstrconsts.rsinvalidpropertyvalue
msgid "Invalid property value"
msgstr ""

#: lclstrconsts.rsinvalidstreamformat
msgid "Invalid stream format"
msgstr ""

#: lclstrconsts.rsisalreadyassociatedwith
msgid "%s is already associated with %s"
msgstr ""

#: lclstrconsts.rsjpeg
msgid "Joint Picture Expert Group"
msgstr ""

#: lclstrconsts.rslastrecordhint
msgid "Last"
msgstr ""

#: lclstrconsts.rslimecolorcaption
msgid "Lime"
msgstr ""

#: lclstrconsts.rslistindexexceedsbounds
msgid "List index exceeds bounds (%d)"
msgstr ""

#: lclstrconsts.rslistmustbeempty
msgid "List must be empty"
msgstr ""

#: lclstrconsts.rsmarooncolorcaption
msgid "Maroon"
msgstr ""

#: lclstrconsts.rsmbabort
msgid "Abort"
msgstr ""

#: lclstrconsts.rsmball
msgid "&All"
msgstr ""

#: lclstrconsts.rsmbcancel
msgctxt "lclstrconsts.rsmbcancel"
msgid "Cancel"
msgstr ""

#: lclstrconsts.rsmbclose
msgid "&Close"
msgstr ""

#: lclstrconsts.rsmbhelp
msgid "&Help"
msgstr ""

#: lclstrconsts.rsmbignore
msgid "&Ignore"
msgstr ""

#: lclstrconsts.rsmbno
msgid "&No"
msgstr ""

#: lclstrconsts.rsmbnotoall
msgid "No to all"
msgstr ""

#: lclstrconsts.rsmbok
msgid "&OK"
msgstr ""

#: lclstrconsts.rsmbopen
msgid "&Open"
msgstr ""

#: lclstrconsts.rsmbretry
msgid "&Retry"
msgstr ""

#: lclstrconsts.rsmbsave
msgid "&Save"
msgstr ""

#: lclstrconsts.rsmbunlock
msgid "&Unlock"
msgstr ""

#: lclstrconsts.rsmbyes
msgid "&Yes"
msgstr ""

#: lclstrconsts.rsmbyestoall
msgid "Yes to &All"
msgstr ""

#: lclstrconsts.rsmedgraycolorcaption
msgid "Medium Gray"
msgstr ""

#: lclstrconsts.rsmenubarcolorcaption
msgid "Menu Bar"
msgstr ""

#: lclstrconsts.rsmenucolorcaption
msgctxt "lclstrconsts.rsmenucolorcaption"
msgid "Menu"
msgstr ""

#: lclstrconsts.rsmenuhighlightcolorcaption
msgid "Menu Highlight"
msgstr ""

#: lclstrconsts.rsmenutextcolorcaption
msgid "Menu Text"
msgstr ""

#: lclstrconsts.rsmodified
msgid "  modified "
msgstr ""

#: lclstrconsts.rsmoneygreencolorcaption
msgid "Money Green"
msgstr ""

#: lclstrconsts.rsmtauthentication
msgid "Authentication"
msgstr ""

#: lclstrconsts.rsmtconfirmation
msgid "Confirmation"
msgstr ""

#: lclstrconsts.rsmtcustom
msgid "Custom"
msgstr ""

#: lclstrconsts.rsmterror
msgctxt "lclstrconsts.rsmterror"
msgid "Error"
msgstr ""

#: lclstrconsts.rsmtinformation
msgid "Information"
msgstr ""

#: lclstrconsts.rsmtwarning
msgid "Warning"
msgstr ""

#: lclstrconsts.rsnavycolorcaption
msgid "Navy"
msgstr ""

#: lclstrconsts.rsnextrecordhint
msgctxt "lclstrconsts.rsnextrecordhint"
msgid "Next"
msgstr ""

#: lclstrconsts.rsnonecolorcaption
msgid "None"
msgstr ""

#: lclstrconsts.rsnotavalidgridfile
msgid "Not a valid grid file"
msgstr ""

#: lclstrconsts.rsnowidgetset
msgid "No widgetset object. Please check if the unit \"interfaces\" was added to the programs uses clause."
msgstr ""

#: lclstrconsts.rsolivecolorcaption
msgid "Olive"
msgstr ""

#: lclstrconsts.rspickdate
msgid "Select a date"
msgstr ""

#: lclstrconsts.rspixmap
msgid "Pixmap"
msgstr ""

#: lclstrconsts.rsportablebitmap
msgid "Portable BitMap"
msgstr ""

#: lclstrconsts.rsportablegraymap
msgid "Portable GrayMap"
msgstr ""

#: lclstrconsts.rsportablenetworkgraphic
msgid "Portable Network Graphic"
msgstr ""

#: lclstrconsts.rsportablepixmap
msgid "Portable PixMap"
msgstr ""

#: lclstrconsts.rspostrecordhint
msgid "Post"
msgstr ""

#: lclstrconsts.rspressoktoignoreandriskdatacorruptionpresscanceltok
msgid "%s%sPress OK to ignore and risk data corruption.%sPress Cancel to kill the program."
msgstr ""

#: lclstrconsts.rspriorrecordhint
msgctxt "lclstrconsts.rspriorrecordhint"
msgid "Prior"
msgstr ""

#: lclstrconsts.rspropertydoesnotexist
msgid "Property %s does not exist"
msgstr ""

#: lclstrconsts.rspurplecolorcaption
msgid "Purple"
msgstr ""

#: lclstrconsts.rsqtoptiondograb
msgid "-dograb (only under X11), running under a debugger can cause an implicit -nograb, use -dograb to override. Need QT_DEBUG."
msgstr ""

#: lclstrconsts.rsqtoptiongraphicsstyle
msgid "-graphicssystem param, sets the backend to be used for on-screen widgets and QPixmaps. Available options are native, raster and opengl. OpenGL is still unstable."
msgstr ""

#: lclstrconsts.rsqtoptionnograb
msgid "-nograb, tells Qt that it must never grab the mouse or the keyboard. Need QT_DEBUG."
msgstr ""

#: lclstrconsts.rsqtoptionreverse
msgid "-reverse, sets the application's layout direction to Qt::RightToLeft."
msgstr ""

#: lclstrconsts.rsqtoptionsession
msgid "-session session, restores the application from an earlier session."
msgstr ""

#: lclstrconsts.rsqtoptionstyle
msgid "-style style or -style=style, sets the application GUI style. Possible values are motif, windows, and platinum. If you compiled Qt with additional styles or have additional styles as plugins these will be available to the -style  command line option. NOTE: Not all styles are available on all platforms. If style param does not exist Qt will start an application with default common style (windows)."
msgstr ""

#: lclstrconsts.rsqtoptionstylesheet
msgid "-stylesheet stylesheet or -stylesheet=stylesheet, sets the application Style Sheet. The value must be a path to a file that contains the Style Sheet. Note: Relative URLs in the Style Sheet file are relative to the Style Sheet file's path."
msgstr ""

#: lclstrconsts.rsqtoptionsync
msgid "-sync (only under X11), switches to synchronous mode for debugging."
msgstr ""

#: lclstrconsts.rsqtoptionwidgetcount
msgid "-widgetcount, prints debug message at the end about number of widgets left undestroyed and maximum number of widgets existed at the same time."
msgstr ""

#: lclstrconsts.rsqtoptionx11bgcolor
msgid "-bg or -background color, sets the default background color and an application palette (light and dark shades are calculated)."
msgstr ""

#: lclstrconsts.rsqtoptionx11btncolor
msgid "-btn or -button color, sets the default button color."
msgstr ""

#: lclstrconsts.rsqtoptionx11cmap
msgid "-cmap, causes the application to install a private color map on an 8-bit display."
msgstr ""

#: lclstrconsts.rsqtoptionx11display
msgid "-display display, sets the X display (default is $DISPLAY)."
msgstr ""

#: lclstrconsts.rsqtoptionx11fgcolor
msgid "-fg or -foreground color, sets the default foreground color."
msgstr ""

#: lclstrconsts.rsqtoptionx11font
msgid "-fn or -font font, defines the application font. The font should be specified using an X logical font description."
msgstr ""

#: lclstrconsts.rsqtoptionx11geometry
msgid "-geometry geometry, sets the client geometry of the first window that is shown."
msgstr ""

#: lclstrconsts.rsqtoptionx11im
msgid "-im, sets the input method server (equivalent to setting the XMODIFIERS environment variable)."
msgstr ""

#: lclstrconsts.rsqtoptionx11inputstyle
msgid "-inputstyle, defines how the input is inserted into the given widget, e.g. onTheSpot makes the input appear directly in the widget, while overTheSpot makes the input appear in a box floating over the widget and is not inserted until the editing is done."
msgstr ""

#: lclstrconsts.rsqtoptionx11name
msgid "-name name, sets the application name."
msgstr ""

#: lclstrconsts.rsqtoptionx11ncols
msgid "-ncols count, limits the number of colors allocated in the color cube on an 8-bit display, if the application is using the QApplication::ManyColor color specification. If count is 216 then a 6x6x6 color cube is used (i.e. 6 levels of red, 6 of green, and 6 of blue); for other values, a cube approximately proportional to a 2x3x1 cube is used."
msgstr ""

#: lclstrconsts.rsqtoptionx11title
msgid "-title title, sets the application title."
msgstr ""

#: lclstrconsts.rsqtoptionx11visual
msgid "-visual TrueColor, forces the application to use a TrueColor visual on an 8-bit display."
msgstr ""

#: lclstrconsts.rsrasterimageendupdate
msgid "Endupdate while no update in progress"
msgstr ""

#: lclstrconsts.rsrasterimagesaveinupdate
msgid "Cannot save image while update in progress"
msgstr ""

#: lclstrconsts.rsrasterimageupdateall
msgid "Cannot begin update all when canvas only update in progress"
msgstr ""

#: lclstrconsts.rsredcolorcaption
msgid "Red"
msgstr ""

#: lclstrconsts.rsrefreshrecordshint
msgid "Refresh"
msgstr ""

#: lclstrconsts.rsreplace
msgid "Replace"
msgstr ""

#: lclstrconsts.rsreplaceall
msgid "Replace all"
msgstr ""

#: lclstrconsts.rsresourcenotfound
msgctxt "lclstrconsts.rsresourcenotfound"
msgid "Resource %s not found"
msgstr ""

#: lclstrconsts.rsscrollbarcolorcaption
msgid "ScrollBar"
msgstr ""

#: lclstrconsts.rsscrollbaroutofrange
msgid "ScrollBar property out of range"
msgstr ""

#: lclstrconsts.rsselectcolortitle
msgid "Select color"
msgstr ""

#: lclstrconsts.rsselectfonttitle
msgid "Select a font"
msgstr ""

#: lclstrconsts.rssilvercolorcaption
msgid "Silver"
msgstr ""

#: lclstrconsts.rssize
msgid "  size "
msgstr ""

#: lclstrconsts.rsskybluecolorcaption
msgid "Sky Blue"
msgstr ""

#: lclstrconsts.rstealcolorcaption
msgid "Teal"
msgstr ""

#: lclstrconsts.rstext
msgid "Text"
msgstr ""

#: lclstrconsts.rstiff
msgid "Tagged Image File Format"
msgstr ""

#: lclstrconsts.rsunabletoloaddefaultfont
msgid "Unable to load default font"
msgstr ""

#: lclstrconsts.rsunknownerrorpleasereportthisbug
msgid "Unknown Error, please report this bug"
msgstr ""

#: lclstrconsts.rsunknownpictureextension
msgid "Unknown picture extension"
msgstr ""

#: lclstrconsts.rsunknownpictureformat
msgid "Unknown picture format"
msgstr ""

#: lclstrconsts.rsunsupportedbitmapformat
msgid "Unsupported bitmap format."
msgstr ""

#: lclstrconsts.rsunsupportedclipboardformat
msgid "Unsupported clipboard format: %s"
msgstr ""

#: lclstrconsts.rswarningunreleaseddcsdump
msgid " WARNING: There are %d unreleased DCs, a detailed dump follows:"
msgstr ""

#: lclstrconsts.rswarningunreleasedgdiobjectsdump
msgid " WARNING: There are %d unreleased GDIObjects, a detailed dump follows:"
msgstr ""

#: lclstrconsts.rswarningunreleasedmessagesinqueue
msgid " WARNING: There are %d messages left in the queue! I'll free them"
msgstr ""

#: lclstrconsts.rswarningunreleasedtimerinfos
msgid " WARNING: There are %d TimerInfo structures left, I'll free them"
msgstr ""

#: lclstrconsts.rswarningunremovedpaintmessages
msgid " WARNING: There are %s unremoved LM_PAINT/LM_GtkPAINT message links left."
msgstr ""

#: lclstrconsts.rswhitecolorcaption
msgid "White"
msgstr ""

#: lclstrconsts.rswholewordsonly
msgid "Whole words only"
msgstr ""

#: lclstrconsts.rswin32error
msgid "Error:"
msgstr ""

#: lclstrconsts.rswin32warning
msgid "Warning:"
msgstr ""

#: lclstrconsts.rswindowcolorcaption
msgid "Window"
msgstr ""

#: lclstrconsts.rswindowframecolorcaption
msgid "Window Frame"
msgstr ""

#: lclstrconsts.rswindowtextcolorcaption
msgid "Window Text"
msgstr ""

#: lclstrconsts.rsyellowcolorcaption
msgid "Yellow"
msgstr ""

#: lclstrconsts.scannotfocus
msgid "Cannot focus a disabled or invisible window"
msgstr ""

#: lclstrconsts.sduplicatemenus
msgid "Duplicate menus"
msgstr ""

#: lclstrconsts.sinvalidactioncreation
msgid "Invalid action creation"
msgstr ""

#: lclstrconsts.sinvalidactionenumeration
msgid "Invalid action enumeration"
msgstr ""

#: lclstrconsts.sinvalidactionregistration
msgid "Invalid action registration"
msgstr ""

#: lclstrconsts.sinvalidactionunregistration
msgid "Invalid action unregistration"
msgstr ""

#: lclstrconsts.sinvalidcharset
msgid "The char set in mask \"%s\" is not valid!"
msgstr ""

#: lclstrconsts.sinvalidimagesize
msgid "Invalid image size"
msgstr ""

#: lclstrconsts.sinvalidindex
msgid "Invalid ImageList Index"
msgstr ""

#: lclstrconsts.smenuindexerror
msgid "Menu index out of range"
msgstr ""

#: lclstrconsts.smenuitemisnil
msgid "MenuItem is nil"
msgstr ""

#: lclstrconsts.smenunotfound
msgid "Sub-menu is not in menu"
msgstr ""

#: lclstrconsts.smkcalt
msgid "Alt+"
msgstr ""

#: lclstrconsts.smkcbksp
msgid "BkSp"
msgstr ""

#: lclstrconsts.smkcctrl
msgid "Ctrl+"
msgstr ""

#: lclstrconsts.smkcdel
msgid "Del"
msgstr ""

#: lclstrconsts.smkcdown
msgctxt "lclstrconsts.smkcdown"
msgid "Down"
msgstr ""

#: lclstrconsts.smkcend
msgctxt "lclstrconsts.smkcend"
msgid "End"
msgstr ""

#: lclstrconsts.smkcenter
msgid "Enter"
msgstr ""

#: lclstrconsts.smkcesc
msgid "Esc"
msgstr ""

#: lclstrconsts.smkchome
msgctxt "lclstrconsts.smkchome"
msgid "Home"
msgstr ""

#: lclstrconsts.smkcins
msgid "Ins"
msgstr ""

#: lclstrconsts.smkcleft
msgctxt "lclstrconsts.smkcleft"
msgid "Left"
msgstr ""

#: lclstrconsts.smkcmeta
msgid "Meta+"
msgstr ""

#: lclstrconsts.smkcpgdn
msgid "PgDn"
msgstr ""

#: lclstrconsts.smkcpgup
msgid "PgUp"
msgstr ""

#: lclstrconsts.smkcright
msgctxt "lclstrconsts.smkcright"
msgid "Right"
msgstr ""

#: lclstrconsts.smkcshift
msgid "Shift+"
msgstr ""

#: lclstrconsts.smkcspace
msgid "Space"
msgstr ""

#: lclstrconsts.smkctab
msgctxt "lclstrconsts.smkctab"
msgid "Tab"
msgstr ""

#: lclstrconsts.smkcup
msgctxt "lclstrconsts.smkcup"
msgid "Up"
msgstr ""

#: lclstrconsts.snomdiform
msgid "No MDI form present."
msgstr ""

#: lclstrconsts.snotimers
msgid "No timers available"
msgstr ""

#: lclstrconsts.sparexpected
msgid "Wrong token type: %s expected"
msgstr ""

#: lclstrconsts.sparinvalidfloat
msgid "Invalid floating point number: %s"
msgstr ""

#: lclstrconsts.sparinvalidinteger
msgid "Invalid integer number: %s"
msgstr ""

#: lclstrconsts.sparlocinfo
msgid " (at %d,%d, stream offset %.8x)"
msgstr ""

#: lclstrconsts.sparunterminatedbinvalue
msgid "Unterminated byte value"
msgstr ""

#: lclstrconsts.sparunterminatedstring
msgid "Unterminated string"
msgstr ""

#: lclstrconsts.sparwrongtokensymbol
msgid "Wrong token symbol: %s expected but %s found"
msgstr ""

#: lclstrconsts.sparwrongtokentype
msgid "Wrong token type: %s expected but %s found"
msgstr ""

